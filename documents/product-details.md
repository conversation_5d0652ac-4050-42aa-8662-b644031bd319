# Unlimited LLM API System – Implementation Guide (v5, **OpenAI‑Compatible Reference Edition**)

> **What’s new in v5?**
>
> * Integrates an **OpenAI‑compatible REST schema** as the *canonical* request/response contract.
> * Restores & expands any details that appeared in earlier drafts (v1 → v3) but were trimmed in v4.
> * Adds provider‑translation examples (Gemini, Anthropic) showing how OpenAI JSON is mapped.
> * Keeps *all original section numbers* so prior cross‑references remain valid.

---

## 1. Introduction & Purpose

This system exposes **one OpenAI‑compatible endpoint** (`POST /api/v1/llm/proxy`) that lets every user tap multiple provider keys transparently.  The proxy **speaks OpenAI JSON** on the wire and auto‑converts to vendor‑native formats behind the scenes.  A global set of **admin routing rules** and real‑time key health drive smart fallback.

### 1.1 Glossary (restored & expanded)

| Term                 | Meaning                                                                                                                                                                                                                           |
| -------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **System API Key**   | Key we issue to the user; Bearer token for `/api/v1/llm/proxy`.                                                                                                                                                                   |
| **Provider API Key** | Secret key for OpenAI, Gemini, Anthropic, … stored encrypted.                                                                                                                                                                     |
| **Routing Rule**     | Admin‑defined tuple `(identifierType, identifierValue, priority)` governing ordering. Predefined `identifierType` values include `KEY_NAME`, `FORMAT`, `USER_ID`, `API_KEY_ID`, `MODEL_NAME`, and `PROVIDER_FORMAT` (extensible). |
| **Unified Schema**   | The JSON body accepted by the proxy – **identical to OpenAI \*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\***\`\`.                                       |

---

## 2. API Style Consideration (OpenAI‑First)

### 2.1 Canonical Request Example

```http
POST /api/v1/llm/proxy HTTP/1.1
Authorization: Bearer sk‑sys_xxx
Content-Type: application/json

{
  "model": "gpt-4o",
  "messages": [
    {"role": "user", "content": "Tell me a joke about penguins."}
  ],
  "temperature": 0.7,
  "stream": false
}
```

*All optional parameters* (`top_p`, `n`, `stop`, `max_tokens`, `functions`, etc.) are forwarded when supported by the selected provider; otherwise they are ignored with a graceful warning header `X‑LLM‑Proxy‑Warning`.

### 2.2 Canonical Response (non‑stream)

Exactly mirrors OpenAI’s:

```jsonc
{
  "id": "chatcmpl‑abc123",
  "object": "chat.completion",
  "created": **********,
  "model": "gpt-4o",
  "choices": [
    {
      "index": 0,
      "message": {"role": "assistant", "content": "…"},
      "finish_reason": "stop"
    }
  ],
  "usage": {"prompt_tokens": 10, "completion_tokens": 24, "total_tokens": 34}
}
```

### 2.3 Streaming

If `"stream": true`, proxy upgrades to **Server‑Sent Events** exactly like OpenAI.  It also handles providers that use different streaming (e.g., Google Gemini’s chunked JSON) by translating to SSE `data: {"choices": …}` frames.

### 2.4 Error Envelope

Follows OpenAI error object:

```jsonc
{
  "error": {
    "message": "Rate limit reached for OpenAI",
    "type": "rate_limit_reached",
    "param": null,
    "code": 429
  }
}
```

Under the hood we still log using RFC 9457 but surface the familiar shape to clients.

---

## 3. Frontend Design (React 18 + Next.js 14)

The SPA lives in \`\` and uses **Next.js 14 App Router**, Tailwind CSS, and shadcn/ui. State is managed with **Zustand**. Where earlier drafts only hinted at UI structure, this version restores **all four subsections (3.1 → 3.4)** in full detail **and now adds a complete layout draft (3.5)** so engineers can scaffold pages rapidly.

### 3.1 API Key Management Section (User Interface)

**Component Hierarchy**

```
<KeyManagerPage>
 ├─ <KeyTable>          ← lists all keys
 ├─ <KeyFormDialog>     ← create / edit modal
 ├─ <TestKeyButton>     ← fires manual test
 └─ <DeleteKeyButton>
```

| Column            | Description                                                                   |
| ----------------- | ----------------------------------------------------------------------------- |
| **Name**          | Editable alias, e.g., “Personal OpenAI Key”.                                  |
| **Format**        | Enum badge (`OPENAI`, `GEMINI`, `ANTHROPIC`).                                 |
| **Endpoint Site** | Base URL (trimmed to domain in table, full in edit modal).                    |
| **Status**        | Badge: gray = Untested, green = Active, red = Invalid, yellow = Rate‑Limited. |
| **Last Used**     | Timestamp of last successful proxy call (WebSocket updates in real‑time).     |
| **Actions**       | *Edit*, *Test*, *Delete*.                                                     |

`<KeyFormDialog>` uses **react‑hook‑form** + zod schema validation. On submit it calls:

```tsx
await fetch(existing ? `/api/users/me/keys/${existing.id}` : "/api/users/me/keys", {
  method: existing ? "PUT" : "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify(values)
});
```

### 3.2 Routing Priority Management (Admin UI)

* **Drag‑and‑drop list** (dnd‑kit) showing each rule as a chip: `[#1 KeyName=PrimaryKey]`.
* Help panel explains that lower numbers = higher priority in the router engine.
* **Save** issues `PUT /api/admin/routing-rules/reorder` with the ordered ID array.

### 3.3 Manual Key Testing Section

Dropdown to choose a saved key or **Ad‑hoc Test**. In *Ad‑hoc* mode the form collects **Key Value**, **Format** (select: `OPENAI`, `GEMINI`, `ANTHROPIC`, …), and an optional **Endpoint Site** (defaults to the provider’s public URL). Pressing **Test** sends `POST /api/users/me/keys/test` with either `{"keyId":"…"}` for a stored key **or** `{"value":"…","format":"OPENAI","endpointSite":"https://api.openai.com/v1"}` for ad‑hoc, then updates the status row via SWR.

### 3.4 Proxy Endpoint Card

Read‑only card in **Dashboard → API Access** shows endpoint, system key, OpenAI‑compat badge, and QR code.

### 3.5 Page & Layout Drafts (new)

#### 3.5.1 Directory Structure

```
apps/frontend/
└─ app/
   ├─ layout.tsx          ← Root layout (Navbar + Sidebar)
   ├─ page.tsx            ← Dashboard summary
   ├─ keys/
   │   ├─ page.tsx        ← /keys – list & forms
   │   └─ layout.tsx      ← breadcrumb wrapper
   ├─ routing/
   │   └─ page.tsx        ← /routing – admin rules UI
   ├─ api-access/
   │   └─ page.tsx        ← /api-access – endpoint card
   └─ (auth)/             ← Next‑Auth routes (login, register)
```

#### 3.5.2 Wireframe Sketches (ASCII)

**Dashboard /keys**

```
┌───────────────────────────────────────────────────────────────┐
│  Navbar ─  Logo │  User Avatar                       │
├───────────────────────────────────────────────────────────────┤
│ Sidebar            │  Key Table (DataGrid)   [Create New]    │
│  ▸ Dashboard       │─────────────────────────────────────────│
│  ▸ API Keys [●]    │  | Name | Format | Status | Last Used | │
│  ▸ Routing Rules   │  |──────|────────|────────|────────────| │
│  ▸ API Access      │  | My OpenAI | OPENAI | Active | Now  | │
│                    │                                             │
└───────────────────────────────────────────────────────────────┘
```

**/routing (admin)** shows draggable chips in a column:

```
Priority Rules
──────────────
[1] KeyName = PrimaryKey   ☰
[2] Format  = OPENAI       ☰
[3] Format  = GEMINI       ☰
[Save Order]
```

**/api-access** card:

```
┌───────────────────────────────┐
│ Proxy Endpoint               │
├───────────────────────────────┤
│ https://api.<your-domain>/…       │
│ System Key: sk‑sys_xxx [📋]  │
│ [Regenerate]                 │
│ [QR Code 🖶]                 │
└───────────────────────────────┘
```

#### 3.5.3 Layout Components

`layout.tsx` root wraps children with `<Sidebar>` and `<TopNav>` using shadcn/ui **Sheet** for responsive sidebar.

```tsx
export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en">
      <body className="min-h-screen flex bg-muted/40">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <TopNav />
          <main className="p-6 overflow-y-auto">{children}</main>
        </div>
      </body>
    </html>
  );
}
```

> **Dev Tip**: Use **Framer Motion** for sidebar reveal on mobile (`initial={{ x: -250 }}` → animate to 0).

\----------------- | --------------------------------------------------------------------------------------------- | | **Name**          | Editable alias, e.g., “Personal OpenAI Key”.                                                  | | **Format**        | Enum badge (`OPENAI`, `GEMINI`, `ANTHROPIC`).                                         | | **Endpoint Site** | Base URL (trimmed to domain in table, full in edit modal).                                    | | **Status**        | Badge: gray = Untested, green = Active, red = Invalid, yellow = Rate‑Limited.                  | | **Last Used**     | Timestamp of last successful proxy call (WebSocket updates in real‑time).                     | | **Actions**       | *Edit*, *Test*, *Delete*.                                                                     |

`<KeyFormDialog>` uses **react‑hook‑form** + zod schema validation. On submit it calls:

```tsx
await fetch(existing ? `/api/users/me/keys/${existing.id}` : "/api/users/me/keys", {
  method: existing ? "PUT" : "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify(values)
});
```

### 3.2 Routing Priority Management (Admin UI)

* **Drag‑and‑drop list** (dnd‑kit) showing each rule as a chip: `[#1  KeyName=PrimaryKey]`.
* Help panel explains that lower numbers = higher priority in the router engine.
* “Save” issues `PUT /api/admin/routing-rules/reorder` with the new ordered ID array.

### 3.3 Manual Key Testing Section (*restored*)

A dropdown lets the user choose a saved key (or *Ad‑hoc Test* to paste an unsaved key). When **Test** is pressed, the UI POSTs to `/api/users/me/keys/test`:

```jsonc
{
  "keyId": "2241c0b2-f4…"   // or { value, format, endpointSite } for ad‑hoc
}
```

Backend performs a provider‑specific `GET /models` call and returns:

```jsonc
{
  "status": "active",
  "provider": "OPENAI",
  "modelsSample": ["gpt-4o", "gpt-3.5-turbo"],
  "rateLimit": null
}
```

The status column updates via SWR mutation.

**Security note** – We always use backend testing to avoid exposing raw provider keys to the browser.

### 3.4 Proxy Endpoint Card

A read‑only card on **Dashboard → API Access** displays:

| Field               | Example                                                             |
| ------------------- | ------------------------------------------------------------------- |
| **Endpoint**        | `https://api.<your-domain>/api/v1/llm/proxy`                        |
| **Your System Key** | `sk‑sys_tEf2…` (copy + regenerate buttons)                          |
| **Schema**          | Badge “OpenAI compatible” – link to Swagger `/docs#/ChatCompletion` |

A **QR code** (qrcode.react) encodes the endpoint + system key for mobile usage.

\------------------- | ----------------------------------------------------------- | | **Endpoint**        | `https://api.<your-domain>/api/v1/llm/proxy`                     | | **Your System Key** | `sk‑sys_tEf2…` (copy button)                                | | **Schema**          | “OpenAI compatible” badge + link to `/docs#/ChatCompletion` |

---

## 4. Backend Design (NestJS 10 w/ Prisma)

Everything from v4 is retained **plus** OpenAI‑compat specifics and earlier missing bits.

### 4.0 Complete Endpoint Reference

| #                           | HTTP   | Path                                | Auth                  | Role  | Description                                                                                                |
| --------------------------- | ------ | ----------------------------------- | --------------------- | ----- | ---------------------------------------------------------------------------------------------------------- |
| **Auth**                    |        |                                     |                       |       |                                                                                                            |
| 1                           | POST   | `/auth/register`                    | –                     | –     | Register new user (email/password).                                                                        |
| 2                           | POST   | `/auth/login`                       | –                     | –     | Issue JWT + refresh cookie.                                                                                |
| 3                           | POST   | `/auth/refresh`                     | Refresh cookie        | –     | Rotate JWT.                                                                                                |
| 4                           | POST   | `/auth/logout`                      | JWT                   | –     | Invalidate refresh token.                                                                                  |
| **User Info**               |        |                                     |                       |       |                                                                                                            |
| 5                           | GET    | `/auth/me`                          | JWT                   | –     | Current user profile inc. **`systemApiKey`**.                                                              |
| **User‑Scoped Key Configs** |        |                                     |                       |       |                                                                                                            |
| 6                           | GET    | `/api/users/me/keys`                | JWT                   | –     | List keys (masked).                                                                                        |
| 7                           | POST   | `/api/users/me/keys`                | JWT                   | –     | **Body**: `{name, format, endpointSite, apiKey, remark}`.                                                  |
| 8                           | PUT    | `/api/users/me/keys/{keyId}`        | JWT                   | –     | Update attributes or rotate key.                                                                           |
| 9                           | DELETE | `/api/users/me/keys/{keyId}`        | JWT                   | –     | Remove key.                                                                                                |
| 10                          | POST   | `/api/users/me/keys/test`           | JWT                   | –     | Trigger backend test.                                                                                      |
| **Admin Routing Rules**     |        |                                     |                       |       |                                                                                                            |
| 11                          | GET    | `/api/admin/routing-rules`          | JWT                   | admin | List rules ordered by `priorityOrder`.                                                                     |
| 12                          | POST   | `/api/admin/routing-rules`          | JWT                   | admin | **Body**: `{identifierType, identifierVa`Monitoring & Observability (restored items)`lue, priorityOrder}`. |
| 13                          | PUT    | `/api/admin/routing-rules/{ruleId}` | JWT                   | admin | Update a rule.                                                                                             |
| 14                          | DELETE | `/api/admin/routing-rules/{ruleId}` | JWT                   | admin | Delete rule.                                                                                               |
| 15                          | PUT    | `/api/admin/routing-rules/reorder`  | JWT                   | admin | **Body**: `ruleIds[]` drag‑drop order.                                                                     |
| **Proxy & Ops**             |        |                                     |                       |       |                                                                                                            |
| 16                          | POST   | `/api/v1/llm/proxy`                 | Bearer *systemApiKey* | –     | Unified LLM call.                                                                                          |
| 17                          | GET    | `/healthz`                          | –                     | –     | Liveness probe.                                                                                            |
| 18                          | GET    | `/metrics`                          | –                     | –     | Prometheus exporter.                                                                                       |

### 4.1 OpenAI‑Compatible Adapter Layer

**Selector Logic – at a glance**

1. Fetch the **ordered key list** computed from the admin routing rules.
2. Choose the **first key** whose status is `ACTIVE` (or `UNTESTED`).
3. Detect the provider **`format`** (`OPENAI`, `GEMINI`, `ANTHROPIC`, …).
4. Convert the incoming **OpenAI JSON** to the provider’s native JSON **iff** the format is not `OPENAI`.
5. Execute the call and translate the provider response **back** to the OpenAI schema.

| Provider                                                                                                              | Format‑conversion notes                                                                                                               |                                                                                  |
| --------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------- |
| **OPENAI**                                                                                                            | No change – body is forwarded exactly as‑is.                                                                                          |                                                                                  |
| **Google Gemini**                                                                                                     | Join all chat turns into a single `prompt` string (\`"\${role}: \${content}                                                           |                                                                                  |
| "`). Unsupported props (`presence\_penalty`, `logit\_bias`, etc.) are logged to header `X‑LLM‑Proxy‑Dropped-Params\`. |                                                                                                                                       |                                                                                  |
| **Anthropic Claude**                                                                                                  | Target endpoint `/v1/messages`:  • The first `system` message populates the `system` field.  • Remaining turns become \`{role: "user" | "assistant", content}`array.<br>  • Copy`temperature`, `top\_p`, `max\_tokens\`. |
|                                                                                                                       |                                                                                                                                       |                                                                                  |

#### TypeScript Converters

```ts
// Gemini transformer
export function openaiToGemini(body: ChatCompletionReq): GeminiReq {
  return {
    // Gemini’s REST API expects the model name either in the URL
    // (e.g., /v1beta/models/<MODEL>:generateContent) **or** in the body for
    // the chat endpoint. We pass it through so the adapter can decide which
    // style to use when constructing the final request.
    model: body.model,
    prompt: body.messages.map(m => `${m.role}: ${m.content}`).join("
"),
    temperature: body.temperature ?? 1,
    topP: body.top_p ?? 1,
    maxOutputTokens: body.max_tokens ?? 2048,
  };
}
: ${m.content}`).join("
"),
    temperature: body.temperature ?? 1,
    topP: body.top_p ?? 1,
    maxOutputTokens: body.max_tokens ?? 2048,
  };
}

// Claude transformer
export function openaiToClaude(body: ChatCompletionReq): ClaudeReq {
  const firstSystem = body.messages.find(m => m.role === "system");
  const system = firstSystem?.content;
  const messages = body.messages
    .filter(m => m !== firstSystem)
    .map(m => ({ role: m.role === "assistant" ? "assistant" : "user", content: m.content }));
  return {
    model: body.model,
    system,
    messages,
    temperature: body.temperature,
    top_p: body.top_p,
    max_tokens: body.max_tokens,
  };
}
```

> **Summary** – Clients always send OpenAI‑style JSON. The router picks the highest‑priority key, converts the payload to Gemini or Claude (if needed), and converts the provider’s response back to OpenAI shape before replying.

### 4.2 Database Schema (full) Database Schema (full)

Earlier drafts split into two tables; here is the **merged complete** schema:

```prisma
model User {
  id            String   @id @default(uuid())
  email         String   @unique
  passwordHash  String
  systemApiKey  String   @unique @default(cuid())
  role          Role     @default(USER)
  apiKeys       UserApiKey[]
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model UserApiKey {
  id              String   @id @default(uuid())
  userId          String
  name            String
  format          ProviderFormat
  endpointSite    String
  apiKeyCipher    Bytes
  status          KeyStatus @default(UNTESTED)
  coolDownSeconds Int      @default(60)
  rateLimitUntil  DateTime?
  remark          String?
  preferredModel  String?     // user‑selected favourite model for this key
  availableModels Json?       // cached array of provider models [{"id":"gpt-4o","ready":true}, …]
  lastUsedAt      DateTime?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model AdminGlobalRoutingRule {
  id             Int              @id @default(autoincrement())
  identifierType RoutingIdType
  identifierVal  String
  priorityOrder  Int
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
}

model UsageLog {
  id             BigInt   @id @default(autoincrement())
  userId         String
  keyId          String
  model          String   // model actually used (e.g., "gpt-4o", "claude-3-sonnet")
  latencyMs      Int
  tokensIn       Int?
  tokensOut      Int?
  providerStatus Int
  success        Boolean
  createdAt      DateTime @default(now())
}
```

### 4.3 Router Engine – Expanded OpenAI Logic

* If the incoming body specifies `stream: true`, adapters **must** return an Async Iterable; the proxy wraps it as SSE.
* Unsupported provider features are silently ignored but flagged via header `X‑LLM‑Proxy‑Dropped-Params: function_call`.

### 4.3.1 Error Handling & Retry Strategy

The router wraps every provider attempt in a unified `try…catch` block that maps provider‑specific errors to **OpenAI‑style** error objects before returning to the client.

| Provider HTTP / Code                                                     | Error Class          | Proxy Action                                                                           | Status Update on `UserApiKey` |
| ------------------------------------------------------------------------ | -------------------- | -------------------------------------------------------------------------------------- | ----------------------------- |
| `401` / `403`                                                            | `invalid_api_key`    | Mark key **INVALID**, skip to next in priority order.                                  | `status = INVALID`            |
| `429` with header `Retry‑After`                                          | `rate_limit_reached` | Set `rateLimitUntil = now + Retry‑After`, mark **RATE\_LIMITED**, try next key.        | `status = RATE_LIMITED`       |
| `400` provider code `insufficient_quota` or `billing_hard_limit_reached` | `insufficient_quota` | Mark **EXHAUSTED**, notify user via webhook if configured, try next key.               | `status = EXHAUSTED`          |
| `5xx`                                                                    | `provider_error`     | Log to `FailureLog`, try next key; key status unchanged.                               | –                             |
| Network timeout (30 s)                                                   | `timeout`            | Log, try next key; after 3 consecutive timeouts mark **UNSTABLE** for 10 min cooldown. | `status = UNSTABLE`           |
| All keys exhausted                                                       | –                    | Return **503** with OpenAI‑format error `{type:"no_provider_available"}` to client.    | –                             |

**Example client‑visible error (rate limit):**

```jsonc
{
  "error": {
    "message": "Rate limit reached for provider (retry after 20 s)",
    "type": "rate_limit_reached",
    "param": null,
    "code": 429
  }
}
```

**Operational Flow (with Preferred‑Model Logic)**

1. **Select key** → the highest‑priority usable key (status `ACTIVE`/`UNTESTED`).
2. **Build model queue** for that key: first `preferredModel` (if set), then the remaining `availableModels` (ready == true).
3. **Iterate models** within the key:

   * Attempt request with the current model.
   * **On success** → log to `UsageLog` and return.
   * **On handled error** (model not found, model rate‑limited, etc.) → mark that specific model as failed in the `availableModels` cache (flag `ready=false`) and continue to the next model in the queue.
4. **If all models for the key fail** → update the `UserApiKey` row (`status`, `rateLimitUntil`, `coolDownSeconds`, `lastUsedAt`) and write a row to `FailureLog`.
5. **Fallback** to the next key in priority order and repeat steps 2‑4.
6. **If every key+model combination fails** → return **503** `{type:"no_provider_available"}`.

This ensures we first exhaust all viable models on a given key before jumping to the next provider, honoring both **preferred model** and overall provider priority.

**Retry Policy (pseudo‑code)**

```text
for key in orderedKeys:
  if !isUsable(key):
      continue
  try:
      resp = adapter.call(key, request)
      updateUsage(key, resp)
      return resp
  except HandledError as err:
      updateUserApiKeyStatus(key, err)   # sets INVALID / RATE_LIMITED / EXHAUSTED etc.
      logFailure(key, err)
      continue  # try next key
raise ServiceUnavailable("no_provider_available")
```

All failures are appended to `FailureLog` with fields: `userId`, `keyId`, `provider`, `errorType`, `httpStatus`, `message`, `createdAt`. Then a webhook alert is emitted if the key status changed. with fields: `userId`, `keyId`, `provider`, `errorType`, `httpStatus`, `message`, `createdAt`.

---

### 4.4 Key Health Checks & Webhook Alerts (new)

A scheduled cron job (`@nestjs/schedule`) runs once per day (configurable):

1. **Decrypt** each stored provider key.
2. **Ping** the provider’s *`/models`* (or equivalent) endpoint to verify validity.
3. **Update Status** – keys that fail auth are marked **INVALID**; keys that hit rate‑limit windows are marked **RATE\_LIMITED** with a calculated `rateLimitUntil` timestamp. Active keys remain **ACTIVE**.
4. **Emit Webhooks** – If a key’s status **changes** (e.g.
   `ACTIVE → INVALID` or `RATE_LIMITED → ACTIVE`), the service publishes a JSON message to every *enabled* webhook destination.

```jsonc
{
  "event": "key.status.changed",
  "userId": "abc123",
  "keyId": "d41d8cd9…",
  "oldStatus": "ACTIVE",
  "newStatus": "INVALID",
  "checkedAt": "2025-06-03T04:15:22Z"
}
```

### Webhook Destinations

Users (and admins) can register outbound webhooks via the existing REST API:

| HTTP     | Path                          | Body Fields                                      | Notes                                |
| -------- | ----------------------------- | ------------------------------------------------ | ------------------------------------ |
| `POST`   | `/api/users/me/webhooks`      | `{url, type}` where `type ∈ {DISCORD, TELEGRAM}` | Creates a new webhook endpoint.      |
| `GET`    | `/api/users/me/webhooks`      | –                                                | List user’s webhooks (masked token). |
| `DELETE` | `/api/users/me/webhooks/{id}` | –                                                | Remove webhook.                      |

* **Discord** – Use *Incoming Webhook URL*; payload posted as `content: "…"` with an embed if desired.
* **Telegram** – Use Bot API `https://api.telegram.org/bot<token>/sendMessage` with `chat_id` encoded in the URL. The proxy posts `text` and disables web previews.

> Implementation hint: inject a `WebhookService` that supports pluggable adapters (`DiscordAdapter`, `TelegramAdapter`). On failure, the webhook is retried with exponential back‑off up to 3 times then logged to `FailureLog`.

---

## 5. Security Considerations (complete list)

*All points from v4 plus those originally in v1 & v2.*

5. **HTTPS Everywhere** – Enforced via HSTS (max‑age = 31536000; preload).
6. **Input Validation** – `class‑validator` DTO pipes; rejects unknown keys.
7. **Rate Limiting** – `@nestjs/throttler` (`60 req/min/systemApiKey`).
8. **CSP & Headers** – Helmet, `crossOriginOpenerPolicy: same‑origin`.
9. **Dependency Scans** – Dependabot + Snyk weekly.

---

## 6. Deployment & DevOps (full stack)

The **entire delivery pipeline** is defined as code so a new engineer can spin up *dev → staging → prod* with a single `make infra`.

### 6.1 Container Images

| Image                             | Context          | Tagging                | Size     |
| --------------------------------- | ---------------- | ---------------------- | -------- |
| `ghcr.io/<your-org>/llm-backend`  | `apps/backend/`  | Git SHA (`main-<sha>`) | ≈ 140 MB |
| `ghcr.io/<your-org>/llm-frontend` | `apps/frontend/` | Git SHA (`main-<sha>`) | ≈ 90 MB  |

### 6.2 Local Development Local Development

A **Docker Compose** file (`docker-compose.dev.yml`) wires up Postgres, Redis, and ClickHouse for quick starts:

```yaml
services:
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_PASSWORD: postgres
    ports: ["5432:5432"]
  redis:
    image: redis:7-alpine
    ports: ["6379:6379"]
  clickhouse:
    image: clickhouse/clickhouse-server:24.3
    ports: ["8123:8123"]
  backend:
    build: ./apps/backend
    env_file: .env.dev
    depends_on: [db, redis]
    ports: ["3001:3001"]
  frontend:
    build: ./apps/frontend
    env_file: .env.dev
    depends_on: [backend]
    ports: ["3000:3000"]
```

### 6.3 CI Pipeline (GitHub Actions)

`.github/workflows/ci.yaml` combines lint, test, build, push, and chart‑lint:

```yaml
jobs:
  test-build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v3
        with: { version: 8 }
      - run: pnpm install --frozen-lockfile
      - run: pnpm lint && pnpm test
      - run: docker build -t ghcr.io/${{ github.repository }}/llm-backend:${{ github.sha }} apps/backend
      - run: docker build -t ghcr.io/${{ github.repository }}/llm-frontend:${{ github.sha }} apps/frontend
      - name: Push images
        run: |
          echo ${{ secrets.GITHUB_TOKEN }} | docker login ghcr.io -u ${{ github.actor }} --password-stdin
          docker push ghcr.io/${{ github.repository }}/llm-backend:${{ github.sha }}
          docker push ghcr.io/${{ github.repository }}/llm-frontend:${{ github.sha }}
      - name: Lint helm chart
        run: helm lint infra/helm/llm-proxy
```

### 6.4 Continuous Delivery (Argo CD)

* **Application manifests** live in `infra/helm/`.
* Argo CD watches the `release` branch and syncs to the \`\` EKS cluster.
* Rollout strategy uses **blue‑green** via the \[Argo Rollouts] controller with automatic analysis on 95th‑percentile latency.

### 6.5 Infrastructure as Code (Terraform)

```hcl
module "llm_api_cluster" {
  source  = "terraform-aws-modules/eks/aws"
  version = "20.8.6"

  cluster_name    = "llm‑cluster"
  cluster_version = "1.30"
  vpc_id          = module.vpc.vpc_id

  eks_managed_node_groups = {
    default = {
      desired_size = 3
      max_size     = 6
      instance_types = ["t3.medium"]
    }
  }
}

module "database" {
  source = "terraform-aws-modules/rds/aws"
  engine            = "postgres"
  engine_version    = "15.5"
  instance_class    = "db.t3.medium"
  allocated_storage = 50
  name              = "llmdb"
  username          = "postgres"
  password          = var.db_password
}
```

### 6.6 Environment Matrix

| Env         | URL                       | DB                     | CDN        | Notes                 |
| ----------- | ------------------------- | ---------------------- | ---------- | --------------------- |
| **dev**     | `*.dev.<your-domain>`     | Shared Aurora Postgres | –          | Ephemeral preview PRs |
| **staging** | `*.staging.<your-domain>` | Stand‑alone            | Cloudflare | Mirrors prod infra    |
| **prod**    | `api.<your-domain>`       | Multi‑AZ RDS PG        | Cloudflare | 24×7 SLO 99.9%        |

### 6.7 Zero‑Downtime Rollouts

* **Backend** Deployment: `RollingUpdate` maxUnavailable = 0, maxSurge = 1.
* **Frontend** served via Cloudflare Pages; atomic uploads ensure instant switch‑over.

### 6.8 Secrets & Configuration

* All provider keys stored in **AWS Secrets Manager**; synced into K8s via **External Secrets Operator**.
* Application config injected through **SealedSecrets** for non‑secret env vars.

---

## 7. Monitoring & Observability (restored items)&#x20;

* **Jaeger trace example**: `span.name = proxy.call / provider=openai / model=gpt-4o`.
* **Loki log label set**: `{app="llm‑proxy", userId="…", provider="OPENAI"}`.

---

## 8. Tech Stack Overview (consolidated)

| Layer        | Stack                                                       |
| ------------ | ----------------------------------------------------------- |
| **Frontend** | Next.js 14, React 18, TypeScript 5, Tailwind CSS, shadcn/ui |
| **Backend**  | NestJS 10, Prisma 5, PostgreSQL 15, Redis 7, Node 20        |
| **Adapters** | OpenAI, Google Gemini, Anthropic Claude 3                   |
|              |                                                             |

---

## 9. Adapter Registry Map

```ts
export const ADAPTER_REGISTRY: Record<ProviderFormat, ClassType<ILlmAdapter>> = {
  OPENAI: OpenAiAdapter,
  GEMINI: GeminiAdapter,
  ANTHROPIC: AnthropicAdapter,

};
```

---

## 10. Appendix

### 10.1 End‑to‑End Flow (Admin Adds & Tests Key → User Call with Fallback)

````mermaid
sequenceDiagram
  %% ==== Admin adds a provider key ====
  actor A as Admin
  actor U as User App
  participant FE as Frontend
  participant BE as Backend
  participant DB as Database
  participant P1 as Provider#1 (Gemini)
  participant P2 as Provider#2 (OpenAI)

  A->>FE: POST /keys (Gemini key)
  FE->>BE: POST /api/users/me/keys
  BE->>DB: INSERT UserApiKey (status=UNTESTED)

  %% ==== Admin triggers manual test ====
  A->>FE: POST /keys/test (keyId)
  FE->>BE: POST /api/users/me/keys/test
  BE->>P1: GET /models
  P1-->>BE: 200 OK
  BE->>DB: UPDATE key status ACTIVE
  note over A,FE: Key now shown as ACTIVE

  %% ==== User makes a chat request ====
  U->>FE: POST /chat (OpenAI schema)
  FE->>BE: POST /api/v1/llm/proxy (systemKey)
  BE->>DB: SELECT ordered keys (via routing rules)

  loop iterate keys
    BE->>P1: POST /chat (converted to Gemini)
    alt success
      P1-->>BE: 200 OK
      BE->>DB: INSERT UsageLog
      BE-->>FE: 200 OK (OpenAI schema)
      FE-->>U: ChatCompletion
      break
    else rate‑limited
      P1-->>BE: 429 Retry‑After:20
      BE->>DB: UPDATE key status RATE_LIMITED (until +20 s)
      BE->>Webhook: key.status.changed
    end
    BE->>P2: POST /chat (OpenAI request passthrough)
    P2-->>BE: 200 OK
    BE->>DB: INSERT UsageLog
    BE-->>FE: 200 OK (OpenAI schema)
    FE-->>U: ChatCompletion
  end

  alt all keys fail
    BE-->>FE: 503 ServiceUnavailable (no_provider_available)
    FE-->>U: Error JSON
  end
```mermaid
sequenceDiagram
  actor U as User App
  participant FE as Frontend
  participant BE as Backend
  participant DB as Database
  participant OAI as OpenAI

  U->>FE: POST /chat
  FE->>BE: POST /api/v1/llm/proxy (systemKey)
  BE->>DB: SELECT user + keys
  DB-->>BE: keys
  BE->>OAI: POST /chat/completions (providerKey)
  OAI-->>BE: 200 OK
  BE->>DB: INSERT usage_log
  BE-->>FE: 200 OK
  FE-->>U: ChatCompletion
````

### 10.2 Curl Smoke Tests

```bash
# Plain completion
curl -s -H "Authorization: Bearer $SYS_KEY" \
     -H "Content-Type: application/json" \
     -d '{"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Ping"}]}' \
     https://api.<your-domain>/api/v1/llm/proxy | jq .

# Stream mode
curl -N -H "Authorization: Bearer $SYS_KEY" \
     -H "Content-Type: application/json" \
     -d '{"model":"gpt-4o","messages":[{"role":"user","content":"Stream demo"}],"stream":true}' \
     https://api.<your-domain>/api/v1/llm/proxy
```

---

*© 2025 Linux.do Team – Unlimited LLM API System (v5)*
