# Environment Configuration
ENVIRONMENT=development

# Server Configuration
PORT=8080

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/llmproxy?sslmode=disable

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_EXPIRES_IN=24h

# Encryption Configuration (must be exactly 32 characters)
ENCRYPTION_KEY=your-32-character-encryption-key

# Rate Limiting Configuration
RATE_LIMIT_RPM=60

# Provider Configuration
PROVIDER_TIMEOUT_SECONDS=30
PROVIDER_MAX_RETRIES=3

# Webhook Configuration
WEBHOOK_ENABLED=true
WEBHOOK_SECRET=your-webhook-secret

# CORS Configuration
CORS_ALLOWED_ORIGINS=*
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*

# Logging Configuration
LOG_LEVEL=info

# Monitoring Configuration
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true

# Development Configuration
GIN_MODE=debug
