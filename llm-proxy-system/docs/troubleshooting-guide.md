# Troubleshooting Guide

Common issues and solutions for the LLM Proxy System.

## 🚨 Common Issues

### 1. Application Won't Start

#### Database Connection Failed

**Symptoms:**
- Error: "failed to connect to database"
- Application exits immediately
- Database connection timeout

**Solutions:**

1. **Check Database Status**
   ```bash
   # PostgreSQL status
   sudo systemctl status postgresql
   
   # Docker container status
   docker-compose ps postgres
   
   # Test connection
   pg_isready -h localhost -U llmproxy
   ```

2. **Verify Connection String**
   ```bash
   # Check environment variable
   echo $DATABASE_URL
   
   # Test connection manually
   psql "postgresql://llmproxy:password@localhost:5432/llmproxy"
   ```

3. **Common Fixes**
   ```bash
   # Restart PostgreSQL
   sudo systemctl restart postgresql
   
   # Or restart Docker container
   docker-compose restart postgres
   
   # Check firewall rules
   sudo ufw status
   ```

#### Redis Connection Failed

**Symptoms:**
- Error: "failed to connect to redis"
- Cache operations failing
- Session management issues

**Solutions:**

1. **Check Redis Status**
   ```bash
   # Redis status
   sudo systemctl status redis
   
   # Docker container status
   docker-compose ps redis
   
   # Test connection
   redis-cli ping
   ```

2. **Verify Redis Configuration**
   ```bash
   # Check Redis URL
   echo $REDIS_URL
   
   # Test connection with password
   redis-cli -u redis://:password@localhost:6379 ping
   ```

3. **Common Fixes**
   ```bash
   # Restart Redis
   sudo systemctl restart redis
   
   # Or restart Docker container
   docker-compose restart redis
   
   # Check Redis logs
   tail -f /var/log/redis/redis-server.log
   ```

### 2. Authentication Issues

#### JWT Token Invalid

**Symptoms:**
- 401 Unauthorized errors
- Users getting logged out frequently
- "Invalid token" messages

**Solutions:**

1. **Check JWT Configuration**
   ```bash
   # Verify JWT secret is set
   echo $JWT_SECRET
   
   # Check token expiration settings
   grep JWT_EXPIRATION .env
   ```

2. **Debug Token Issues**
   ```bash
   # Decode JWT token (without verification)
   echo "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." | base64 -d
   
   # Check token in browser developer tools
   # Application > Local Storage > auth_token
   ```

3. **Common Fixes**
   - Ensure JWT_SECRET is at least 32 characters
   - Check system clock synchronization
   - Clear browser cache and cookies
   - Regenerate JWT secret if compromised

#### API Key Authentication Failed

**Symptoms:**
- "Invalid API key" errors
- Provider requests failing
- Key status showing as "invalid"

**Solutions:**

1. **Verify API Key**
   ```bash
   # Test OpenAI key directly
   curl https://api.openai.com/v1/models \
     -H "Authorization: Bearer sk-your-key"
   
   # Test through proxy
   curl -X POST http://localhost:8080/api/v1/users/me/keys/1/test \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

2. **Check Key Status**
   - Verify key is marked as "active" in dashboard
   - Check provider account has sufficient credits
   - Ensure key has correct permissions

### 3. Performance Issues

#### Slow Response Times

**Symptoms:**
- Requests taking >5 seconds
- Timeout errors
- High response time metrics

**Solutions:**

1. **Check System Resources**
   ```bash
   # CPU usage
   top
   htop
   
   # Memory usage
   free -h
   
   # Disk I/O
   iostat -x 1
   ```

2. **Database Performance**
   ```sql
   -- Check slow queries
   SELECT query, mean_time, calls
   FROM pg_stat_statements
   ORDER BY mean_time DESC
   LIMIT 10;
   
   -- Check database connections
   SELECT count(*) FROM pg_stat_activity;
   ```

3. **Optimization Steps**
   ```bash
   # Increase database connections
   DATABASE_MAX_CONNECTIONS=200
   
   # Optimize Redis
   redis-cli config set maxmemory-policy allkeys-lru
   
   # Enable connection pooling
   DATABASE_POOL_MAX_OPEN=25
   ```

#### High Memory Usage

**Symptoms:**
- Out of memory errors
- Application crashes
- System becoming unresponsive

**Solutions:**

1. **Monitor Memory Usage**
   ```bash
   # Application memory
   ps aux | grep llm-proxy
   
   # System memory
   free -h
   
   # Docker container memory
   docker stats
   ```

2. **Memory Optimization**
   ```bash
   # Set Go memory limit
   export GOMEMLIMIT=1GiB
   
   # Tune garbage collector
   export GOGC=100
   
   # Limit Docker container memory
   docker run --memory=1g llm-proxy-backend
   ```

### 4. Provider Issues

#### Provider API Failures

**Symptoms:**
- "Provider unavailable" errors
- High error rates for specific providers
- Inconsistent responses

**Solutions:**

1. **Check Provider Status**
   ```bash
   # OpenAI status
   curl https://status.openai.com/api/v2/status.json
   
   # Test provider directly
   curl https://api.openai.com/v1/models \
     -H "Authorization: Bearer sk-your-key"
   ```

2. **Provider Configuration**
   ```bash
   # Check provider settings
   curl -H "Authorization: Bearer ADMIN_TOKEN" \
     http://localhost:8080/api/v1/admin/providers
   
   # Update provider timeout
   PROVIDER_TIMEOUT_SECONDS=60
   ```

3. **Fallback Configuration**
   ```bash
   # Enable fallback
   FALLBACK_ENABLED=true
   
   # Set provider priorities
   curl -X PUT -H "Authorization: Bearer ADMIN_TOKEN" \
     http://localhost:8080/api/v1/admin/routing/priorities \
     -d '{"openai": 1, "gemini": 2, "claude": 3}'
   ```

### 5. Frontend Issues

#### Frontend Won't Load

**Symptoms:**
- Blank page
- JavaScript errors in console
- Build failures

**Solutions:**

1. **Check Build Process**
   ```bash
   cd frontend
   
   # Clear cache
   npm cache clean --force
   
   # Reinstall dependencies
   rm -rf node_modules package-lock.json
   npm install
   
   # Rebuild
   npm run build
   ```

2. **Environment Configuration**
   ```bash
   # Check API URL
   echo $REACT_APP_API_URL
   
   # Verify backend connectivity
   curl http://localhost:8080/healthz
   ```

3. **Browser Issues**
   - Clear browser cache
   - Disable browser extensions
   - Check browser console for errors
   - Try incognito/private mode

#### API Connection Issues

**Symptoms:**
- Network errors in browser
- CORS errors
- Failed API requests

**Solutions:**

1. **CORS Configuration**
   ```bash
   # Check CORS settings
   CORS_ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
   
   # Test CORS
   curl -H "Origin: http://localhost:3000" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS http://localhost:8080/api/v1/auth/login
   ```

2. **Network Debugging**
   ```bash
   # Check network connectivity
   curl -v http://localhost:8080/healthz
   
   # Test from frontend container
   docker exec frontend-container curl backend:8080/healthz
   ```

## 🔧 Debugging Tools

### Log Analysis

#### Application Logs
```bash
# View real-time logs
tail -f /var/log/llm-proxy/app.log

# Search for errors
grep "ERROR" /var/log/llm-proxy/app.log | tail -20

# Filter by user
grep "user_id=123" /var/log/llm-proxy/app.log

# JSON log parsing
cat /var/log/llm-proxy/app.log | jq 'select(.level == "error")'
```

#### Docker Logs
```bash
# Container logs
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres

# Filter logs
docker-compose logs backend | grep ERROR

# Follow specific service
docker logs -f llm-proxy-backend
```

### Health Checks

#### System Health
```bash
# Application health
curl http://localhost:8080/healthz

# Database health
pg_isready -h localhost -U llmproxy

# Redis health
redis-cli ping

# Comprehensive health check
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  http://localhost:8080/api/v1/admin/system/health
```

#### Service Monitoring
```bash
# Check service status
systemctl status llm-proxy
systemctl status postgresql
systemctl status redis

# Docker service status
docker-compose ps

# Process monitoring
ps aux | grep llm-proxy
```

### Performance Monitoring

#### System Metrics
```bash
# CPU and memory
htop

# Disk usage
df -h
du -sh /var/lib/postgresql/data

# Network connections
netstat -tulpn | grep :8080
ss -tulpn | grep :8080
```

#### Application Metrics
```bash
# Get system metrics
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  http://localhost:8080/api/v1/admin/monitoring/system

# Get business metrics
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  http://localhost:8080/api/v1/admin/monitoring/business
```

## 🚨 Emergency Procedures

### Service Recovery

#### Quick Restart
```bash
# Docker Compose
docker-compose restart

# Systemd services
sudo systemctl restart llm-proxy
sudo systemctl restart postgresql
sudo systemctl restart redis

# Full system restart
sudo reboot
```

#### Database Recovery
```bash
# Check database integrity
sudo -u postgres psql llmproxy -c "SELECT pg_database_size('llmproxy');"

# Vacuum database
sudo -u postgres psql llmproxy -c "VACUUM ANALYZE;"

# Restore from backup
gunzip -c /backups/latest_backup.sql.gz | sudo -u postgres psql llmproxy
```

### Data Recovery

#### Configuration Backup
```bash
# Backup current configuration
tar -czf config_backup_$(date +%Y%m%d).tar.gz \
  .env \
  configs/ \
  docker-compose.yml

# Restore configuration
tar -xzf config_backup_20240115.tar.gz
```

#### Database Backup
```bash
# Create emergency backup
sudo -u postgres pg_dump llmproxy | gzip > emergency_backup_$(date +%Y%m%d_%H%M%S).sql.gz

# Restore specific table
sudo -u postgres pg_restore -t users emergency_backup.sql.gz
```

## 📞 Getting Help

### Support Channels

1. **Documentation**: Check relevant documentation sections
2. **GitHub Issues**: Search existing issues or create new one
3. **Community Forum**: Ask questions in community discussions
4. **Support Email**: <EMAIL> for urgent issues

### Information to Include

When reporting issues, include:

1. **System Information**
   ```bash
   # System details
   uname -a
   docker --version
   go version
   node --version
   
   # Application version
   curl http://localhost:8080/healthz | jq .version
   ```

2. **Error Messages**
   - Complete error messages
   - Stack traces
   - Log excerpts

3. **Configuration**
   - Environment variables (sanitized)
   - Configuration files
   - Docker compose files

4. **Steps to Reproduce**
   - Detailed steps
   - Expected vs actual behavior
   - Screenshots if applicable

### Log Collection Script

```bash
#!/bin/bash
# collect_logs.sh

TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_DIR="logs_$TIMESTAMP"

mkdir -p $LOG_DIR

# System information
uname -a > $LOG_DIR/system_info.txt
docker --version >> $LOG_DIR/system_info.txt
docker-compose --version >> $LOG_DIR/system_info.txt

# Application logs
cp /var/log/llm-proxy/app.log $LOG_DIR/
docker-compose logs > $LOG_DIR/docker_logs.txt

# Configuration (sanitized)
cp .env $LOG_DIR/env_sanitized.txt
sed -i 's/PASSWORD=.*/PASSWORD=***REDACTED***/g' $LOG_DIR/env_sanitized.txt
sed -i 's/SECRET=.*/SECRET=***REDACTED***/g' $LOG_DIR/env_sanitized.txt

# System status
systemctl status llm-proxy > $LOG_DIR/service_status.txt
docker-compose ps > $LOG_DIR/container_status.txt

# Create archive
tar -czf logs_$TIMESTAMP.tar.gz $LOG_DIR
rm -rf $LOG_DIR

echo "Logs collected in logs_$TIMESTAMP.tar.gz"
```

This troubleshooting guide covers the most common issues and provides systematic approaches to diagnosing and resolving problems in the LLM Proxy System.
