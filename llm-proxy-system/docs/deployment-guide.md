# Deployment Guide

Complete guide for deploying the LLM Proxy System to production environments.

## 🚀 Deployment Options

### 1. Docker Compose (Recommended)
### 2. Kubernetes
### 3. Manual Deployment
### 4. Cloud Platforms (AWS, GCP, Azure)

## 📋 Prerequisites

### System Requirements
- **CPU**: 2+ cores (4+ recommended for production)
- **Memory**: 4GB RAM minimum (8GB+ recommended)
- **Storage**: 20GB+ available disk space
- **Network**: HTTPS/TLS certificate for production

### Software Dependencies
- Docker 20.10+ and Docker Compose 2.0+
- PostgreSQL 13+ (if not using Docker)
- Redis 6+ (if not using Docker)
- Nginx or similar reverse proxy (for production)

## 🐳 Docker Compose Deployment

### Production Docker Compose Setup

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: llmproxy
      POSTGRES_USER: llmproxy
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U llmproxy"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    environment:
      - DATABASE_URL=postgresql://llmproxy:${POSTGRES_PASSWORD}@postgres:5432/llmproxy
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - GIN_MODE=release
      - PORT=8080
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    environment:
      - REACT_APP_API_URL=https://api.yourdomain.com
    ports:
      - "3000:3000"
    depends_on:
      - backend
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### Environment Configuration

Create `.env.prod`:

```bash
# Database
POSTGRES_PASSWORD=your_secure_postgres_password

# Redis
REDIS_PASSWORD=your_secure_redis_password

# Security
JWT_SECRET=your_jwt_secret_key_minimum_32_characters
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Domain
DOMAIN=yourdomain.com
API_DOMAIN=api.yourdomain.com

# SSL
SSL_EMAIL=<EMAIL>
```

### Deployment Steps

1. **Prepare the server:**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker and Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

2. **Clone and configure:**
```bash
# Clone repository
git clone https://github.com/your-org/llm-proxy-system.git
cd llm-proxy-system

# Setup environment
cp .env.prod .env
# Edit .env with your actual values

# Setup SSL certificates (Let's Encrypt)
sudo apt install certbot
sudo certbot certonly --standalone -d yourdomain.com -d api.yourdomain.com
```

3. **Deploy:**
```bash
# Build and start services
docker-compose -f docker-compose.prod.yml up -d

# Check status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f
```

## ☸️ Kubernetes Deployment

### Kubernetes Manifests

Create `k8s/namespace.yaml`:

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: llm-proxy
```

Create `k8s/configmap.yaml`:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: llm-proxy-config
  namespace: llm-proxy
data:
  GIN_MODE: "release"
  PORT: "8080"
  ENVIRONMENT: "production"
```

Create `k8s/secret.yaml`:

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: llm-proxy-secrets
  namespace: llm-proxy
type: Opaque
stringData:
  DATABASE_URL: "****************************************/llmproxy"
  REDIS_URL: "redis://:password@redis:6379"
  JWT_SECRET: "your-jwt-secret"
  ENCRYPTION_KEY: "your-32-character-encryption-key"
```

Create `k8s/postgres.yaml`:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: llm-proxy
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        env:
        - name: POSTGRES_DB
          value: llmproxy
        - name: POSTGRES_USER
          value: llmproxy
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: llm-proxy
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
```

### Deployment Commands

```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -n llm-proxy

# View logs
kubectl logs -f deployment/llm-proxy-backend -n llm-proxy

# Scale deployment
kubectl scale deployment llm-proxy-backend --replicas=3 -n llm-proxy
```

## 🔧 Manual Deployment

### Server Setup

1. **Install dependencies:**
```bash
# Install Go
wget https://go.dev/dl/go1.21.5.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.21.5.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PostgreSQL
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Install Redis
sudo apt install redis-server
sudo systemctl start redis
sudo systemctl enable redis
```

2. **Database setup:**
```bash
# Create database and user
sudo -u postgres psql
CREATE DATABASE llmproxy;
CREATE USER llmproxy WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE llmproxy TO llmproxy;
\q
```

3. **Application setup:**
```bash
# Clone repository
git clone https://github.com/your-org/llm-proxy-system.git
cd llm-proxy-system

# Backend setup
go mod download
cp .env.example .env
# Edit .env with your configuration

# Build backend
go build -o bin/server cmd/server/main.go

# Frontend setup
cd frontend
npm install
npm run build
cd ..

# Create systemd service
sudo cp scripts/llm-proxy.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable llm-proxy
sudo systemctl start llm-proxy
```

## 🌐 Nginx Configuration

Create `/etc/nginx/sites-available/llm-proxy`:

```nginx
# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

# Backend upstream
upstream backend {
    server 127.0.0.1:8080;
    # Add more servers for load balancing
    # server 127.0.0.1:8081;
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name yourdomain.com api.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

# Frontend
server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Frontend static files
    location / {
        root /var/www/llm-proxy/frontend/build;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}

# Backend API
server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/api.yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.yourdomain.com/privkey.pem;
    
    # SSL configuration (same as above)
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # API endpoints
    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Rate limiting
        limit_req zone=api burst=20 nodelay;
    }

    # Auth endpoints with stricter rate limiting
    location /api/v1/auth/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        limit_req zone=auth burst=10 nodelay;
    }

    # Health check (no rate limiting)
    location /healthz {
        proxy_pass http://backend;
        access_log off;
    }
}
```

## 📊 Monitoring Setup

### Prometheus Configuration

Create `prometheus.yml`:

```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'llm-proxy'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

### Grafana Dashboard

Import the provided Grafana dashboard from `monitoring/grafana-dashboard.json`.

## 🔒 Security Configuration

### SSL/TLS Setup

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificates
sudo certbot --nginx -d yourdomain.com -d api.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Firewall Configuration

```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

## 🔄 Database Migrations

### Initial Setup

```bash
# Run migrations
go run cmd/migrate/main.go

# Create admin user
go run cmd/admin/main.go create-user --email <EMAIL> --password admin123 --role admin
```

### Backup and Restore

```bash
# Backup
pg_dump -h localhost -U llmproxy llmproxy > backup.sql

# Restore
psql -h localhost -U llmproxy llmproxy < backup.sql
```

## 📈 Performance Tuning

### Database Optimization

```sql
-- PostgreSQL configuration
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
SELECT pg_reload_conf();
```

### Redis Optimization

```bash
# Redis configuration
echo 'maxmemory 512mb' >> /etc/redis/redis.conf
echo 'maxmemory-policy allkeys-lru' >> /etc/redis/redis.conf
sudo systemctl restart redis
```

## 🚨 Troubleshooting

### Common Issues

1. **Database connection failed:**
   - Check PostgreSQL service status
   - Verify connection string
   - Check firewall rules

2. **Redis connection failed:**
   - Check Redis service status
   - Verify Redis password
   - Check network connectivity

3. **SSL certificate issues:**
   - Verify domain DNS records
   - Check certificate expiration
   - Validate certificate chain

### Log Locations

```bash
# Application logs
journalctl -u llm-proxy -f

# Nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# PostgreSQL logs
tail -f /var/log/postgresql/postgresql-15-main.log
```

## 🔄 Updates and Maintenance

### Application Updates

```bash
# Pull latest code
git pull origin main

# Rebuild and restart
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# Or for manual deployment
go build -o bin/server cmd/server/main.go
sudo systemctl restart llm-proxy
```

### Database Maintenance

```bash
# Vacuum and analyze
psql -h localhost -U llmproxy llmproxy -c "VACUUM ANALYZE;"

# Check database size
psql -h localhost -U llmproxy llmproxy -c "SELECT pg_size_pretty(pg_database_size('llmproxy'));"
```

This deployment guide provides comprehensive instructions for deploying the LLM Proxy System in various environments with proper security, monitoring, and maintenance procedures.
