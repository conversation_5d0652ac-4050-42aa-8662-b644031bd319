# Developer Guide

Complete guide for developers working on the LLM Proxy System.

## 🚀 Development Setup

### Prerequisites

- **Go 1.21+**: Backend development
- **Node.js 18+**: Frontend development
- **PostgreSQL 13+**: Database
- **Redis 6+**: Caching
- **Git**: Version control
- **Docker**: Containerization (optional)

### Local Development Environment

#### 1. Clone Repository
```bash
git clone https://github.com/your-org/llm-proxy-system.git
cd llm-proxy-system
```

#### 2. Backend Setup
```bash
# Install Go dependencies
go mod download

# Copy environment file
cp .env.example .env

# Edit .env with local settings
DATABASE_URL=postgresql://llmproxy:password@localhost:5432/llmproxy_dev?sslmode=disable
REDIS_URL=redis://localhost:6379
JWT_SECRET=dev_jwt_secret_key_minimum_32_characters
ENCRYPTION_KEY=dev_encryption_key_32_characters_
```

#### 3. Database Setup
```bash
# Create database
createdb llmproxy_dev

# Run migrations
go run cmd/migrate/main.go

# Create test user
go run cmd/admin/main.go create-user \
  --email <EMAIL> \
  --password dev123 \
  --role admin
```

#### 4. Frontend Setup
```bash
cd frontend
npm install
cp .env.example .env.local

# Edit .env.local
REACT_APP_API_URL=http://localhost:8080
```

#### 5. Start Development Servers
```bash
# Terminal 1: Backend
go run cmd/server/main.go

# Terminal 2: Frontend
cd frontend
npm start
```

### Docker Development Setup

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f backend

# Access services
# Backend: http://localhost:8080
# Frontend: http://localhost:3000
# Database: localhost:5432
# Redis: localhost:6379
```

## 🏗️ Architecture Overview

### Project Structure

```
llm-proxy-system/
├── cmd/                    # Application entry points
│   ├── server/            # Main server application
│   ├── migrate/           # Database migration tool
│   └── admin/             # Admin CLI tool
├── internal/              # Private application code
│   ├── adapters/          # LLM provider adapters
│   ├── admin/             # Admin functionality
│   ├── auth/              # Authentication system
│   ├── config/            # Configuration management
│   ├── database/          # Database models and migrations
│   ├── handlers/          # HTTP handlers
│   ├── keys/              # API key management
│   ├── logger/            # Logging utilities
│   ├── monitoring/        # Monitoring and analytics
│   ├── proxy/             # LLM proxy service
│   ├── router/            # HTTP routing
│   ├── routing/           # Provider routing logic
│   ├── services/          # Business logic services
│   ├── utils/             # Utility functions
│   └── webhooks/          # Webhook system
├── frontend/              # React frontend application
├── docs/                  # Documentation
├── scripts/               # Deployment and utility scripts
├── configs/               # Configuration files
└── tests/                 # Test files
```

### Module Architecture

The system is built with 12 modules:

1. **Core Infrastructure** (`internal/config`, `internal/logger`, `internal/utils`)
2. **Database Layer** (`internal/database`)
3. **Authentication System** (`internal/auth`)
4. **Adapter Integration** (`internal/adapters`)
5. **Routing Engine** (`internal/routing`)
6. **Proxy Service** (`internal/proxy`)
7. **Key Management** (`internal/keys`)
8. **Admin Dashboard** (`internal/admin`)
9. **Webhook System** (`internal/webhooks`)
10. **Frontend Application** (`frontend/`)
11. **Monitoring & Analytics** (`internal/monitoring`)
12. **Documentation & Deployment** (`docs/`, deployment configs)

## 🔧 Development Workflow

### Code Organization

#### Backend (Go)

**Service Layer Pattern:**
```go
// Service interface
type Service interface {
    CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error)
    GetUser(ctx context.Context, id uint) (*User, error)
}

// Service implementation
type service struct {
    db     *gorm.DB
    logger *logger.Logger
}

// Handler layer
func (h *Handler) CreateUser(c *gin.Context) {
    // Request validation
    // Call service
    // Return response
}
```

**Repository Pattern:**
```go
type Repository interface {
    Create(ctx context.Context, user *User) error
    GetByID(ctx context.Context, id uint) (*User, error)
}
```

#### Frontend (React)

**Component Structure:**
```
src/
├── components/           # Reusable UI components
├── pages/               # Page-level components
├── contexts/            # React contexts
├── hooks/               # Custom hooks
├── services/            # API services
├── utils/               # Utility functions
└── types/               # TypeScript types
```

**Component Pattern:**
```typescript
// Page component
const APIKeysPage: React.FC = () => {
  const [keys, setKeys] = useState<APIKey[]>([]);
  
  // Component logic
  
  return (
    <div>
      {/* Component JSX */}
    </div>
  );
};
```

### Development Standards

#### Go Code Standards

1. **Package Naming**: Use lowercase, single words
2. **Function Naming**: Use camelCase, start with uppercase for exported
3. **Error Handling**: Always handle errors explicitly
4. **Context Usage**: Pass context as first parameter
5. **Logging**: Use structured logging with fields

```go
// Good example
func (s *Service) CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error) {
    logger.Info("Creating user", "email", req.Email)
    
    user := &User{
        Email: req.Email,
        Role:  database.RoleUser,
    }
    
    if err := s.db.WithContext(ctx).Create(user).Error; err != nil {
        logger.Error("Failed to create user", "error", err)
        return nil, fmt.Errorf("failed to create user: %w", err)
    }
    
    return user, nil
}
```

#### React Code Standards

1. **Component Naming**: Use PascalCase
2. **File Naming**: Use PascalCase for components, camelCase for utilities
3. **Props Interface**: Define TypeScript interfaces for props
4. **State Management**: Use useState and useContext appropriately
5. **Error Handling**: Implement error boundaries and proper error states

```typescript
// Good example
interface APIKeyCardProps {
  apiKey: APIKey;
  onUpdate: (key: APIKey) => void;
  onDelete: (id: number) => void;
}

const APIKeyCard: React.FC<APIKeyCardProps> = ({ apiKey, onUpdate, onDelete }) => {
  const [isLoading, setIsLoading] = useState(false);
  
  const handleTest = async () => {
    setIsLoading(true);
    try {
      const result = await apiService.testAPIKey(apiKey.id);
      onUpdate(result);
      toast.success('API key tested successfully');
    } catch (error) {
      toast.error('API key test failed');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="card">
      {/* Component content */}
    </div>
  );
};
```

## 🧪 Testing

### Backend Testing

#### Unit Tests
```go
func TestUserService_CreateUser(t *testing.T) {
    // Setup test database
    db := setupTestDB(t)
    service := NewService(db)
    
    // Test data
    req := &CreateUserRequest{
        Email:    "<EMAIL>",
        Password: "password123",
    }
    
    // Execute
    user, err := service.CreateUser(context.Background(), req)
    
    // Assert
    assert.NoError(t, err)
    assert.Equal(t, req.Email, user.Email)
    assert.NotEmpty(t, user.ID)
}
```

#### Integration Tests
```go
func TestAPIKeyHandler_CreateKey(t *testing.T) {
    // Setup test server
    router := setupTestRouter(t)
    
    // Test request
    body := `{"provider": "openai", "name": "Test Key", "api_key": "sk-test"}`
    req := httptest.NewRequest("POST", "/api/v1/users/me/keys", strings.NewReader(body))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer "+testToken)
    
    // Execute
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    // Assert
    assert.Equal(t, http.StatusCreated, w.Code)
}
```

#### Running Tests
```bash
# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run specific package tests
go test ./internal/auth

# Run tests with verbose output
go test -v ./...
```

### Frontend Testing

#### Component Tests
```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { APIKeyCard } from './APIKeyCard';

describe('APIKeyCard', () => {
  const mockAPIKey = {
    id: 1,
    provider: 'openai',
    name: 'Test Key',
    status: 'active',
    masked_api_key: 'sk-...abc123',
  };

  test('renders API key information', () => {
    render(
      <APIKeyCard 
        apiKey={mockAPIKey} 
        onUpdate={jest.fn()} 
        onDelete={jest.fn()} 
      />
    );
    
    expect(screen.getByText('Test Key')).toBeInTheDocument();
    expect(screen.getByText('openai')).toBeInTheDocument();
  });

  test('calls onDelete when delete button clicked', async () => {
    const onDelete = jest.fn();
    render(
      <APIKeyCard 
        apiKey={mockAPIKey} 
        onUpdate={jest.fn()} 
        onDelete={onDelete} 
      />
    );
    
    fireEvent.click(screen.getByText('Delete'));
    
    await waitFor(() => {
      expect(onDelete).toHaveBeenCalledWith(1);
    });
  });
});
```

#### Running Frontend Tests
```bash
cd frontend

# Run all tests
npm test

# Run tests with coverage
npm test -- --coverage

# Run tests in watch mode
npm test -- --watch
```

## 🔌 API Development

### Adding New Endpoints

#### 1. Define Handler
```go
// internal/handlers/new_feature.go
func (h *Handler) CreateFeature(c *gin.Context) {
    var req CreateFeatureRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
        return
    }
    
    feature, err := h.service.CreateFeature(c.Request.Context(), &req)
    if err != nil {
        utils.InternalErrorResponse(c, "Failed to create feature")
        return
    }
    
    utils.CreatedResponse(c, feature)
}
```

#### 2. Add Route
```go
// internal/router/router.go
func setupRoutes(r *gin.Engine, services *services.Container) {
    api := r.Group("/api/v1")
    
    // Add new route
    api.POST("/features", authMiddleware(), handler.CreateFeature)
}
```

#### 3. Add Service Logic
```go
// internal/services/feature_service.go
func (s *FeatureService) CreateFeature(ctx context.Context, req *CreateFeatureRequest) (*Feature, error) {
    feature := &Feature{
        Name:        req.Name,
        Description: req.Description,
        UserID:      getUserIDFromContext(ctx),
    }
    
    if err := s.db.WithContext(ctx).Create(feature).Error; err != nil {
        return nil, fmt.Errorf("failed to create feature: %w", err)
    }
    
    return feature, nil
}
```

### Adding New LLM Providers

#### 1. Create Adapter
```go
// internal/adapters/new_provider.go
type NewProviderAdapter struct {
    baseURL string
    client  *http.Client
}

func (a *NewProviderAdapter) ChatCompletion(ctx context.Context, req *ChatCompletionRequest) (*ChatCompletionResponse, error) {
    // Convert request to provider format
    providerReq := a.convertRequest(req)
    
    // Make API call
    resp, err := a.makeRequest(ctx, providerReq)
    if err != nil {
        return nil, err
    }
    
    // Convert response to standard format
    return a.convertResponse(resp), nil
}
```

#### 2. Register Adapter
```go
// internal/adapters/registry.go
func init() {
    RegisterAdapter("new_provider", func(config AdapterConfig) Adapter {
        return &NewProviderAdapter{
            baseURL: config.BaseURL,
            client:  &http.Client{Timeout: config.Timeout},
        }
    })
}
```

## 🔄 Database Migrations

### Creating Migrations

```go
// migrations/001_create_new_table.go
func up_001_create_new_table(tx *gorm.DB) error {
    return tx.AutoMigrate(&NewTable{})
}

func down_001_create_new_table(tx *gorm.DB) error {
    return tx.Migrator().DropTable(&NewTable{})
}
```

### Running Migrations

```bash
# Run migrations
go run cmd/migrate/main.go

# Rollback migration
go run cmd/migrate/main.go --rollback

# Check migration status
go run cmd/migrate/main.go --status
```

## 📦 Building and Deployment

### Building the Application

#### Backend Build
```bash
# Build for current platform
go build -o bin/server cmd/server/main.go

# Build for Linux (production)
GOOS=linux GOARCH=amd64 go build -o bin/server-linux cmd/server/main.go

# Build with optimizations
go build -ldflags="-s -w" -o bin/server cmd/server/main.go
```

#### Frontend Build
```bash
cd frontend

# Production build
npm run build

# Build with specific environment
REACT_APP_API_URL=https://api.yourdomain.com npm run build
```

### Docker Build

```bash
# Build backend image
docker build -t llm-proxy-backend .

# Build frontend image
docker build -t llm-proxy-frontend ./frontend

# Build with specific tag
docker build -t llm-proxy-backend:v1.0.0 .
```

## 🔍 Debugging

### Backend Debugging

#### Using Delve Debugger
```bash
# Install delve
go install github.com/go-delve/delve/cmd/dlv@latest

# Debug application
dlv debug cmd/server/main.go

# Debug with arguments
dlv debug cmd/server/main.go -- --config=dev.yaml
```

#### Logging and Tracing
```go
// Add debug logging
logger.Debug("Processing request", 
    "user_id", userID, 
    "provider", provider,
    "model", model)

// Add request tracing
ctx = context.WithValue(ctx, "request_id", generateRequestID())
```

### Frontend Debugging

#### React Developer Tools
- Install React Developer Tools browser extension
- Use Components and Profiler tabs for debugging

#### Debug Logging
```typescript
// Add debug logging
console.log('API Key created:', apiKey);
console.debug('Request payload:', payload);

// Use React DevTools
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info:', debugData);
}
```

## 🤝 Contributing

### Pull Request Process

1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/new-feature`
3. **Make changes** following coding standards
4. **Add tests** for new functionality
5. **Run tests**: `go test ./...` and `npm test`
6. **Update documentation** if needed
7. **Commit changes**: Use conventional commit messages
8. **Push branch**: `git push origin feature/new-feature`
9. **Create pull request** with detailed description

### Commit Message Format

```
type(scope): description

[optional body]

[optional footer]
```

Examples:
```
feat(auth): add JWT refresh token support
fix(proxy): handle provider timeout errors
docs(api): update authentication documentation
test(keys): add API key validation tests
```

### Code Review Guidelines

- **Functionality**: Does the code work as intended?
- **Testing**: Are there adequate tests?
- **Performance**: Are there any performance concerns?
- **Security**: Are there any security vulnerabilities?
- **Documentation**: Is the code well-documented?
- **Standards**: Does the code follow project standards?

This developer guide provides comprehensive information for contributing to and extending the LLM Proxy System.
