# API Documentation

Complete API reference for the LLM Proxy System.

## Base URL

```
Production: https://api.yourdomain.com
Development: http://localhost:8080
```

## Authentication

The API uses two authentication methods:

### 1. JWT Authentication (User Sessions)
```bash
# Login to get JWT token
POST /api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}

# Use JWT token in subsequent requests
Authorization: Bearer <jwt_token>
```

### 2. System API Key (Direct API Access)
```bash
# Use system API key for direct LLM requests
Authorization: Bearer <system_api_key>
```

## API Endpoints

### Authentication Endpoints

#### Register User
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "jwt_token_here",
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "role": "user",
      "system_api_key": "sk-sys_generated_key"
    }
  }
}
```

#### Login User
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Logout User
```http
POST /api/v1/auth/logout
Authorization: Bearer <jwt_token>
```

#### Get Current User
```http
GET /api/v1/users/me
Authorization: Bearer <jwt_token>
```

### API Key Management

#### List API Keys
```http
GET /api/v1/users/me/keys
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "keys": [
      {
        "id": 1,
        "provider": "openai",
        "name": "My OpenAI Key",
        "status": "active",
        "masked_api_key": "sk-...abc123",
        "request_count": 150,
        "success_count": 148,
        "error_count": 2,
        "last_used_at": "2024-01-15T10:30:00Z",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "count": 1
  }
}
```

#### Create API Key
```http
POST /api/v1/users/me/keys
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "provider": "openai",
  "name": "My OpenAI Key",
  "api_key": "sk-your-actual-openai-key"
}
```

#### Update API Key
```http
PUT /api/v1/users/me/keys/{id}
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "Updated Key Name",
  "api_key": "sk-new-api-key" // optional
}
```

#### Delete API Key
```http
DELETE /api/v1/users/me/keys/{id}
Authorization: Bearer <jwt_token>
```

#### Test API Key
```http
POST /api/v1/users/me/keys/{id}/test
Authorization: Bearer <jwt_token>
```

### LLM Proxy Endpoints

#### Chat Completions (OpenAI Compatible)
```http
POST /api/v1/chat/completions
Authorization: Bearer <system_api_key>
Content-Type: application/json

{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "max_tokens": 100,
  "temperature": 0.7,
  "stream": false
}
```

**Response:**
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": **********,
  "model": "gpt-3.5-turbo",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! I'm doing well, thank you for asking. How can I help you today?"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 12,
    "completion_tokens": 20,
    "total_tokens": 32
  }
}
```

#### Direct LLM Proxy
```http
POST /api/v1/llm/proxy
Authorization: Bearer <system_api_key>
Content-Type: application/json

{
  "model": "gpt-4",
  "messages": [
    {
      "role": "user",
      "content": "Explain quantum computing"
    }
  ],
  "provider": "openai", // optional - for specific provider
  "max_tokens": 500,
  "temperature": 0.7
}
```

#### Validate Request
```http
POST /api/v1/llm/validate
Authorization: Bearer <system_api_key>
Content-Type: application/json

{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "user",
      "content": "Test message"
    }
  ]
}
```

#### Get Supported Models
```http
GET /api/v1/models
Authorization: Bearer <system_api_key>
```

### Webhook Management

#### List Webhooks
```http
GET /api/v1/users/me/webhooks
Authorization: Bearer <jwt_token>
```

#### Create Webhook
```http
POST /api/v1/users/me/webhooks
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "Discord Notifications",
  "url": "https://discord.com/api/webhooks/...",
  "events": ["user.registered", "api_key.created"],
  "is_active": true,
  "secret": "webhook_secret",
  "description": "Notifications for Discord channel"
}
```

#### Update Webhook
```http
PUT /api/v1/users/me/webhooks/{id}
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "Updated Webhook",
  "is_active": false
}
```

#### Delete Webhook
```http
DELETE /api/v1/users/me/webhooks/{id}
Authorization: Bearer <jwt_token>
```

#### Test Webhook
```http
POST /api/v1/users/me/webhooks/{id}/test
Authorization: Bearer <jwt_token>
```

#### Get Supported Events
```http
GET /api/v1/users/me/webhooks/events
Authorization: Bearer <jwt_token>
```

### Admin Endpoints

#### Get System Statistics
```http
GET /api/v1/admin/system/stats
Authorization: Bearer <admin_jwt_token>
```

#### Get System Health
```http
GET /api/v1/admin/system/health
Authorization: Bearer <admin_jwt_token>
```

#### List Users
```http
GET /api/v1/admin/users?page=1&limit=20
Authorization: Bearer <admin_jwt_token>
```

#### Update User
```http
PUT /api/v1/admin/users/{id}
Authorization: Bearer <admin_jwt_token>
Content-Type: application/json

{
  "role": "admin",
  "is_active": true
}
```

#### Delete User
```http
DELETE /api/v1/admin/users/{id}
Authorization: Bearer <admin_jwt_token>
```

### Monitoring Endpoints

#### Get System Metrics
```http
GET /api/v1/admin/monitoring/system
Authorization: Bearer <admin_jwt_token>
```

#### Get Business Metrics
```http
GET /api/v1/admin/monitoring/business
Authorization: Bearer <admin_jwt_token>
```

#### Get Analytics
```http
GET /api/v1/admin/monitoring/analytics?period=day&start_time=2024-01-01T00:00:00Z&end_time=2024-01-02T00:00:00Z
Authorization: Bearer <admin_jwt_token>
```

#### Get Real-time Metrics
```http
GET /api/v1/admin/monitoring/realtime
Authorization: Bearer <admin_jwt_token>
```

#### Get Provider Metrics
```http
GET /api/v1/admin/monitoring/providers/{provider}
Authorization: Bearer <admin_jwt_token>
```

## Error Responses

All API endpoints return errors in a consistent format:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": "Email is required"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

### Common Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `VALIDATION_ERROR` | Invalid request data | 400 |
| `UNAUTHORIZED` | Authentication required | 401 |
| `FORBIDDEN` | Insufficient permissions | 403 |
| `NOT_FOUND` | Resource not found | 404 |
| `RATE_LIMITED` | Rate limit exceeded | 429 |
| `INTERNAL_ERROR` | Server error | 500 |
| `SERVICE_UNAVAILABLE` | Service temporarily unavailable | 503 |

## Rate Limiting

The API implements rate limiting per user:

- **Default**: 100 requests per minute
- **Admin**: 1000 requests per minute
- **LLM Proxy**: 60 requests per minute

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642694400
```

## Pagination

List endpoints support pagination:

```http
GET /api/v1/admin/users?page=1&limit=20
```

Response includes pagination metadata:
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "total_pages": 8,
    "has_next": true,
    "has_prev": false
  }
}
```

## Webhooks

### Webhook Events

| Event | Description | Payload |
|-------|-------------|---------|
| `user.registered` | New user registration | User object |
| `user.login` | User login | User ID, timestamp |
| `api_key.created` | API key created | Key metadata |
| `api_key.deleted` | API key deleted | Key ID |
| `api_key.invalid` | API key validation failed | Key ID, error |
| `llm_request.success` | LLM request completed | Request metadata |
| `llm_request.failure` | LLM request failed | Request metadata, error |
| `system.alert` | System alert triggered | Alert details |
| `quota.exceeded` | Usage quota exceeded | Usage details |

### Webhook Payload Format

```json
{
  "event": "user.registered",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "user_id": 123,
    "email": "<EMAIL>"
  },
  "webhook_id": "wh_123456789",
  "signature": "sha256=..."
}
```

### Webhook Signature Verification

Webhooks are signed with HMAC-SHA256:

```python
import hmac
import hashlib

def verify_webhook(payload, signature, secret):
    expected = hmac.new(
        secret.encode(),
        payload.encode(),
        hashlib.sha256
    ).hexdigest()
    return hmac.compare_digest(f"sha256={expected}", signature)
```

## SDKs and Examples

### cURL Examples

See individual endpoint documentation above.

### Python Example

```python
import requests

# Authentication
response = requests.post('http://localhost:8080/api/v1/auth/login', json={
    'email': '<EMAIL>',
    'password': 'password123'
})
token = response.json()['data']['token']

# LLM Request
response = requests.post('http://localhost:8080/api/v1/chat/completions', 
    headers={'Authorization': f'Bearer {token}'},
    json={
        'model': 'gpt-3.5-turbo',
        'messages': [{'role': 'user', 'content': 'Hello!'}]
    }
)
print(response.json())
```

### JavaScript Example

```javascript
// Authentication
const authResponse = await fetch('http://localhost:8080/api/v1/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});
const { data } = await authResponse.json();
const token = data.token;

// LLM Request
const llmResponse = await fetch('http://localhost:8080/api/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    model: 'gpt-3.5-turbo',
    messages: [{ role: 'user', content: 'Hello!' }]
  })
});
const result = await llmResponse.json();
console.log(result);
```
