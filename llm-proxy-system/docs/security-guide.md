# Security Guide

Comprehensive security guidelines and best practices for the LLM Proxy System.

## 🔒 Security Overview

The LLM Proxy System implements multiple layers of security:

- **Authentication**: JWT-based user authentication
- **Authorization**: Role-based access control (RBAC)
- **Encryption**: AES-GCM encryption for sensitive data
- **Transport Security**: TLS/HTTPS for all communications
- **Input Validation**: Comprehensive request validation
- **Rate Limiting**: Protection against abuse
- **Audit Logging**: Complete audit trail
- **Secure Headers**: Security-focused HTTP headers

## 🔐 Authentication Security

### JWT Configuration

#### Strong JWT Secrets
```bash
# Generate secure JWT secret (minimum 32 characters)
openssl rand -base64 32

# Environment configuration
JWT_SECRET=your_generated_32_character_secret_key_here
JWT_EXPIRATION_HOURS=24
JWT_REFRESH_EXPIRATION_HOURS=168
```

#### JWT Best Practices

1. **Secret Management**
   - Use cryptographically secure random secrets
   - Rotate secrets regularly (quarterly recommended)
   - Store secrets in secure environment variables
   - Never commit secrets to version control

2. **Token Expiration**
   ```bash
   # Production settings
   JWT_EXPIRATION_HOURS=1          # Short-lived access tokens
   JWT_REFRESH_EXPIRATION_HOURS=24 # Longer refresh tokens
   
   # Development settings (more lenient)
   JWT_EXPIRATION_HOURS=24
   JWT_REFRESH_EXPIRATION_HOURS=168
   ```

3. **Token Validation**
   - Validate token signature
   - Check token expiration
   - Verify token claims
   - Implement token blacklisting for logout

### Password Security

#### Password Requirements
```go
// Minimum password requirements
const (
    MinPasswordLength = 8
    RequireUppercase  = true
    RequireLowercase  = true
    RequireNumbers    = true
    RequireSpecial    = false // Optional
)
```

#### Password Hashing
```go
// Use bcrypt with appropriate cost
const BcryptCost = 12 // Adjust based on security vs performance needs

func HashPassword(password string) (string, error) {
    bytes, err := bcrypt.GenerateFromPassword([]byte(password), BcryptCost)
    return string(bytes), err
}
```

## 🔑 API Key Security

### Encryption Configuration

#### AES-GCM Encryption
```bash
# Generate 32-character encryption key
openssl rand -hex 16

# Environment configuration
ENCRYPTION_KEY=your_32_character_encryption_key_here
ENCRYPTION_ALGORITHM=AES-GCM
```

#### Key Storage Security
```go
// API keys are encrypted before storage
type UserAPIKey struct {
    ID              uint   `gorm:"primaryKey"`
    UserID          uint   `gorm:"not null"`
    Provider        string `gorm:"not null"`
    Name            string `gorm:"not null"`
    EncryptedAPIKey string `gorm:"not null"` // Encrypted with AES-GCM
    KeyHash         string `gorm:"not null"` // SHA-256 hash for validation
    Status          string `gorm:"default:testing"`
}
```

### API Key Best Practices

1. **Key Rotation**
   - Implement regular key rotation
   - Provide key rotation notifications
   - Support graceful key transitions

2. **Key Validation**
   - Test keys before activation
   - Monitor key health continuously
   - Automatic key deactivation on failures

3. **Access Control**
   - Users can only access their own keys
   - Admin oversight for security monitoring
   - Audit logging for all key operations

## 🌐 Transport Security

### TLS/HTTPS Configuration

#### SSL Certificate Setup
```bash
# Let's Encrypt (recommended for production)
sudo certbot --nginx -d yourdomain.com -d api.yourdomain.com

# Manual certificate installation
sudo cp yourdomain.com.crt /etc/ssl/certs/
sudo cp yourdomain.com.key /etc/ssl/private/
sudo chmod 600 /etc/ssl/private/yourdomain.com.key
```

#### Nginx TLS Configuration
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    # Modern TLS configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    
    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';" always;
}
```

### Environment-Specific Security

#### Production Security
```bash
# Force HTTPS
FORCE_HTTPS=true
SECURE_COOKIES=true
HSTS_ENABLED=true

# Disable debug features
GIN_MODE=release
LOG_LEVEL=info
DEBUG_ENABLED=false
```

#### Development Security
```bash
# Relaxed for development
FORCE_HTTPS=false
SECURE_COOKIES=false
GIN_MODE=debug
LOG_LEVEL=debug
```

## 🛡️ Input Validation & Sanitization

### Request Validation

#### Input Sanitization
```go
// Validate and sanitize user input
func ValidateEmail(email string) error {
    email = strings.TrimSpace(strings.ToLower(email))
    
    if len(email) == 0 {
        return errors.New("email is required")
    }
    
    if len(email) > 254 {
        return errors.New("email too long")
    }
    
    emailRegex := regexp.MustCompile(`^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,}$`)
    if !emailRegex.MatchString(email) {
        return errors.New("invalid email format")
    }
    
    return nil
}
```

#### SQL Injection Prevention
```go
// Use parameterized queries with GORM
func (r *Repository) GetUserByEmail(email string) (*User, error) {
    var user User
    // GORM automatically handles parameterization
    err := r.db.Where("email = ?", email).First(&user).Error
    return &user, err
}
```

#### XSS Prevention
```go
// HTML escape user content
import "html"

func SanitizeUserContent(content string) string {
    return html.EscapeString(content)
}
```

### Rate Limiting

#### Rate Limit Configuration
```bash
# Global rate limits
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Admin rate limits
RATE_LIMIT_ADMIN_MULTIPLIER=10

# Provider-specific limits
PROVIDER_RATE_LIMIT_ENABLED=true
```

#### Implementation
```go
// Rate limiting middleware
func RateLimitMiddleware(limit int, window time.Duration) gin.HandlerFunc {
    limiter := rate.NewLimiter(rate.Every(window), limit)
    
    return func(c *gin.Context) {
        if !limiter.Allow() {
            c.JSON(http.StatusTooManyRequests, gin.H{
                "error": "Rate limit exceeded",
                "retry_after": window.Seconds(),
            })
            c.Abort()
            return
        }
        c.Next()
    }
}
```

## 🔍 Security Monitoring

### Audit Logging

#### Security Events
```go
// Log security-relevant events
func LogSecurityEvent(ctx context.Context, event SecurityEvent) {
    logger.Info("Security event",
        "event_type", event.Type,
        "user_id", event.UserID,
        "ip_address", event.IPAddress,
        "user_agent", event.UserAgent,
        "timestamp", event.Timestamp,
        "details", event.Details,
    )
}

// Security event types
const (
    EventLogin           = "user.login"
    EventLoginFailed     = "user.login.failed"
    EventLogout          = "user.logout"
    EventPasswordChange  = "user.password.changed"
    EventAPIKeyCreated   = "api_key.created"
    EventAPIKeyDeleted   = "api_key.deleted"
    EventUnauthorized    = "access.unauthorized"
    EventRateLimited     = "access.rate_limited"
)
```

#### Failed Login Monitoring
```go
// Track failed login attempts
type FailedLoginAttempt struct {
    IPAddress string
    Email     string
    Timestamp time.Time
    UserAgent string
}

// Implement account lockout after multiple failures
const MaxFailedAttempts = 5
const LockoutDuration = 15 * time.Minute
```

### Intrusion Detection

#### Suspicious Activity Detection
```go
// Monitor for suspicious patterns
func DetectSuspiciousActivity(userID uint, activity Activity) bool {
    // Multiple failed logins
    if activity.Type == "login_failed" {
        count := countRecentFailedLogins(userID, 1*time.Hour)
        return count > 5
    }
    
    // Unusual API usage patterns
    if activity.Type == "api_request" {
        rate := calculateRequestRate(userID, 1*time.Minute)
        return rate > 100 // requests per minute
    }
    
    // Geographic anomalies
    if activity.Type == "login" {
        return isUnusualLocation(userID, activity.IPAddress)
    }
    
    return false
}
```

## 🔐 Database Security

### Connection Security

#### Secure Database Configuration
```bash
# Use SSL connections
DATABASE_URL=********************************/db?sslmode=require

# Connection limits
DATABASE_MAX_CONNECTIONS=100
DATABASE_CONNECTION_MAX_LIFETIME=1h
```

#### Database User Permissions
```sql
-- Create limited database user
CREATE USER llmproxy_app WITH PASSWORD 'secure_password';

-- Grant minimal required permissions
GRANT CONNECT ON DATABASE llmproxy TO llmproxy_app;
GRANT USAGE ON SCHEMA public TO llmproxy_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO llmproxy_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO llmproxy_app;

-- Revoke unnecessary permissions
REVOKE CREATE ON SCHEMA public FROM llmproxy_app;
```

### Data Protection

#### Sensitive Data Handling
```go
// Encrypt sensitive fields
type User struct {
    ID           uint   `gorm:"primaryKey"`
    Email        string `gorm:"uniqueIndex;not null"`
    PasswordHash string `gorm:"not null"`
    // Never store plain text passwords or API keys
}

// Mask sensitive data in logs
func (u User) String() string {
    return fmt.Sprintf("User{ID: %d, Email: %s}", u.ID, maskEmail(u.Email))
}

func maskEmail(email string) string {
    parts := strings.Split(email, "@")
    if len(parts) != 2 {
        return "***"
    }
    return parts[0][:1] + "***@" + parts[1]
}
```

## 🔒 Webhook Security

### Webhook Signature Verification

#### HMAC Signature
```go
// Generate webhook signature
func GenerateWebhookSignature(payload []byte, secret string) string {
    h := hmac.New(sha256.New, []byte(secret))
    h.Write(payload)
    return "sha256=" + hex.EncodeToString(h.Sum(nil))
}

// Verify webhook signature
func VerifyWebhookSignature(payload []byte, signature, secret string) bool {
    expected := GenerateWebhookSignature(payload, secret)
    return hmac.Equal([]byte(signature), []byte(expected))
}
```

#### Webhook Security Configuration
```bash
# Enable signature verification
WEBHOOK_SIGNATURE_ENABLED=true
WEBHOOK_SIGNATURE_ALGORITHM=sha256

# Webhook timeouts
WEBHOOK_TIMEOUT_SECONDS=30
WEBHOOK_MAX_RETRIES=3
```

## 🚨 Incident Response

### Security Incident Procedures

#### Immediate Response
1. **Identify the incident**
   - Monitor security alerts
   - Analyze suspicious activity
   - Assess impact and scope

2. **Contain the incident**
   - Block malicious IPs
   - Disable compromised accounts
   - Isolate affected systems

3. **Investigate and document**
   - Collect evidence
   - Analyze attack vectors
   - Document timeline

#### Recovery Procedures
```bash
# Emergency user lockout
curl -X PUT -H "Authorization: Bearer ADMIN_TOKEN" \
  http://localhost:8080/api/v1/admin/users/123 \
  -d '{"is_active": false}'

# Force password reset
curl -X POST -H "Authorization: Bearer ADMIN_TOKEN" \
  http://localhost:8080/api/v1/admin/users/123/force-password-reset

# Revoke all sessions
curl -X POST -H "Authorization: Bearer ADMIN_TOKEN" \
  http://localhost:8080/api/v1/admin/security/revoke-all-sessions
```

### Security Monitoring Alerts

#### Critical Security Alerts
- Multiple failed login attempts
- Unusual API usage patterns
- Unauthorized access attempts
- System configuration changes
- Database security events

#### Alert Configuration
```bash
# Security alert thresholds
SECURITY_ALERT_FAILED_LOGINS=5
SECURITY_ALERT_UNUSUAL_ACTIVITY=true
SECURITY_ALERT_CONFIG_CHANGES=true

# Alert channels
SECURITY_ALERT_EMAIL=<EMAIL>
SECURITY_ALERT_WEBHOOK=https://hooks.slack.com/services/...
```

## 📋 Security Checklist

### Deployment Security Checklist

- [ ] **TLS/HTTPS enabled** for all communications
- [ ] **Strong JWT secrets** (32+ characters, cryptographically secure)
- [ ] **Database encryption** enabled (encryption at rest)
- [ ] **API key encryption** configured (AES-GCM)
- [ ] **Rate limiting** enabled and configured
- [ ] **Security headers** implemented
- [ ] **Input validation** comprehensive
- [ ] **Audit logging** enabled
- [ ] **Backup encryption** configured
- [ ] **Firewall rules** properly configured
- [ ] **Regular security updates** scheduled
- [ ] **Monitoring and alerting** configured
- [ ] **Incident response plan** documented
- [ ] **Security training** completed

### Regular Security Maintenance

#### Weekly Tasks
- Review security logs
- Check for failed login attempts
- Monitor unusual activity patterns
- Verify backup integrity

#### Monthly Tasks
- Update dependencies
- Review user access permissions
- Rotate API keys
- Security configuration review

#### Quarterly Tasks
- Rotate JWT secrets
- Security audit
- Penetration testing
- Update security documentation

This security guide provides comprehensive protection for the LLM Proxy System across all layers of the application stack.
