# Admin Manual

Complete guide for system administrators managing the LLM Proxy System.

## 🔐 Admin Access

### Admin Account Setup

1. **Initial Admin Creation**
   ```bash
   # Create first admin user
   go run cmd/admin/main.go create-user \
     --email <EMAIL> \
     --password admin123 \
     --role admin
   ```

2. **Admin Login**
   - Use the same login page as regular users
   - Admin users get additional menu options and permissions

3. **Admin Dashboard Access**
   - Navigate to `/admin` after login
   - Access comprehensive system administration tools

### Admin Permissions

Admin users have access to:
- System statistics and health monitoring
- User management and administration
- Global system configuration
- Provider routing and priority management
- System alerts and notifications
- Usage analytics across all users
- Webhook event triggering and testing

## 📊 System Monitoring

### Admin Dashboard

The admin dashboard provides comprehensive system overview:

#### System Health
- **CPU Usage**: Real-time CPU utilization
- **Memory Usage**: RAM consumption and availability
- **Database Status**: Connection health and performance
- **Redis Status**: Cache performance and memory usage
- **Application Metrics**: Request rates, error rates, response times

#### Business Metrics
- **Total Users**: Active and inactive user counts
- **API Keys**: Total keys across all users by status
- **Request Volume**: System-wide request statistics
- **Provider Performance**: Success rates and latency by provider

### Real-time Monitoring

#### System Metrics API
```bash
# Get current system metrics
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  https://api.yourdomain.com/api/v1/admin/monitoring/system

# Get business metrics
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  https://api.yourdomain.com/api/v1/admin/monitoring/business
```

#### Health Check Endpoint
```bash
# Comprehensive health check
curl https://api.yourdomain.com/healthz

# Response includes:
# - Overall system status
# - Component health (database, redis, etc.)
# - Performance metrics
# - Uptime information
```

### Analytics and Reporting

#### Usage Analytics
- **Time-based Analysis**: Hour, day, week, month views
- **Provider Breakdown**: Performance by LLM provider
- **User Activity**: Top users and usage patterns
- **Error Analysis**: Error rates and types
- **Performance Trends**: Response time and throughput trends

#### Custom Reports
```bash
# Get analytics for specific time period
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  "https://api.yourdomain.com/api/v1/admin/monitoring/analytics?period=day&start_time=2024-01-01T00:00:00Z&end_time=2024-01-02T00:00:00Z"
```

## 👥 User Management

### User Administration

#### View All Users
```bash
# List users with pagination
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  "https://api.yourdomain.com/api/v1/admin/users?page=1&limit=20"
```

#### User Management Actions

1. **Update User Role**
   ```bash
   curl -X PUT -H "Authorization: Bearer ADMIN_TOKEN" \
     -H "Content-Type: application/json" \
     https://api.yourdomain.com/api/v1/admin/users/123 \
     -d '{"role": "admin"}'
   ```

2. **Activate/Deactivate User**
   ```bash
   curl -X PUT -H "Authorization: Bearer ADMIN_TOKEN" \
     -H "Content-Type: application/json" \
     https://api.yourdomain.com/api/v1/admin/users/123 \
     -d '{"is_active": false}'
   ```

3. **Delete User**
   ```bash
   curl -X DELETE -H "Authorization: Bearer ADMIN_TOKEN" \
     https://api.yourdomain.com/api/v1/admin/users/123
   ```

#### User Statistics

View detailed user information:
- Account creation date
- Last login time
- API key count and status
- Request volume and success rates
- Token usage statistics

### Bulk Operations

#### User Import/Export
```bash
# Export user data
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  https://api.yourdomain.com/api/v1/admin/users/export > users.csv

# Import users (CSV format)
curl -X POST -H "Authorization: Bearer ADMIN_TOKEN" \
  -F "file=@users.csv" \
  https://api.yourdomain.com/api/v1/admin/users/import
```

## ⚙️ System Configuration

### Global Settings

#### Rate Limiting Configuration
```bash
# Update global rate limits
curl -X PUT -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  https://api.yourdomain.com/api/v1/admin/config/rate-limits \
  -d '{
    "default_requests_per_minute": 100,
    "admin_requests_per_minute": 1000,
    "burst_capacity": 20
  }'
```

#### Provider Configuration
```bash
# Update provider settings
curl -X PUT -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  https://api.yourdomain.com/api/v1/admin/config/providers \
  -d '{
    "default_provider": "openai",
    "timeout_seconds": 30,
    "max_retries": 3,
    "health_check_interval": 60
  }'
```

### Routing Management

#### Provider Priority Configuration
```bash
# Set provider priorities
curl -X PUT -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  https://api.yourdomain.com/api/v1/admin/routing/priorities \
  -d '{
    "openai": 1,
    "gemini": 2,
    "claude": 3,
    "perplexity": 4
  }'
```

#### Routing Rules
```bash
# Create routing rule
curl -X POST -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  https://api.yourdomain.com/api/v1/admin/routing/rules \
  -d '{
    "name": "High Priority Users",
    "condition": "user.role == \"premium\"",
    "provider": "gpt-4",
    "priority": 1
  }'
```

## 🚨 Alert Management

### Alert Configuration

#### View Active Alerts
```bash
# Get current alerts
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  https://api.yourdomain.com/api/v1/admin/monitoring/alerts
```

#### Create Alert Rules
```bash
# Create new alert rule
curl -X POST -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  https://api.yourdomain.com/api/v1/admin/monitoring/alerts \
  -d '{
    "name": "High CPU Usage",
    "metric": "cpu_usage",
    "condition": "gt",
    "threshold": 80.0,
    "duration": "5m",
    "severity": "warning",
    "channels": ["email", "webhook"]
  }'
```

### Default Alert Rules

The system includes these default alerts:

1. **High CPU Usage** (>80%)
   - Severity: Warning
   - Duration: 5 minutes
   - Channels: Email, Webhook

2. **High Memory Usage** (>85%)
   - Severity: Warning
   - Duration: 5 minutes
   - Channels: Email, Webhook

3. **High Error Rate** (>5%)
   - Severity: Error
   - Duration: 2 minutes
   - Channels: Email, Webhook, Slack

4. **Slow Response Time** (>5 seconds)
   - Severity: Warning
   - Duration: 3 minutes
   - Channels: Email

5. **Low Cache Hit Rate** (<80%)
   - Severity: Info
   - Duration: 10 minutes
   - Channels: Email

### Alert Channels

#### Email Notifications
Configure SMTP settings in environment variables:
```bash
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=app_password
```

#### Webhook Notifications
```bash
# Configure alert webhook
curl -X POST -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  https://api.yourdomain.com/api/v1/admin/webhooks \
  -d '{
    "name": "Alert Webhook",
    "url": "https://hooks.slack.com/services/...",
    "events": ["system.alert"],
    "is_active": true
  }'
```

## 🔧 System Maintenance

### Database Management

#### Database Health
```bash
# Check database status
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  https://api.yourdomain.com/api/v1/admin/system/database/health

# Database statistics
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  https://api.yourdomain.com/api/v1/admin/system/database/stats
```

#### Database Maintenance
```bash
# Run database vacuum (PostgreSQL)
psql -h localhost -U llmproxy llmproxy -c "VACUUM ANALYZE;"

# Check database size
psql -h localhost -U llmproxy llmproxy -c "
  SELECT 
    pg_size_pretty(pg_database_size('llmproxy')) as database_size,
    pg_size_pretty(pg_total_relation_size('usage_logs')) as usage_logs_size;
"

# Clean old usage logs (older than 90 days)
psql -h localhost -U llmproxy llmproxy -c "
  DELETE FROM usage_logs 
  WHERE created_at < NOW() - INTERVAL '90 days';
"
```

### Cache Management

#### Redis Operations
```bash
# Check Redis status
redis-cli -u $REDIS_URL info memory

# Clear cache
redis-cli -u $REDIS_URL flushdb

# Monitor Redis operations
redis-cli -u $REDIS_URL monitor
```

### Log Management

#### Application Logs
```bash
# View application logs
tail -f /var/log/llm-proxy/app.log

# Search for errors
grep "ERROR" /var/log/llm-proxy/app.log | tail -20

# Log rotation
logrotate /etc/logrotate.d/llm-proxy
```

#### Access Logs
```bash
# Nginx access logs
tail -f /var/log/nginx/access.log

# Analyze request patterns
awk '{print $1}' /var/log/nginx/access.log | sort | uniq -c | sort -nr | head -10
```

## 🔄 Backup and Recovery

### Database Backup

#### Automated Backup Script
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"
DB_NAME="llmproxy"

# Create backup
pg_dump -h postgres -U llmproxy $DB_NAME | gzip > $BACKUP_DIR/backup_$DATE.sql.gz

# Keep only last 7 days of backups
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete

echo "Backup completed: backup_$DATE.sql.gz"
```

#### Restore from Backup
```bash
# Restore database
gunzip -c /backups/backup_20240115_120000.sql.gz | psql -h postgres -U llmproxy llmproxy
```

### Configuration Backup

#### Backup System Configuration
```bash
# Backup configuration files
tar -czf config_backup_$(date +%Y%m%d).tar.gz \
  .env \
  configs/ \
  nginx/ \
  docker-compose.prod.yml
```

## 📈 Performance Optimization

### Database Optimization

#### Query Performance
```sql
-- Check slow queries
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- Analyze table statistics
ANALYZE usage_logs;

-- Check index usage
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE tablename = 'usage_logs';
```

#### Connection Pool Tuning
```bash
# Optimize PostgreSQL settings
DATABASE_MAX_CONNECTIONS=200
DATABASE_MAX_IDLE_CONNECTIONS=20
DATABASE_CONNECTION_MAX_LIFETIME=1h
```

### Redis Optimization

#### Memory Management
```bash
# Set Redis memory limit
redis-cli -u $REDIS_URL config set maxmemory 512mb
redis-cli -u $REDIS_URL config set maxmemory-policy allkeys-lru

# Monitor memory usage
redis-cli -u $REDIS_URL info memory
```

### Application Performance

#### Go Application Tuning
```bash
# Environment variables for performance
GOMAXPROCS=4
GOGC=100
GOMEMLIMIT=1GiB
```

## 🔒 Security Management

### Security Monitoring

#### Failed Login Attempts
```bash
# Monitor failed logins
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  https://api.yourdomain.com/api/v1/admin/security/failed-logins

# Block suspicious IPs
curl -X POST -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  https://api.yourdomain.com/api/v1/admin/security/block-ip \
  -d '{"ip": "*************", "reason": "Brute force attempt"}'
```

#### API Key Security
```bash
# Audit API key usage
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  https://api.yourdomain.com/api/v1/admin/security/api-key-audit

# Force API key rotation
curl -X POST -H "Authorization: Bearer ADMIN_TOKEN" \
  https://api.yourdomain.com/api/v1/admin/security/rotate-keys
```

### SSL Certificate Management

#### Certificate Renewal
```bash
# Check certificate expiration
openssl x509 -in /etc/ssl/certs/yourdomain.com.crt -text -noout | grep "Not After"

# Renew Let's Encrypt certificates
certbot renew --dry-run
certbot renew
```

## 🚨 Troubleshooting

### Common Issues

#### High Memory Usage
1. Check for memory leaks in application logs
2. Analyze Redis memory usage
3. Review database connection pool settings
4. Monitor garbage collection metrics

#### Database Connection Issues
1. Check PostgreSQL connection limits
2. Verify network connectivity
3. Review connection pool configuration
4. Monitor database performance metrics

#### Provider API Failures
1. Check provider API status pages
2. Verify API key validity
3. Review rate limiting settings
4. Monitor provider response times

### Emergency Procedures

#### System Recovery
```bash
# Restart all services
docker-compose -f docker-compose.prod.yml restart

# Check service health
docker-compose -f docker-compose.prod.yml ps

# View service logs
docker-compose -f docker-compose.prod.yml logs -f backend
```

#### Database Recovery
```bash
# Check database connectivity
pg_isready -h postgres -U llmproxy

# Restart PostgreSQL
docker-compose -f docker-compose.prod.yml restart postgres

# Restore from backup if needed
gunzip -c /backups/latest_backup.sql.gz | psql -h postgres -U llmproxy llmproxy
```

This admin manual provides comprehensive guidance for effectively managing and maintaining the LLM Proxy System in production environments.
