# User Manual

Complete guide for using the LLM Proxy System as an end user.

## 🚀 Getting Started

### Account Registration

1. **Visit the Registration Page**
   - Navigate to `https://yourdomain.com/register`
   - Or click "Create Account" from the login page

2. **Create Your Account**
   - Enter your email address
   - Create a strong password (minimum 6 characters with uppercase, lowercase, and number)
   - Accept the Terms of Service and Privacy Policy
   - Click "Create Account"

3. **Email Verification**
   - Check your email for a verification link
   - Click the link to verify your account
   - You'll be redirected to the login page

### Logging In

1. **Access the Login Page**
   - Navigate to `https://yourdomain.com/login`
   - Enter your email and password
   - Click "Sign In"

2. **Dashboard Access**
   - After successful login, you'll be redirected to your dashboard
   - Your system API key will be displayed in your profile

## 📊 Dashboard Overview

### Main Dashboard

The dashboard provides an overview of your account activity:

- **API Keys**: Number of configured provider keys
- **Total Requests**: Lifetime request count
- **Webhooks**: Number of configured webhooks
- **Tokens Used**: Total tokens consumed
- **Recent Activity**: Latest system events

### Quick Actions

From the dashboard, you can quickly:
- Add a new API key
- Create a webhook
- Access the LLM playground
- View usage analytics

## 🔑 API Key Management

### Adding Provider API Keys

1. **Navigate to API Keys**
   - Click "API Keys" in the sidebar
   - Click "Add API Key" button

2. **Configure Your Key**
   - **Provider**: Select from 12+ supported providers
     - OpenAI (GPT-3.5, GPT-4)
     - Google Gemini
     - Anthropic Claude
     - Perplexity
     - Mistral
     - DeepSeek
     - Moonshot
     - Ollama
     - AWS Bedrock
     - Azure OpenAI
     - Alibaba
   - **Name**: Give your key a descriptive name
   - **API Key**: Enter your provider's API key

3. **Test Your Key**
   - Click "Test" to verify the key works
   - The system will make a test request to validate

### Managing API Keys

- **View Keys**: See all your configured keys with status indicators
- **Edit Keys**: Update key names or replace API keys
- **Delete Keys**: Remove keys you no longer need
- **Monitor Usage**: Track requests, tokens, and success rates

### Key Status Indicators

- 🟢 **Active**: Key is working properly
- 🟡 **Testing**: Key validation in progress
- 🔴 **Invalid**: Key has failed validation
- ⚪ **Inactive**: Key is disabled

## 🎮 LLM Playground

### Accessing the Playground

1. Click "LLM Playground" in the sidebar
2. The playground provides an interactive interface to test LLM requests

### Using the Playground

1. **Select Model**
   - Choose from available models across all your configured providers
   - Popular options: GPT-3.5 Turbo, GPT-4, Gemini Pro, Claude 3

2. **Configure Parameters**
   - **Max Tokens**: Maximum response length (1-4000)
   - **Temperature**: Creativity level (0.0-2.0)
   - **Model**: Specific model to use

3. **Send Messages**
   - Type your message in the input area
   - Click "Send Message" to get a response
   - View the conversation history

4. **Quick Examples**
   - Use pre-built examples for common tasks:
     - Code generation
     - Text summarization
     - Creative writing
     - Data analysis
     - Translation
     - Problem solving

### Playground Features

- **Real-time Responses**: See responses as they're generated
- **Conversation History**: Maintain context across messages
- **Error Handling**: Clear error messages for failed requests
- **Token Counting**: Track token usage for each request

## 🔔 Webhook Management

### Creating Webhooks

1. **Navigate to Webhooks**
   - Click "Webhooks" in the sidebar
   - Click "Add Webhook" button

2. **Configure Webhook**
   - **Name**: Descriptive name for your webhook
   - **URL**: Your endpoint URL (must be HTTPS)
   - **Events**: Select which events to receive
   - **Secret**: Optional secret for signature verification
   - **Description**: Optional description

### Supported Events

- **User Events**
  - `user.login`: User login events
  - `user.logout`: User logout events

- **API Key Events**
  - `api_key.created`: New API key added
  - `api_key.deleted`: API key removed
  - `api_key.invalid`: API key validation failed

- **LLM Request Events**
  - `llm_request.success`: Successful LLM request
  - `llm_request.failure`: Failed LLM request

- **System Events**
  - `system.alert`: System alerts
  - `quota.exceeded`: Usage quota exceeded

### Webhook Testing

- **Test Webhook**: Send a test event to verify your endpoint
- **View Logs**: See delivery attempts and responses
- **Retry Failed**: Manually retry failed deliveries

### Webhook Security

- **Signature Verification**: Webhooks are signed with HMAC-SHA256
- **HTTPS Required**: All webhook URLs must use HTTPS
- **Timeout Handling**: 30-second timeout with automatic retries

## 📈 Usage Analytics

### Viewing Usage History

1. **Navigate to Usage History**
   - Click "Usage History" in the sidebar
   - View your request analytics and trends

2. **Analytics Dashboard**
   - **Request Volume**: Requests over time
   - **Token Usage**: Token consumption trends
   - **Provider Performance**: Success rates by provider
   - **Error Analysis**: Error types and frequencies

### Usage Metrics

- **Total Requests**: Lifetime request count
- **Success Rate**: Percentage of successful requests
- **Average Response Time**: Mean response time across requests
- **Token Consumption**: Total tokens used
- **Provider Distribution**: Usage breakdown by provider

### Filtering and Exports

- **Time Ranges**: Filter by hour, day, week, or month
- **Provider Filter**: View metrics for specific providers
- **Export Data**: Download usage data as CSV

## 👤 Profile Management

### Account Settings

1. **Navigate to Profile**
   - Click "Profile" in the sidebar
   - View and manage your account information

2. **Account Information**
   - **Email**: Your account email (read-only)
   - **Role**: Your account role (user/admin)
   - **System API Key**: Your unique API key for direct requests

### System API Key

Your system API key allows direct access to the LLM proxy:

```bash
curl -X POST https://api.yourdomain.com/api/v1/chat/completions \
  -H "Authorization: Bearer YOUR_SYSTEM_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

### Security Settings

- **Password Change**: Update your account password
- **Two-Factor Authentication**: Enable 2FA for additional security
- **Session Management**: View and revoke active sessions

## 🔌 API Integration

### Using Your System API Key

Your system API key provides direct access to the LLM proxy API:

1. **Get Your API Key**
   - Found in your Profile page
   - Format: `sk-sys_xxxxxxxxxx`

2. **Make Requests**
   - Use OpenAI-compatible endpoints
   - Include your API key in the Authorization header

### Example Integrations

#### Python

```python
import openai

# Configure the client
client = openai.OpenAI(
    api_key="YOUR_SYSTEM_API_KEY",
    base_url="https://api.yourdomain.com/api/v1"
)

# Make a request
response = client.chat.completions.create(
    model="gpt-3.5-turbo",
    messages=[
        {"role": "user", "content": "Hello, world!"}
    ]
)

print(response.choices[0].message.content)
```

#### JavaScript

```javascript
const response = await fetch('https://api.yourdomain.com/api/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_SYSTEM_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    model: 'gpt-3.5-turbo',
    messages: [
      { role: 'user', content: 'Hello, world!' }
    ]
  })
});

const data = await response.json();
console.log(data.choices[0].message.content);
```

#### cURL

```bash
curl -X POST https://api.yourdomain.com/api/v1/chat/completions \
  -H "Authorization: Bearer YOUR_SYSTEM_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Hello, world!"}
    ]
  }'
```

## 🚨 Troubleshooting

### Common Issues

#### API Key Not Working

1. **Check Key Status**
   - Verify the key shows as "Active" in your dashboard
   - Test the key using the "Test" button

2. **Verify Provider Key**
   - Ensure your provider API key is valid
   - Check if you have sufficient credits/quota

3. **Check Provider Status**
   - Some providers may have temporary outages
   - The system will automatically retry with fallback providers

#### Webhook Not Receiving Events

1. **Verify URL**
   - Ensure your webhook URL is accessible
   - Must use HTTPS protocol

2. **Check Event Selection**
   - Verify you've selected the correct events
   - Test with a simple event like `user.login`

3. **Review Logs**
   - Check webhook delivery logs for error messages
   - Look for timeout or connection errors

#### Slow Response Times

1. **Check Provider Performance**
   - Some providers may be experiencing high load
   - Try switching to a different provider

2. **Optimize Requests**
   - Reduce max_tokens for faster responses
   - Use streaming for long responses

#### Rate Limiting

1. **Check Usage Limits**
   - Default: 100 requests per minute
   - Contact admin for limit increases

2. **Implement Backoff**
   - Add exponential backoff to your requests
   - Respect rate limit headers

### Getting Help

- **Documentation**: Check the complete documentation
- **Support Email**: <EMAIL>
- **Status Page**: status.yourdomain.com
- **Community Forum**: forum.yourdomain.com

## 📚 Best Practices

### API Key Security

- **Never Share Keys**: Keep your API keys confidential
- **Rotate Regularly**: Update keys periodically
- **Use Environment Variables**: Don't hardcode keys in your code
- **Monitor Usage**: Watch for unexpected usage patterns

### Request Optimization

- **Choose Appropriate Models**: Use the right model for your task
- **Optimize Prompts**: Clear, specific prompts get better results
- **Manage Token Usage**: Monitor and optimize token consumption
- **Implement Caching**: Cache responses when appropriate

### Webhook Reliability

- **Implement Idempotency**: Handle duplicate events gracefully
- **Verify Signatures**: Always verify webhook signatures
- **Handle Retries**: Implement proper retry logic
- **Monitor Endpoints**: Ensure your endpoints are always available

This user manual provides comprehensive guidance for effectively using the LLM Proxy System's features and capabilities.
