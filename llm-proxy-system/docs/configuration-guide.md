# Configuration Guide

Complete guide for configuring the LLM Proxy System for different environments.

## 📋 Environment Configuration

### Environment Files

The system uses environment files for configuration:

- `.env` - Development configuration
- `.env.prod` - Production configuration
- `.env.test` - Testing configuration

### Environment Variables

#### Server Configuration

```bash
# Server Settings
PORT=8080                    # Server port (default: 8080)
GIN_MODE=release            # Gin mode: debug, release, test
ENVIRONMENT=production      # Environment: development, production, test
LOG_LEVEL=info             # Log level: debug, info, warn, error

# CORS Settings
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://api.yourdomain.com
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Origin,Content-Type,Authorization
CORS_ALLOW_CREDENTIALS=true
```

#### Database Configuration

```bash
# PostgreSQL Database
DATABASE_URL=postgresql://user:password@host:port/database?sslmode=require
DATABASE_MAX_CONNECTIONS=100        # Maximum database connections
DATABASE_MAX_IDLE_CONNECTIONS=10    # Maximum idle connections
DATABASE_CONNECTION_MAX_LIFETIME=1h # Connection lifetime

# Database Pool Settings
DATABASE_POOL_MAX_OPEN=25           # Maximum open connections
DATABASE_POOL_MAX_IDLE=5            # Maximum idle connections
DATABASE_POOL_MAX_LIFETIME=5m       # Connection max lifetime
```

#### Redis Configuration

```bash
# Redis Cache
REDIS_URL=redis://username:password@host:port/database
REDIS_PASSWORD=your_redis_password
REDIS_DB=0                          # Redis database number
REDIS_MAX_RETRIES=3                 # Maximum retry attempts
REDIS_POOL_SIZE=10                  # Connection pool size
REDIS_MIN_IDLE_CONNS=5              # Minimum idle connections
```

#### Security Configuration

```bash
# JWT Authentication
JWT_SECRET=your_jwt_secret_key_minimum_32_characters
JWT_EXPIRATION_HOURS=24             # JWT token expiration (hours)
JWT_REFRESH_EXPIRATION_HOURS=168    # Refresh token expiration (hours)

# Encryption
ENCRYPTION_KEY=your_32_character_encryption_key_here  # Must be 32 characters
ENCRYPTION_ALGORITHM=AES-GCM        # Encryption algorithm

# Rate Limiting
RATE_LIMIT_ENABLED=true             # Enable rate limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100  # Requests per minute per user
RATE_LIMIT_BURST=20                 # Burst capacity
RATE_LIMIT_ADMIN_MULTIPLIER=10      # Admin rate limit multiplier
```

#### LLM Provider Configuration

```bash
# Provider Settings
DEFAULT_PROVIDER=openai             # Default LLM provider
PROVIDER_TIMEOUT_SECONDS=30         # Provider request timeout
PROVIDER_MAX_RETRIES=3              # Maximum retry attempts
PROVIDER_RETRY_DELAY_SECONDS=1      # Retry delay

# Provider Routing
ENABLE_INTELLIGENT_ROUTING=true     # Enable intelligent routing
ROUTING_STRATEGY=priority           # Routing strategy: priority, round_robin, least_latency
FALLBACK_ENABLED=true               # Enable provider fallback
HEALTH_CHECK_INTERVAL_SECONDS=60    # Provider health check interval
```

#### Monitoring Configuration

```bash
# Monitoring
ENABLE_MONITORING=true              # Enable monitoring
METRICS_RETENTION_DAYS=30           # Metrics retention period
MONITORING_INTERVAL_SECONDS=30      # Monitoring collection interval

# Alerting
ENABLE_ALERTING=true                # Enable alerting
ALERT_EMAIL_ENABLED=true            # Enable email alerts
ALERT_WEBHOOK_ENABLED=true          # Enable webhook alerts
ALERT_SLACK_ENABLED=false           # Enable Slack alerts

# Alert Thresholds
ALERT_CPU_THRESHOLD=80              # CPU usage alert threshold (%)
ALERT_MEMORY_THRESHOLD=85           # Memory usage alert threshold (%)
ALERT_ERROR_RATE_THRESHOLD=5        # Error rate alert threshold (%)
ALERT_RESPONSE_TIME_THRESHOLD=5000  # Response time alert threshold (ms)
```

#### Webhook Configuration

```bash
# Webhook Settings
WEBHOOK_TIMEOUT_SECONDS=30          # Webhook request timeout
WEBHOOK_RETRY_ATTEMPTS=3            # Maximum retry attempts
WEBHOOK_RETRY_DELAY_SECONDS=2       # Retry delay
WEBHOOK_MAX_PAYLOAD_SIZE=1048576    # Maximum payload size (bytes)

# Webhook Security
WEBHOOK_SIGNATURE_ENABLED=true      # Enable webhook signatures
WEBHOOK_SIGNATURE_ALGORITHM=sha256  # Signature algorithm
```

#### Email Configuration

```bash
# SMTP Settings (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=LLM Proxy System

# Email Templates
EMAIL_TEMPLATE_DIR=./templates/email
```

## 🔧 Configuration Files

### Main Configuration File

Create `configs/config.yaml`:

```yaml
server:
  port: 8080
  mode: release
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 120s

database:
  max_connections: 100
  max_idle_connections: 10
  connection_max_lifetime: 1h
  log_level: warn

redis:
  pool_size: 10
  min_idle_conns: 5
  dial_timeout: 5s
  read_timeout: 3s
  write_timeout: 3s

security:
  jwt_expiration: 24h
  refresh_expiration: 168h
  bcrypt_cost: 12
  rate_limit:
    enabled: true
    requests_per_minute: 100
    burst: 20

providers:
  timeout: 30s
  max_retries: 3
  retry_delay: 1s
  health_check_interval: 60s
  
monitoring:
  enabled: true
  interval: 30s
  retention_days: 30
  
webhooks:
  timeout: 30s
  max_retries: 3
  retry_delay: 2s
  max_payload_size: 1MB
```

### Logging Configuration

Create `configs/logging.yaml`:

```yaml
logging:
  level: info
  format: json
  output: stdout
  
  # File logging
  file:
    enabled: true
    path: ./logs/app.log
    max_size: 100MB
    max_backups: 5
    max_age: 30
    compress: true
  
  # Structured logging fields
  fields:
    service: llm-proxy
    version: 1.0.0
    environment: production
```

### Provider Configuration

Create `configs/providers.yaml`:

```yaml
providers:
  openai:
    name: OpenAI
    base_url: https://api.openai.com/v1
    priority: 1
    enabled: true
    models:
      - gpt-3.5-turbo
      - gpt-4
      - gpt-4-turbo-preview
    rate_limits:
      requests_per_minute: 3500
      tokens_per_minute: 90000
  
  gemini:
    name: Google Gemini
    base_url: https://generativelanguage.googleapis.com/v1
    priority: 2
    enabled: true
    models:
      - gemini-pro
      - gemini-pro-vision
    rate_limits:
      requests_per_minute: 60
      tokens_per_minute: 32000
  
  claude:
    name: Anthropic Claude
    base_url: https://api.anthropic.com/v1
    priority: 3
    enabled: true
    models:
      - claude-3-sonnet-********
      - claude-3-opus-********
      - claude-3-haiku-********
    rate_limits:
      requests_per_minute: 1000
      tokens_per_minute: 80000

routing:
  strategy: priority
  fallback_enabled: true
  health_check_enabled: true
  load_balancing: false
```

## 🌍 Environment-Specific Configurations

### Development Configuration

`.env.development`:

```bash
# Development Environment
ENVIRONMENT=development
GIN_MODE=debug
LOG_LEVEL=debug
PORT=8080

# Local Database
DATABASE_URL=postgresql://llmproxy:password@localhost:5432/llmproxy_dev?sslmode=disable
REDIS_URL=redis://localhost:6379

# Development Security (less secure for convenience)
JWT_SECRET=dev_jwt_secret_key_minimum_32_characters
ENCRYPTION_KEY=dev_encryption_key_32_characters_

# Relaxed Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=1000
RATE_LIMIT_BURST=100

# Development Features
ENABLE_MONITORING=true
ENABLE_ALERTING=false
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
```

### Production Configuration

`.env.production`:

```bash
# Production Environment
ENVIRONMENT=production
GIN_MODE=release
LOG_LEVEL=info
PORT=8080

# Production Database with SSL
DATABASE_URL=postgresql://llmproxy:${POSTGRES_PASSWORD}@postgres:5432/llmproxy?sslmode=require
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379

# Strong Security
JWT_SECRET=${JWT_SECRET}
ENCRYPTION_KEY=${ENCRYPTION_KEY}

# Production Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Full Monitoring
ENABLE_MONITORING=true
ENABLE_ALERTING=true
METRICS_RETENTION_DAYS=30

# Production CORS
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://api.yourdomain.com
```

### Testing Configuration

`.env.test`:

```bash
# Test Environment
ENVIRONMENT=test
GIN_MODE=test
LOG_LEVEL=error
PORT=8081

# Test Database
DATABASE_URL=postgresql://llmproxy:password@localhost:5432/llmproxy_test?sslmode=disable
REDIS_URL=redis://localhost:6379/1

# Test Security
JWT_SECRET=test_jwt_secret_key_minimum_32_characters
ENCRYPTION_KEY=test_encryption_key_32_characters

# Disabled Features for Testing
ENABLE_MONITORING=false
ENABLE_ALERTING=false
RATE_LIMIT_ENABLED=false
```

## 🔐 Security Configuration

### SSL/TLS Configuration

```bash
# SSL Certificate Paths
SSL_CERT_PATH=/etc/ssl/certs/yourdomain.com.crt
SSL_KEY_PATH=/etc/ssl/private/yourdomain.com.key
SSL_CA_PATH=/etc/ssl/certs/ca-certificates.crt

# TLS Settings
TLS_MIN_VERSION=1.2
TLS_MAX_VERSION=1.3
TLS_CIPHER_SUITES=ECDHE-RSA-AES256-GCM-SHA384,ECDHE-RSA-AES128-GCM-SHA256
```

### Security Headers

```bash
# Security Headers
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE=31536000
HSTS_INCLUDE_SUBDOMAINS=true
HSTS_PRELOAD=true
CONTENT_TYPE_NOSNIFF=true
FRAME_OPTIONS=DENY
XSS_PROTECTION=1; mode=block
REFERRER_POLICY=strict-origin-when-cross-origin
```

## 📊 Monitoring Configuration

### Prometheus Configuration

Create `monitoring/prometheus.yml`:

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'llm-proxy'
    static_configs:
      - targets: ['backend:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### Grafana Configuration

Create `monitoring/grafana/datasources/prometheus.yml`:

```yaml
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
```

## 🔄 Configuration Validation

### Environment Validation Script

Create `scripts/validate-config.sh`:

```bash
#!/bin/bash

echo "Validating LLM Proxy System Configuration..."

# Check required environment variables
required_vars=(
    "DATABASE_URL"
    "REDIS_URL"
    "JWT_SECRET"
    "ENCRYPTION_KEY"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "ERROR: Required environment variable $var is not set"
        exit 1
    fi
done

# Validate JWT secret length
if [ ${#JWT_SECRET} -lt 32 ]; then
    echo "ERROR: JWT_SECRET must be at least 32 characters long"
    exit 1
fi

# Validate encryption key length
if [ ${#ENCRYPTION_KEY} -ne 32 ]; then
    echo "ERROR: ENCRYPTION_KEY must be exactly 32 characters long"
    exit 1
fi

# Test database connection
echo "Testing database connection..."
if ! pg_isready -d "$DATABASE_URL" > /dev/null 2>&1; then
    echo "ERROR: Cannot connect to database"
    exit 1
fi

# Test Redis connection
echo "Testing Redis connection..."
if ! redis-cli -u "$REDIS_URL" ping > /dev/null 2>&1; then
    echo "ERROR: Cannot connect to Redis"
    exit 1
fi

echo "Configuration validation passed!"
```

## 🔧 Configuration Management

### Configuration Loading Priority

1. Environment variables (highest priority)
2. Configuration files (`.yaml`, `.json`)
3. Default values (lowest priority)

### Dynamic Configuration

Some configurations can be updated without restart:

- Rate limiting settings
- Provider priorities
- Alert thresholds
- Monitoring intervals

### Configuration Hot Reload

```bash
# Reload configuration without restart
curl -X POST http://localhost:8080/api/v1/admin/config/reload \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

## 📝 Configuration Examples

### Minimal Production Configuration

```bash
# Essential production settings
DATABASE_URL=********************************/db
REDIS_URL=redis://host:6379
JWT_SECRET=your_32_character_jwt_secret_key
ENCRYPTION_KEY=your_32_character_encryption_key
GIN_MODE=release
ENVIRONMENT=production
```

### High-Performance Configuration

```bash
# Optimized for high throughput
DATABASE_MAX_CONNECTIONS=200
REDIS_POOL_SIZE=50
RATE_LIMIT_REQUESTS_PER_MINUTE=1000
PROVIDER_TIMEOUT_SECONDS=10
MONITORING_INTERVAL_SECONDS=60
```

### Security-Focused Configuration

```bash
# Maximum security settings
JWT_EXPIRATION_HOURS=1
RATE_LIMIT_REQUESTS_PER_MINUTE=50
WEBHOOK_SIGNATURE_ENABLED=true
SECURITY_HEADERS_ENABLED=true
TLS_MIN_VERSION=1.3
```

This configuration guide provides comprehensive settings for all aspects of the LLM Proxy System across different environments and use cases.
