// Package providers provides access to the existing adapter system
// This is a bridge to import and use the existing adapters without modification
package providers

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"sync"

	"llm-proxy-system/pkg/adapters/core"

	// Import real adapter implementations from the external adapters module
	adapterCore "llm-adapters/core"
	"llm-adapters/providers/openai"
	"llm-adapters/providers/gemini"
	"llm-adapters/providers/claude"
	"llm-adapters/providers/perplexity"
	"llm-adapters/providers/mistral"
	"llm-adapters/providers/deepseek"
	"llm-adapters/providers/moonshot"
	"llm-adapters/providers/ollama"
	"llm-adapters/providers/aws"
	"llm-adapters/providers/azure"
	"llm-adapters/providers/ali"
)

// Registry manages adapter instances
type Registry struct {
	adapters map[string]core.Adapter
	mutex    sync.RWMutex
}

// NewRegistry creates a new adapter registry
func NewRegistry() *Registry {
	return &Registry{
		adapters: make(map[string]core.Adapter),
	}
}

// RegisterAdapter registers an adapter with the registry
func (r *Registry) RegisterAdapter(name string, adapter core.Adapter) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.adapters[strings.ToLower(name)] = adapter
}

// GetAdapter retrieves an adapter by name
func (r *Registry) GetAdapter(name string) (core.Adapter, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	adapter, exists := r.adapters[strings.ToLower(name)]
	if !exists {
		return nil, fmt.Errorf("adapter not found: %s", name)
	}
	
	return adapter, nil
}

// ListAdapters returns all registered adapter names
func (r *Registry) ListAdapters() []string {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	names := make([]string, 0, len(r.adapters))
	for name := range r.adapters {
		names = append(names, name)
	}
	
	return names
}

// AdapterBridge wraps external adapters to match internal interface
type AdapterBridge struct {
	adapter adapterCore.Adapter
}

// NewAdapterBridge creates a new adapter bridge
func NewAdapterBridge(adapter adapterCore.Adapter) *AdapterBridge {
	return &AdapterBridge{adapter: adapter}
}

// GetProviderName returns the provider name
func (b *AdapterBridge) GetProviderName() string {
	return b.adapter.GetProviderName()
}

// GetSupportedModels returns the supported models
func (b *AdapterBridge) GetSupportedModels() []string {
	return b.adapter.GetSupportedModels()
}

// ConvertRequest converts the request format
func (b *AdapterBridge) ConvertRequest(ctx context.Context, request *core.OpenAIRequest) (interface{}, error) {
	// Convert from internal core to external core format
	externalRequest := convertToExternalRequest(request)
	return b.adapter.ConvertRequest(ctx, externalRequest)
}

// BuildRequestURL builds the request URL
func (b *AdapterBridge) BuildRequestURL(baseURL string, request *core.OpenAIRequest) (string, error) {
	externalRequest := convertToExternalRequest(request)
	return b.adapter.BuildRequestURL(baseURL, externalRequest)
}

// SetupHeaders sets up headers
func (b *AdapterBridge) SetupHeaders(headers map[string]string, apiKey string) error {
	return b.adapter.SetupHeaders(headers, apiKey)
}

// ExecuteRequest executes the request
func (b *AdapterBridge) ExecuteRequest(ctx context.Context, httpClient *http.Client, url string, headers map[string]string, body []byte) (*http.Response, error) {
	return b.adapter.ExecuteRequest(ctx, httpClient, url, headers, body)
}

// ConvertResponse converts the response
func (b *AdapterBridge) ConvertResponse(ctx context.Context, response *http.Response, isStream bool) (*core.OpenAIResponse, error) {
	externalResponse, err := b.adapter.ConvertResponse(ctx, response, isStream)
	if err != nil {
		return nil, err
	}
	return convertToInternalResponse(externalResponse), nil
}

// HandleError handles errors
func (b *AdapterBridge) HandleError(response *http.Response) error {
	return b.adapter.HandleError(response)
}

// AdapterFactory creates adapters for different providers
type AdapterFactory struct{}

// NewAdapterFactory creates a new adapter factory
func NewAdapterFactory() *AdapterFactory {
	return &AdapterFactory{}
}

// CreateAdapter creates an adapter for the specified provider
func (f *AdapterFactory) CreateAdapter(providerName string, config *core.AdapterConfig) (core.Adapter, error) {
	// Ensure config is not nil
	if config == nil {
		config = &core.AdapterConfig{}
	}

	// Convert config to external format
	externalConfig := convertToExternalConfig(config)

	// Create real adapter implementations and wrap them with bridge
	switch strings.ToLower(providerName) {
	case "openai":
		adapter := openai.NewAdapter(externalConfig)
		return NewAdapterBridge(adapter), nil
	case "gemini":
		adapter := gemini.NewAdapter(externalConfig)
		return NewAdapterBridge(adapter), nil
	case "claude":
		adapter := claude.NewAdapter(externalConfig)
		return NewAdapterBridge(adapter), nil
	case "perplexity":
		adapter := perplexity.NewAdapter(externalConfig)
		return NewAdapterBridge(adapter), nil
	case "mistral":
		adapter := mistral.NewAdapter(externalConfig)
		return NewAdapterBridge(adapter), nil
	case "deepseek":
		adapter := deepseek.NewAdapter(externalConfig)
		return NewAdapterBridge(adapter), nil
	case "moonshot":
		adapter := moonshot.NewAdapter(externalConfig)
		return NewAdapterBridge(adapter), nil
	case "ollama":
		adapter := ollama.NewAdapter(externalConfig)
		return NewAdapterBridge(adapter), nil
	case "aws", "bedrock":
		adapter := aws.NewAdapter(externalConfig)
		return NewAdapterBridge(adapter), nil
	case "azure":
		adapter := azure.NewAdapter(externalConfig)
		return NewAdapterBridge(adapter), nil
	case "ali", "alibaba":
		adapter := ali.NewAdapter(externalConfig)
		return NewAdapterBridge(adapter), nil
	default:
		return nil, fmt.Errorf("unsupported provider: %s", providerName)
	}
}

// GetSupportedProviders returns a list of all supported providers
func (f *AdapterFactory) GetSupportedProviders() []string {
	return []string{
		"openai", "gemini", "claude", "perplexity", "mistral",
		"deepseek", "moonshot", "ollama", "aws", "azure", "ali",
	}
}

// AdapterManager manages automatic routing of requests to appropriate adapters
type AdapterManager struct {
	factory *AdapterFactory
}

// NewAdapterManager creates a new adapter manager
func NewAdapterManager() *AdapterManager {
	return &AdapterManager{
		factory: NewAdapterFactory(),
	}
}

// RouteRequest automatically routes a request to the appropriate adapter based on the model
func (m *AdapterManager) RouteRequest(request *core.OpenAIRequest) (core.Adapter, error) {
	provider := m.detectProviderFromModel(request.Model)
	if provider == "" {
		return nil, fmt.Errorf("unable to determine provider for model: %s", request.Model)
	}
	
	return m.factory.CreateAdapter(provider, &core.AdapterConfig{})
}

// detectProviderFromModel detects the provider based on the model name
func (m *AdapterManager) detectProviderFromModel(model string) string {
	model = strings.ToLower(model)
	
	// OpenAI models
	if strings.Contains(model, "gpt") || strings.Contains(model, "text-embedding") || 
	   strings.Contains(model, "whisper") || strings.Contains(model, "dall-e") {
		return "openai"
	}
	
	// Gemini models
	if strings.Contains(model, "gemini") {
		return "gemini"
	}
	
	// Claude models
	if strings.Contains(model, "claude") {
		return "claude"
	}
	
	// Perplexity models
	if strings.Contains(model, "sonar") || strings.Contains(model, "perplexity") {
		return "perplexity"
	}
	
	// Mistral models
	if strings.Contains(model, "mistral") || strings.Contains(model, "mixtral") {
		return "mistral"
	}
	
	// Deepseek models
	if strings.Contains(model, "deepseek") {
		return "deepseek"
	}
	
	// Moonshot models
	if strings.Contains(model, "moonshot") {
		return "moonshot"
	}
	
	// Ollama models (common open source models)
	if strings.Contains(model, "llama") || strings.Contains(model, "phi") || 
	   strings.Contains(model, "gemma") || strings.Contains(model, "qwen") {
		return "ollama"
	}
	
	// AWS Bedrock models
	if strings.Contains(model, "titan") || strings.Contains(model, "amazon") {
		return "aws"
	}
	
	// Azure models (usually same as OpenAI but with different naming)
	if strings.Contains(model, "azure") {
		return "azure"
	}
	
	// Alibaba models
	if strings.Contains(model, "qwen") && !strings.Contains(model, "llama") {
		return "ali"
	}
	
	// Default to OpenAI for unknown models
	return "openai"
}

// DefaultRegistry creates a registry with all built-in adapters
func DefaultRegistry() *Registry {
	registry := NewRegistry()
	factory := NewAdapterFactory()
	
	// Register all supported providers with default configurations
	supportedProviders := factory.GetSupportedProviders()
	for _, provider := range supportedProviders {
		adapter, err := factory.CreateAdapter(provider, &core.AdapterConfig{})
		if err == nil {
			registry.RegisterAdapter(provider, adapter)
		}
	}

	return registry
}

// Conversion functions between internal and external core types

// convertToExternalConfig converts internal config to external config
func convertToExternalConfig(config *core.AdapterConfig) *adapterCore.AdapterConfig {
	return &adapterCore.AdapterConfig{
		APIKey:      config.APIKey,
		BaseURL:     config.BaseURL,
		HTTPClient:  config.HTTPClient,
		ExtraParams: config.ExtraParams,
	}
}

// convertToExternalRequest converts internal request to external request
func convertToExternalRequest(req *core.OpenAIRequest) *adapterCore.OpenAIRequest {
	if req == nil {
		return nil
	}

	// Convert messages
	var messages []adapterCore.Message
	for _, msg := range req.Messages {
		messages = append(messages, adapterCore.Message{
			Role:    msg.Role,
			Content: msg.Content,
			Name:    msg.Name,
		})
	}

	return &adapterCore.OpenAIRequest{
		Model:            req.Model,
		Messages:         messages,
		Prompt:           req.Prompt,
		MaxTokens:        req.MaxTokens,
		Temperature:      req.Temperature,
		TopP:             req.TopP,
		TopK:             req.TopK,
		Stream:           req.Stream,
		Stop:             req.Stop,
		FrequencyPenalty: req.FrequencyPenalty,
		PresencePenalty:  req.PresencePenalty,
		N:                req.N,
		User:             req.User,
		Seed:             req.Seed,
	}
}

// convertToInternalResponse converts external response to internal response
func convertToInternalResponse(resp *adapterCore.OpenAIResponse) *core.OpenAIResponse {
	if resp == nil {
		return nil
	}

	// Convert choices
	var choices []core.Choice
	for _, choice := range resp.Choices {
		var message *core.Message
		if choice.Message != nil {
			message = &core.Message{
				Role:    choice.Message.Role,
				Content: choice.Message.Content,
				Name:    choice.Message.Name,
			}
		}

		choices = append(choices, core.Choice{
			Index:        choice.Index,
			Message:      message,
			FinishReason: choice.FinishReason,
		})
	}

	// Convert usage
	var usage *core.Usage
	if resp.Usage != nil {
		usage = &core.Usage{
			PromptTokens:     resp.Usage.PromptTokens,
			CompletionTokens: resp.Usage.CompletionTokens,
			TotalTokens:      resp.Usage.TotalTokens,
		}
	}

	return &core.OpenAIResponse{
		ID:                resp.ID,
		Object:            resp.Object,
		Created:           resp.Created,
		Model:             resp.Model,
		Choices:           choices,
		Usage:             usage,
		SystemFingerprint: resp.SystemFingerprint,
	}
}