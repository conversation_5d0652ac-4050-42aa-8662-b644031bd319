// Package providers provides access to the existing adapter system
// This is a bridge to import and use the existing adapters without modification
package providers

import (
	"fmt"
	"strings"
	"sync"

	"llm-proxy-system/pkg/adapters/core"
)

// Registry manages adapter instances
type Registry struct {
	adapters map[string]core.Adapter
	mutex    sync.RWMutex
}

// NewRegistry creates a new adapter registry
func NewRegistry() *Registry {
	return &Registry{
		adapters: make(map[string]core.Adapter),
	}
}

// RegisterAdapter registers an adapter with the registry
func (r *Registry) RegisterAdapter(name string, adapter core.Adapter) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.adapters[strings.ToLower(name)] = adapter
}

// GetAdapter retrieves an adapter by name
func (r *Registry) GetAdapter(name string) (core.Adapter, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	adapter, exists := r.adapters[strings.ToLower(name)]
	if !exists {
		return nil, fmt.Errorf("adapter not found: %s", name)
	}
	
	return adapter, nil
}

// ListAdapters returns all registered adapter names
func (r *Registry) ListAdapters() []string {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	names := make([]string, 0, len(r.adapters))
	for name := range r.adapters {
		names = append(names, name)
	}
	
	return names
}

// AdapterFactory creates adapters for different providers
type AdapterFactory struct{}

// NewAdapterFactory creates a new adapter factory
func NewAdapterFactory() *AdapterFactory {
	return &AdapterFactory{}
}

// CreateAdapter creates an adapter for the specified provider
func (f *AdapterFactory) CreateAdapter(providerName string, config *core.AdapterConfig) (core.Adapter, error) {
	// This is where we would integrate with the existing adapter system
	// For now, we'll create a mock adapter that implements the interface
	
	switch strings.ToLower(providerName) {
	case "openai":
		return NewMockAdapter("openai", []string{"gpt-4", "gpt-3.5-turbo", "text-embedding-ada-002"}), nil
	case "gemini":
		return NewMockAdapter("gemini", []string{"gemini-1.5-pro", "gemini-1.5-flash", "text-embedding-004"}), nil
	case "claude":
		return NewMockAdapter("claude", []string{"claude-3-5-sonnet-20241022", "claude-3-opus-20240229", "claude-3-haiku-20240307"}), nil
	case "perplexity":
		return NewMockAdapter("perplexity", []string{"llama-3.1-sonar-large-128k-online", "llama-3.1-sonar-small-128k-online"}), nil
	case "mistral":
		return NewMockAdapter("mistral", []string{"mistral-large-latest", "mistral-small-latest", "mistral-embed"}), nil
	case "deepseek":
		return NewMockAdapter("deepseek", []string{"deepseek-chat", "deepseek-coder"}), nil
	case "moonshot":
		return NewMockAdapter("moonshot", []string{"moonshot-v1-8k", "moonshot-v1-32k", "moonshot-v1-128k"}), nil
	case "ollama":
		return NewMockAdapter("ollama", []string{"llama3.2", "mistral", "gemma2"}), nil
	case "aws", "bedrock":
		return NewMockAdapter("aws", []string{"claude-3-sonnet-20240229", "titan-text-express-v1"}), nil
	case "azure":
		return NewMockAdapter("azure", []string{"gpt-4", "gpt-35-turbo", "text-embedding-ada-002"}), nil
	case "ali", "alibaba":
		return NewMockAdapter("ali", []string{"qwen-turbo", "qwen-plus", "qwen-max"}), nil
	default:
		return nil, fmt.Errorf("unsupported provider: %s", providerName)
	}
}

// GetSupportedProviders returns a list of all supported providers
func (f *AdapterFactory) GetSupportedProviders() []string {
	return []string{
		"openai", "gemini", "claude", "perplexity", "mistral",
		"deepseek", "moonshot", "ollama", "aws", "azure", "ali",
	}
}

// AdapterManager manages automatic routing of requests to appropriate adapters
type AdapterManager struct {
	factory *AdapterFactory
}

// NewAdapterManager creates a new adapter manager
func NewAdapterManager() *AdapterManager {
	return &AdapterManager{
		factory: NewAdapterFactory(),
	}
}

// RouteRequest automatically routes a request to the appropriate adapter based on the model
func (m *AdapterManager) RouteRequest(request *core.OpenAIRequest) (core.Adapter, error) {
	provider := m.detectProviderFromModel(request.Model)
	if provider == "" {
		return nil, fmt.Errorf("unable to determine provider for model: %s", request.Model)
	}
	
	return m.factory.CreateAdapter(provider, &core.AdapterConfig{})
}

// detectProviderFromModel detects the provider based on the model name
func (m *AdapterManager) detectProviderFromModel(model string) string {
	model = strings.ToLower(model)
	
	// OpenAI models
	if strings.Contains(model, "gpt") || strings.Contains(model, "text-embedding") || 
	   strings.Contains(model, "whisper") || strings.Contains(model, "dall-e") {
		return "openai"
	}
	
	// Gemini models
	if strings.Contains(model, "gemini") {
		return "gemini"
	}
	
	// Claude models
	if strings.Contains(model, "claude") {
		return "claude"
	}
	
	// Perplexity models
	if strings.Contains(model, "sonar") || strings.Contains(model, "perplexity") {
		return "perplexity"
	}
	
	// Mistral models
	if strings.Contains(model, "mistral") || strings.Contains(model, "mixtral") {
		return "mistral"
	}
	
	// Deepseek models
	if strings.Contains(model, "deepseek") {
		return "deepseek"
	}
	
	// Moonshot models
	if strings.Contains(model, "moonshot") {
		return "moonshot"
	}
	
	// Ollama models (common open source models)
	if strings.Contains(model, "llama") || strings.Contains(model, "phi") || 
	   strings.Contains(model, "gemma") || strings.Contains(model, "qwen") {
		return "ollama"
	}
	
	// AWS Bedrock models
	if strings.Contains(model, "titan") || strings.Contains(model, "amazon") {
		return "aws"
	}
	
	// Azure models (usually same as OpenAI but with different naming)
	if strings.Contains(model, "azure") {
		return "azure"
	}
	
	// Alibaba models
	if strings.Contains(model, "qwen") && !strings.Contains(model, "llama") {
		return "ali"
	}
	
	// Default to OpenAI for unknown models
	return "openai"
}

// DefaultRegistry creates a registry with all built-in adapters
func DefaultRegistry() *Registry {
	registry := NewRegistry()
	factory := NewAdapterFactory()
	
	// Register all supported providers with default configurations
	supportedProviders := factory.GetSupportedProviders()
	for _, provider := range supportedProviders {
		adapter, err := factory.CreateAdapter(provider, &core.AdapterConfig{})
		if err == nil {
			registry.RegisterAdapter(provider, adapter)
		}
	}
	
	return registry
}

// MockAdapter is a placeholder adapter for testing and development
// In production, this would be replaced with actual adapter implementations
type MockAdapter struct {
	providerName    string
	supportedModels []string
}

// NewMockAdapter creates a new mock adapter
func NewMockAdapter(providerName string, models []string) *MockAdapter {
	return &MockAdapter{
		providerName:    providerName,
		supportedModels: models,
	}
}

// GetProviderName returns the provider name
func (m *MockAdapter) GetProviderName() string {
	return m.providerName
}

// GetSupportedModels returns the supported models
func (m *MockAdapter) GetSupportedModels() []string {
	return m.supportedModels
}

// ConvertRequest converts an OpenAI request (mock implementation)
func (m *MockAdapter) ConvertRequest(ctx context.Context, request *core.OpenAIRequest) (interface{}, error) {
	// Mock implementation - just return the request as-is
	return request, nil
}

// BuildRequestURL builds the request URL (mock implementation)
func (m *MockAdapter) BuildRequestURL(baseURL string, request *core.OpenAIRequest) (string, error) {
	if baseURL == "" {
		baseURL = "https://api.example.com"
	}
	return core.BuildURL(baseURL, "/v1/chat/completions"), nil
}

// SetupHeaders sets up headers (mock implementation)
func (m *MockAdapter) SetupHeaders(headers map[string]string, apiKey string) error {
	if headers == nil {
		headers = make(map[string]string)
	}
	headers["Content-Type"] = "application/json"
	headers["Authorization"] = "Bearer " + apiKey
	return nil
}

// ExecuteRequest executes the request (mock implementation)
func (m *MockAdapter) ExecuteRequest(ctx context.Context, httpClient *http.Client, url string, headers map[string]string, body []byte) (*http.Response, error) {
	// Mock implementation - return a mock response
	return &http.Response{
		StatusCode: 200,
		Header:     make(http.Header),
		Body:       io.NopCloser(strings.NewReader(`{"choices":[{"message":{"role":"assistant","content":"Mock response"}}]}`)),
	}, nil
}

// ConvertResponse converts the response (mock implementation)
func (m *MockAdapter) ConvertResponse(ctx context.Context, response *http.Response, isStream bool) (*core.OpenAIResponse, error) {
	// Mock implementation
	return &core.OpenAIResponse{
		ID:      "mock-response-id",
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   "mock-model",
		Choices: []core.Choice{
			{
				Index: 0,
				Message: &core.Message{
					Role:    "assistant",
					Content: json.RawMessage(`"Mock response from ` + m.providerName + `"`),
				},
				FinishReason: stringPtr("stop"),
			},
		},
	}, nil
}

// HandleError handles errors (mock implementation)
func (m *MockAdapter) HandleError(response *http.Response) error {
	return core.NewAdapterError(core.ErrorTypeServerError, "Mock error", response.StatusCode, m.providerName)
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

// Required imports
import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"time"
)
