// Package core provides the adapter interfaces and types
// This is a bridge to the existing adapter system
package core

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// Adapter interface defines the contract for all LLM provider adapters
type Adapter interface {
	// GetProviderName returns the name of the provider
	GetProviderName() string
	
	// GetSupportedModels returns a list of models supported by this provider
	GetSupportedModels() []string
	
	// ConvertRequest converts an OpenAI-compatible request to the provider's format
	ConvertRequest(ctx context.Context, request *OpenAIRequest) (interface{}, error)
	
	// BuildRequestURL builds the complete request URL for the provider
	BuildRequestURL(baseURL string, request *OpenAIRequest) (string, error)
	
	// SetupHeaders sets up the necessary headers for the request
	SetupHeaders(headers map[string]string, apiKey string) error
	
	// ExecuteRequest executes the HTTP request to the provider
	ExecuteRequest(ctx context.Context, httpClient *http.Client, url string, headers map[string]string, body []byte) (*http.Response, error)
	
	// ConvertResponse converts the provider's response to OpenAI format
	ConvertResponse(ctx context.Context, response *http.Response, isStream bool) (*OpenAIResponse, error)
	
	// HandleError handles provider-specific errors
	HandleError(response *http.Response) error
}

// AdapterConfig holds configuration for an adapter
type AdapterConfig struct {
	APIKey      string                 `json:"api_key"`
	BaseURL     string                 `json:"base_url"`
	HTTPClient  *http.Client           `json:"-"`
	ExtraParams map[string]interface{} `json:"extra_params,omitempty"`
}

// OpenAIRequest represents an OpenAI-compatible request
type OpenAIRequest struct {
	Model            string      `json:"model"`
	Messages         []Message   `json:"messages,omitempty"`
	Prompt           interface{} `json:"prompt,omitempty"`
	MaxTokens        *int        `json:"max_tokens,omitempty"`
	Temperature      *float64    `json:"temperature,omitempty"`
	TopP             *float64    `json:"top_p,omitempty"`
	TopK             *int        `json:"top_k,omitempty"`
	Stream           bool        `json:"stream,omitempty"`
	Stop             interface{} `json:"stop,omitempty"`
	FrequencyPenalty *float64    `json:"frequency_penalty,omitempty"`
	PresencePenalty  *float64    `json:"presence_penalty,omitempty"`
	N                *int        `json:"n,omitempty"`
	Tools            []Tool      `json:"tools,omitempty"`
	ToolChoice       interface{} `json:"tool_choice,omitempty"`
	Functions        []Function  `json:"functions,omitempty"`
	FunctionCall     interface{} `json:"function_call,omitempty"`
	User             string      `json:"user,omitempty"`
	Seed             *int        `json:"seed,omitempty"`
}

// OpenAIResponse represents an OpenAI-compatible response
type OpenAIResponse struct {
	ID                string   `json:"id"`
	Object            string   `json:"object"`
	Created           int64    `json:"created"`
	Model             string   `json:"model"`
	Choices           []Choice `json:"choices"`
	Usage             *Usage   `json:"usage,omitempty"`
	SystemFingerprint string   `json:"system_fingerprint,omitempty"`
}

// Message represents a chat message
type Message struct {
	Role         string          `json:"role"`
	Content      json.RawMessage `json:"content"`
	Name         string          `json:"name,omitempty"`
	ToolCalls    []ToolCall      `json:"tool_calls,omitempty"`
	ToolCallID   string          `json:"tool_call_id,omitempty"`
	FunctionCall *FunctionCall   `json:"function_call,omitempty"`
}

// GetStringContent returns the content as a string
func (m *Message) GetStringContent() string {
	var content string
	if err := json.Unmarshal(m.Content, &content); err != nil {
		return string(m.Content)
	}
	return content
}

// Choice represents a response choice
type Choice struct {
	Index        int      `json:"index"`
	Message      *Message `json:"message,omitempty"`
	Delta        *Message `json:"delta,omitempty"`
	FinishReason *string  `json:"finish_reason"`
	Logprobs     *Logprobs `json:"logprobs,omitempty"`
}

// Usage represents token usage information
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// Tool represents a function tool
type Tool struct {
	Type     string   `json:"type"`
	Function Function `json:"function"`
}

// Function represents a function definition
type Function struct {
	Name        string      `json:"name"`
	Description string      `json:"description,omitempty"`
	Parameters  interface{} `json:"parameters,omitempty"`
}

// ToolCall represents a tool call
type ToolCall struct {
	ID       string       `json:"id"`
	Type     string       `json:"type"`
	Function FunctionCall `json:"function"`
}

// FunctionCall represents a function call
type FunctionCall struct {
	Name      string `json:"name"`
	Arguments string `json:"arguments"`
}

// Logprobs represents log probabilities
type Logprobs struct {
	Content []TokenLogprob `json:"content,omitempty"`
}

// TokenLogprob represents token log probability
type TokenLogprob struct {
	Token   string  `json:"token"`
	Logprob float64 `json:"logprob"`
	Bytes   []int   `json:"bytes,omitempty"`
}

// Error types for consistent error handling
type ErrorType string

const (
	ErrorTypeInvalidRequest     ErrorType = "invalid_request"
	ErrorTypeAuthentication     ErrorType = "authentication"
	ErrorTypePermission         ErrorType = "permission"
	ErrorTypeNotFound           ErrorType = "not_found"
	ErrorTypeRateLimit          ErrorType = "rate_limit"
	ErrorTypeQuotaExceeded      ErrorType = "quota_exceeded"
	ErrorTypeServerError        ErrorType = "server_error"
	ErrorTypeServiceUnavailable ErrorType = "service_unavailable"
	ErrorTypeTimeout            ErrorType = "timeout"
	ErrorTypeUnknown            ErrorType = "unknown"
)

// AdapterError represents an adapter-specific error
type AdapterError struct {
	Type       ErrorType `json:"type"`
	Message    string    `json:"message"`
	StatusCode int       `json:"status_code"`
	Provider   string    `json:"provider"`
	Cause      error     `json:"-"`
}

func (e *AdapterError) Error() string {
	return fmt.Sprintf("[%s] %s: %s", e.Provider, e.Type, e.Message)
}

// NewAdapterError creates a new adapter error
func NewAdapterError(errorType ErrorType, message string, statusCode int, provider string) *AdapterError {
	return &AdapterError{
		Type:       errorType,
		Message:    message,
		StatusCode: statusCode,
		Provider:   provider,
	}
}

// WrapError wraps an existing error as an adapter error
func WrapError(err error, errorType ErrorType, message string, statusCode int, provider string) *AdapterError {
	return &AdapterError{
		Type:       errorType,
		Message:    message,
		StatusCode: statusCode,
		Provider:   provider,
		Cause:      err,
	}
}

// MapHTTPStatusToErrorType maps HTTP status codes to error types
func MapHTTPStatusToErrorType(statusCode int) ErrorType {
	switch statusCode {
	case 400:
		return ErrorTypeInvalidRequest
	case 401:
		return ErrorTypeAuthentication
	case 403:
		return ErrorTypePermission
	case 404:
		return ErrorTypeNotFound
	case 429:
		return ErrorTypeRateLimit
	case 500:
		return ErrorTypeServerError
	case 502, 503:
		return ErrorTypeServiceUnavailable
	case 504:
		return ErrorTypeTimeout
	default:
		return ErrorTypeUnknown
	}
}

// IsSuccessStatusCode checks if a status code indicates success
func IsSuccessStatusCode(statusCode int) bool {
	return statusCode >= 200 && statusCode < 300
}

// BuildURL builds a complete URL from base URL and endpoint
func BuildURL(baseURL, endpoint string) string {
	if baseURL == "" {
		return endpoint
	}
	if endpoint == "" {
		return baseURL
	}
	
	// Remove trailing slash from base URL
	if baseURL[len(baseURL)-1] == '/' {
		baseURL = baseURL[:len(baseURL)-1]
	}
	
	// Add leading slash to endpoint if missing
	if endpoint[0] != '/' {
		endpoint = "/" + endpoint
	}
	
	return baseURL + endpoint
}

// ParseJSONResponse parses a JSON response into the given interface
func ParseJSONResponse(response *http.Response, v interface{}) error {
	defer response.Body.Close()
	return json.NewDecoder(response.Body).Decode(v)
}

// ReadResponseBody reads the response body as bytes
func ReadResponseBody(response *http.Response) ([]byte, error) {
	defer response.Body.Close()
	return io.ReadAll(response.Body)
}

// HTTPClient provides HTTP client functionality
type HTTPClient struct {
	client *http.Client
}

// NewHTTPClient creates a new HTTP client
func NewHTTPClient() *HTTPClient {
	return &HTTPClient{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// NewHTTPClientWithClient creates a new HTTP client with a custom http.Client
func NewHTTPClientWithClient(client *http.Client) *HTTPClient {
	return &HTTPClient{
		client: client,
	}
}

// DoRequest executes an HTTP request
func (c *HTTPClient) DoRequest(ctx context.Context, method, url string, headers map[string]string, body []byte) (*http.Response, error) {
	var reqBody io.Reader
	if body != nil {
		reqBody = bytes.NewReader(body)
	}
	
	req, err := http.NewRequestWithContext(ctx, method, url, reqBody)
	if err != nil {
		return nil, err
	}
	
	// Set headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}
	
	return c.client.Do(req)
}
