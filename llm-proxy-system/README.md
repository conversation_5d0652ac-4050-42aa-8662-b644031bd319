# LLM Proxy System

A comprehensive multi-provider LLM proxy system with OpenAI-compatible API interface. This system provides a unified endpoint that routes requests to multiple LLM providers (OpenAI, Gemini, Claude, and 12+ others) with intelligent fallback, real-time monitoring, and enterprise-grade features.

## 🎯 Core Features

- **OpenAI-Compatible API**: Single endpoint (`/api/v1/llm/proxy`) that accepts OpenAI JSON format
- **Multi-Provider Support**: Integrates with 12+ LLM providers via modular adapter system
- **Intelligent Routing**: Priority-based provider selection with automatic fallback
- **Real-time Monitoring**: Key health checks, status updates, and webhook notifications
- **Enterprise Security**: JWT authentication, encrypted key storage, rate limiting
- **Admin Management**: Web interface for routing rules and provider key management
- **Streaming Support**: Server-Sent Events for real-time response streaming

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Providers     │
│   (Next.js)     │◄──►│   (Go/Gin)      │◄──►│   (Adapters)    │
│                 │    │                 │    │                 │
│ • Key Mgmt UI   │    │ • Proxy Service │    │ • OpenAI        │
│ • Admin Panel   │    │ • Auth System   │    │ • Gemini        │
│ • Real-time     │    │ • Routing Engine│    │ • Claude        │
│   Updates       │    │ • Webhooks      │    │ • 12+ Others    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Database      │
                    │   (PostgreSQL)  │
                    │                 │
                    │ • Users         │
                    │ • API Keys      │
                    │ • Routing Rules │
                    │ • Usage Logs    │
                    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Go 1.21+
- Node.js 18+
- PostgreSQL 15+
- Redis 7+

### Development Setup

1. **Clone and setup**:
```bash
git clone <repository>
cd llm-proxy-system
```

2. **Backend setup**:
```bash
cd backend
go mod download
cp .env.example .env
# Configure your database and Redis connections in .env
go run cmd/server/main.go
```

3. **Frontend setup**:
```bash
cd frontend
npm install
cp .env.local.example .env.local
# Configure API endpoints in .env.local
npm run dev
```

4. **Database setup**:
```bash
# Run migrations
go run cmd/migrate/main.go
```

### Using Docker Compose

```bash
docker-compose up -d
```

This starts all services including PostgreSQL, Redis, backend, and frontend.

## 📚 API Documentation

### Main Proxy Endpoint

**POST** `/api/v1/llm/proxy`

OpenAI-compatible endpoint that routes to configured providers:

```bash
curl -X POST https://api.yourdomain.com/api/v1/llm/proxy \
  -H "Authorization: Bearer sk-sys_your_system_key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {"role": "user", "content": "Hello, world!"}
    ],
    "temperature": 0.7,
    "stream": false
  }'
```

### Authentication Endpoints

- `POST /auth/register` - Register new user
- `POST /auth/login` - User login
- `POST /auth/refresh` - Refresh JWT token
- `GET /auth/me` - Get current user info

### Key Management Endpoints

- `GET /api/users/me/keys` - List user's provider keys
- `POST /api/users/me/keys` - Add new provider key
- `PUT /api/users/me/keys/{id}` - Update provider key
- `DELETE /api/users/me/keys/{id}` - Delete provider key
- `POST /api/users/me/keys/test` - Test provider key

### Admin Endpoints

- `GET /api/admin/routing-rules` - List routing rules
- `POST /api/admin/routing-rules` - Create routing rule
- `PUT /api/admin/routing-rules/reorder` - Reorder routing priorities

## 🔧 Configuration

### Environment Variables

**Backend (.env)**:
```env
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/llmproxy
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h

# Server
PORT=8080
GIN_MODE=release

# Encryption
ENCRYPTION_KEY=your-32-byte-encryption-key
```

**Frontend (.env.local)**:
```env
NEXT_PUBLIC_API_URL=http://localhost:8080
NEXT_PUBLIC_WS_URL=ws://localhost:8080
```

## 🏢 Provider Integration

This system leverages our comprehensive adapter system supporting 12+ providers:

- **OpenAI**: GPT-4, GPT-3.5, embeddings, DALL-E
- **Google Gemini**: Gemini 1.5 Pro/Flash, embeddings
- **Anthropic Claude**: Claude 3.5 Sonnet, Opus, Haiku
- **Perplexity**: Sonar models with online search
- **Mistral**: Mistral Large, Mixtral, Codestral
- **Deepseek**: Chat, Coder, R1 reasoning models
- **AWS Bedrock**: Enterprise Claude, Titan, Llama
- **Azure OpenAI**: Enterprise GPT models
- **And more**: Moonshot, Ollama, Alibaba Cloud

## 🔐 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Encrypted Storage**: Provider API keys encrypted at rest
- **Rate Limiting**: 60 requests/minute per system API key
- **Input Validation**: Comprehensive request validation
- **HTTPS Enforcement**: TLS required for all endpoints
- **CORS Protection**: Configurable CORS policies

## 📊 Monitoring & Observability

- **Health Checks**: `/healthz` endpoint for liveness probes
- **Metrics**: Prometheus metrics at `/metrics`
- **Logging**: Structured JSON logging
- **Tracing**: Distributed tracing support
- **Webhooks**: Real-time notifications for key status changes

## 🚀 Deployment

### Kubernetes

```bash
# Apply Kubernetes manifests
kubectl apply -f deployments/k8s/

# Or use Helm
helm install llm-proxy deployments/helm/llm-proxy
```

### Docker

```bash
# Build images
docker build -t llm-proxy-backend ./backend
docker build -t llm-proxy-frontend ./frontend

# Run with docker-compose
docker-compose -f docker-compose.prod.yml up -d
```

## 🧪 Testing

```bash
# Backend tests
cd backend
go test ./...

# Frontend tests
cd frontend
npm test

# Integration tests
npm run test:integration

# Load tests
npm run test:load
```

## 📖 Documentation

- [API Documentation](./docs/api.md)
- [Deployment Guide](./docs/deployment.md)
- [Configuration Reference](./docs/configuration.md)
- [Provider Integration](./docs/providers.md)
- [Security Guide](./docs/security.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- [Documentation](./docs/)
- [Issue Tracker](https://github.com/your-org/llm-proxy-system/issues)
- [Discussions](https://github.com/your-org/llm-proxy-system/discussions)
