package proxy

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/database"
	"llm-proxy-system/internal/logger"
	"llm-proxy-system/internal/routing"
	"llm-proxy-system/pkg/adapters/core"

	"gorm.io/gorm"
)

// Service handles LLM proxy requests
type Service struct {
	db             *gorm.DB
	config         *config.Config
	routingService *routing.Service
	usageRepo      *database.UsageLogRepository
}

// NewService creates a new proxy service
func NewService(db *gorm.DB, cfg *config.Config, routingService *routing.Service) *Service {
	return &Service{
		db:             db,
		config:         cfg,
		routingService: routingService,
		usageRepo:      database.NewUsageLogRepository(db),
	}
}

// ProxyRequest represents an OpenAI-compatible LLM request
type ProxyRequest struct {
	Model            string                 `json:"model" binding:"required"`
	Messages         []core.Message         `json:"messages,omitempty"`
	Prompt           interface{}            `json:"prompt,omitempty"`
	MaxTokens        *int                   `json:"max_tokens,omitempty"`
	Temperature      *float64               `json:"temperature,omitempty"`
	TopP             *float64               `json:"top_p,omitempty"`
	TopK             *int                   `json:"top_k,omitempty"`
	Stream           bool                   `json:"stream,omitempty"`
	Stop             interface{}            `json:"stop,omitempty"`
	FrequencyPenalty *float64               `json:"frequency_penalty,omitempty"`
	PresencePenalty  *float64               `json:"presence_penalty,omitempty"`
	N                *int                   `json:"n,omitempty"`
	Tools            []core.Tool            `json:"tools,omitempty"`
	ToolChoice       interface{}            `json:"tool_choice,omitempty"`
	Functions        []core.Function        `json:"functions,omitempty"`
	FunctionCall     interface{}            `json:"function_call,omitempty"`
	User             string                 `json:"user,omitempty"`
	Seed             *int                   `json:"seed,omitempty"`
	
	// Additional metadata
	Provider     string   `json:"provider,omitempty"`     // Optional: force specific provider
	RequiredCaps []string `json:"required_caps,omitempty"` // Optional: required capabilities
}

// ProxyResponse represents the response from the proxy service
type ProxyResponse struct {
	*core.OpenAIResponse
	
	// Additional metadata
	Provider     string        `json:"_provider,omitempty"`
	Duration     time.Duration `json:"_duration,omitempty"`
	TokensUsed   int           `json:"_tokens_used,omitempty"`
	RequestID    string        `json:"_request_id,omitempty"`
	Fallbacks    []string      `json:"_fallbacks,omitempty"`
}

// ProcessRequest processes an LLM request through the proxy
func (s *Service) ProcessRequest(ctx context.Context, userID uint, req *ProxyRequest, requestID, ipAddress, userAgent string) (*ProxyResponse, error) {
	start := time.Now()
	
	logger.Info("Processing LLM request", "user_id", userID, "model", req.Model, "request_id", requestID)
	
	// Create routing request
	routingReq := &routing.RoutingRequest{
		UserID:       userID,
		Model:        req.Model,
		Provider:     req.Provider,
		RequiredCaps: req.RequiredCaps,
	}
	
	// Route the request to appropriate provider
	routingResult, err := s.routingService.RouteRequest(ctx, routingReq)
	if err != nil {
		s.logUsage(userID, 0, req.Model, "", requestID, "error", err.Error(), ipAddress, userAgent, time.Since(start), 0, 0, 0)
		return nil, fmt.Errorf("routing failed: %w", err)
	}
	
	logger.Info("Request routed successfully", "provider", routingResult.Provider, "priority", routingResult.Priority, "user_id", userID)
	
	// Convert proxy request to OpenAI format
	openAIReq := s.convertToOpenAIRequest(req)
	
	// Execute the request through the adapter
	response, err := s.executeRequest(ctx, routingResult.Adapter, openAIReq, req.Stream)
	if err != nil {
		s.logUsage(userID, routingResult.UserAPIKeyID, req.Model, routingResult.Provider, requestID, "error", err.Error(), ipAddress, userAgent, time.Since(start), 0, 0, 0)
		return nil, fmt.Errorf("request execution failed: %w", err)
	}
	
	// Calculate token usage
	promptTokens, completionTokens, totalTokens := s.calculateTokenUsage(response)
	
	// Log successful usage
	s.logUsage(userID, routingResult.UserAPIKeyID, req.Model, routingResult.Provider, requestID, "success", "", ipAddress, userAgent, time.Since(start), promptTokens, completionTokens, totalTokens)
	
	// Create proxy response
	proxyResponse := &ProxyResponse{
		OpenAIResponse: response,
		Provider:       routingResult.Provider,
		Duration:       time.Since(start),
		TokensUsed:     totalTokens,
		RequestID:      requestID,
		Fallbacks:      routingResult.Fallbacks,
	}
	
	logger.Info("Request processed successfully", "provider", routingResult.Provider, "tokens", totalTokens, "duration_ms", time.Since(start).Milliseconds())
	
	return proxyResponse, nil
}

// ProcessStreamRequest processes a streaming LLM request
func (s *Service) ProcessStreamRequest(ctx context.Context, userID uint, req *ProxyRequest, requestID, ipAddress, userAgent string) (<-chan *ProxyResponse, <-chan error, error) {
	start := time.Now()
	
	logger.Info("Processing streaming LLM request", "user_id", userID, "model", req.Model, "request_id", requestID)
	
	// Create routing request
	routingReq := &routing.RoutingRequest{
		UserID:       userID,
		Model:        req.Model,
		Provider:     req.Provider,
		RequiredCaps: req.RequiredCaps,
	}
	
	// Route the request to appropriate provider
	routingResult, err := s.routingService.RouteRequest(ctx, routingReq)
	if err != nil {
		s.logUsage(userID, 0, req.Model, "", requestID, "error", err.Error(), ipAddress, userAgent, time.Since(start), 0, 0, 0)
		return nil, nil, fmt.Errorf("routing failed: %w", err)
	}
	
	// Convert proxy request to OpenAI format
	openAIReq := s.convertToOpenAIRequest(req)
	openAIReq.Stream = true
	
	// Create channels for streaming
	responseChan := make(chan *ProxyResponse, 10)
	errorChan := make(chan error, 1)
	
	// Start streaming in a goroutine
	go func() {
		defer close(responseChan)
		defer close(errorChan)
		
		// Execute streaming request
		err := s.executeStreamingRequest(ctx, routingResult.Adapter, openAIReq, responseChan, errorChan, routingResult.Provider, requestID, routingResult.Fallbacks)
		if err != nil {
			s.logUsage(userID, routingResult.UserAPIKeyID, req.Model, routingResult.Provider, requestID, "error", err.Error(), ipAddress, userAgent, time.Since(start), 0, 0, 0)
			errorChan <- err
			return
		}
		
		// Log successful streaming usage (tokens will be calculated from stream)
		s.logUsage(userID, routingResult.UserAPIKeyID, req.Model, routingResult.Provider, requestID, "success", "", ipAddress, userAgent, time.Since(start), 0, 0, 0)
	}()
	
	return responseChan, errorChan, nil
}

// convertToOpenAIRequest converts a proxy request to OpenAI format
func (s *Service) convertToOpenAIRequest(req *ProxyRequest) *core.OpenAIRequest {
	return &core.OpenAIRequest{
		Model:            req.Model,
		Messages:         req.Messages,
		Prompt:           req.Prompt,
		MaxTokens:        req.MaxTokens,
		Temperature:      req.Temperature,
		TopP:             req.TopP,
		TopK:             req.TopK,
		Stream:           req.Stream,
		Stop:             req.Stop,
		FrequencyPenalty: req.FrequencyPenalty,
		PresencePenalty:  req.PresencePenalty,
		N:                req.N,
		Tools:            req.Tools,
		ToolChoice:       req.ToolChoice,
		Functions:        req.Functions,
		FunctionCall:     req.FunctionCall,
		User:             req.User,
		Seed:             req.Seed,
	}
}

// executeRequest executes a request through the adapter
func (s *Service) executeRequest(ctx context.Context, adapter core.Adapter, req *core.OpenAIRequest, isStream bool) (*core.OpenAIResponse, error) {
	// Convert request to provider format
	providerReq, err := adapter.ConvertRequest(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to convert request: %w", err)
	}
	
	// Build request URL
	url, err := adapter.BuildRequestURL("", req)
	if err != nil {
		return nil, fmt.Errorf("failed to build request URL: %w", err)
	}
	
	// Setup headers
	headers := make(map[string]string)
	if err := adapter.SetupHeaders(headers, ""); err != nil {
		return nil, fmt.Errorf("failed to setup headers: %w", err)
	}
	
	// Convert request to JSON
	reqBody, err := json.Marshal(providerReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}
	
	// Execute request
	httpResp, err := adapter.ExecuteRequest(ctx, nil, url, headers, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	
	// Convert response
	response, err := adapter.ConvertResponse(ctx, httpResp, isStream)
	if err != nil {
		return nil, fmt.Errorf("failed to convert response: %w", err)
	}
	
	return response, nil
}

// executeStreamingRequest executes a streaming request
func (s *Service) executeStreamingRequest(ctx context.Context, adapter core.Adapter, req *core.OpenAIRequest, responseChan chan<- *ProxyResponse, errorChan chan<- error, provider, requestID string, fallbacks []string) error {
	// For now, simulate streaming by sending a single response
	// In a full implementation, this would handle actual streaming
	
	response, err := s.executeRequest(ctx, adapter, req, true)
	if err != nil {
		return err
	}
	
	// Send the response as a stream chunk
	proxyResponse := &ProxyResponse{
		OpenAIResponse: response,
		Provider:       provider,
		RequestID:      requestID,
		Fallbacks:      fallbacks,
	}
	
	select {
	case responseChan <- proxyResponse:
	case <-ctx.Done():
		return ctx.Err()
	}
	
	return nil
}

// calculateTokenUsage calculates token usage from the response
func (s *Service) calculateTokenUsage(response *core.OpenAIResponse) (int, int, int) {
	if response.Usage != nil {
		return response.Usage.PromptTokens, response.Usage.CompletionTokens, response.Usage.TotalTokens
	}
	
	// Fallback: estimate tokens from content length
	totalContent := 0
	for _, choice := range response.Choices {
		if choice.Message != nil {
			totalContent += len(choice.Message.GetStringContent())
		}
	}
	
	// Rough estimation: 4 characters per token
	estimatedTokens := totalContent / 4
	return estimatedTokens / 2, estimatedTokens / 2, estimatedTokens
}

// logUsage logs the usage to the database
func (s *Service) logUsage(userID, userAPIKeyID uint, model, provider, requestID, status, errorMessage, ipAddress, userAgent string, duration time.Duration, promptTokens, completionTokens, totalTokens int) {
	usageLog := &database.UsageLog{
		UserID:           userID,
		UserAPIKeyID:     &userAPIKeyID,
		Provider:         provider,
		Model:            model,
		RequestID:        requestID,
		Method:           "chat.completion",
		PromptTokens:     promptTokens,
		CompletionTokens: completionTokens,
		TotalTokens:      totalTokens,
		Duration:         duration.Milliseconds(),
		Status:           status,
		ErrorMessage:     errorMessage,
		IPAddress:        ipAddress,
		UserAgent:        userAgent,
	}
	
	if userAPIKeyID == 0 {
		usageLog.UserAPIKeyID = nil
	}
	
	if err := s.usageRepo.Create(usageLog); err != nil {
		logger.Error("Failed to log usage", "error", err, "user_id", userID, "request_id", requestID)
	}
}
