package proxy

import (
	"context"
	"testing"

	"llm-proxy-system/internal/adapters"
	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/database"
	"llm-proxy-system/internal/routing"
	"llm-proxy-system/pkg/adapters/core"

	"github.com/redis/go-redis/v9"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestProxyService() (*Service, error) {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	
	// Run migrations
	err = db.AutoMigrate(
		&database.User{},
		&database.UserAPIKey{},
		&database.AdminGlobalRoutingRule{},
		&database.UsageLog{},
		&database.WebhookConfig{},
	)
	if err != nil {
		return nil, err
	}
	
	// Setup test Redis (mock)
	redisClient := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   1, // Use test database
	})
	
	// Setup test config
	cfg := &config.Config{
		EncryptionKey: "12345678901234567890123456789012", // 32 chars
		Provider: struct {
			TimeoutSeconds int `json:"timeout_seconds"`
			MaxRetries     int `json:"max_retries"`
		}{
			TimeoutSeconds: 30,
			MaxRetries:     3,
		},
	}
	
	// Setup adapter service
	adapterService, err := adapters.NewService(db, cfg)
	if err != nil {
		return nil, err
	}
	
	// Setup routing service
	routingService := routing.NewService(db, redisClient, cfg, adapterService)
	
	// Create proxy service
	return NewService(db, cfg, routingService), nil
}

func setupTestProxyData(db *gorm.DB) error {
	// Create test user
	user := &database.User{
		Email:        "<EMAIL>",
		PasswordHash: "hashed_password",
		SystemAPIKey: "sk-sys_test_key",
		Role:         database.RoleUser,
		IsActive:     true,
	}
	
	if err := db.Create(user).Error; err != nil {
		return err
	}
	
	// Create test API keys
	apiKeys := []*database.UserAPIKey{
		{
			UserID:          user.ID,
			Provider:        "openai",
			Name:            "Test OpenAI Key",
			EncryptedAPIKey: "test-openai-key", // In production this would be encrypted
			Status:          database.StatusActive,
		},
		{
			UserID:          user.ID,
			Provider:        "gemini",
			Name:            "Test Gemini Key",
			EncryptedAPIKey: "test-gemini-key", // In production this would be encrypted
			Status:          database.StatusActive,
		},
	}
	
	for _, key := range apiKeys {
		if err := db.Create(key).Error; err != nil {
			return err
		}
	}
	
	// Create test routing rules
	rules := []*database.AdminGlobalRoutingRule{
		{
			Provider:    "openai",
			Priority:    1,
			IsEnabled:   true,
			ModelFilter: "gpt*",
			Description: "OpenAI GPT models",
		},
		{
			Provider:    "gemini",
			Priority:    2,
			IsEnabled:   true,
			ModelFilter: "gemini*",
			Description: "Google Gemini models",
		},
	}
	
	for _, rule := range rules {
		if err := db.Create(rule).Error; err != nil {
			return err
		}
	}
	
	return nil
}

func TestNewService(t *testing.T) {
	service, err := setupTestProxyService()
	if err != nil {
		t.Fatalf("Failed to create proxy service: %v", err)
	}
	
	if service == nil {
		t.Fatal("Service is nil")
	}
	
	if service.routingService == nil {
		t.Error("Routing service is nil")
	}
	
	if service.usageRepo == nil {
		t.Error("Usage repository is nil")
	}
}

func TestConvertToOpenAIRequest(t *testing.T) {
	service, err := setupTestProxyService()
	if err != nil {
		t.Fatalf("Failed to create proxy service: %v", err)
	}
	
	// Test proxy request
	proxyReq := &ProxyRequest{
		Model: "gpt-4",
		Messages: []core.Message{
			{
				Role:    "user",
				Content: []byte(`"Hello, world!"`),
			},
		},
		MaxTokens:   intPtr(100),
		Temperature: floatPtr(0.7),
		Stream:      false,
	}
	
	openAIReq := service.convertToOpenAIRequest(proxyReq)
	
	if openAIReq.Model != proxyReq.Model {
		t.Errorf("Expected model %s, got %s", proxyReq.Model, openAIReq.Model)
	}
	
	if len(openAIReq.Messages) != len(proxyReq.Messages) {
		t.Errorf("Expected %d messages, got %d", len(proxyReq.Messages), len(openAIReq.Messages))
	}
	
	if openAIReq.MaxTokens == nil || *openAIReq.MaxTokens != *proxyReq.MaxTokens {
		t.Error("MaxTokens not converted correctly")
	}
	
	if openAIReq.Temperature == nil || *openAIReq.Temperature != *proxyReq.Temperature {
		t.Error("Temperature not converted correctly")
	}
	
	if openAIReq.Stream != proxyReq.Stream {
		t.Error("Stream not converted correctly")
	}
}

func TestCalculateTokenUsage(t *testing.T) {
	service, err := setupTestProxyService()
	if err != nil {
		t.Fatalf("Failed to create proxy service: %v", err)
	}
	
	// Test with usage information
	responseWithUsage := &core.OpenAIResponse{
		Usage: &core.Usage{
			PromptTokens:     50,
			CompletionTokens: 100,
			TotalTokens:      150,
		},
	}
	
	prompt, completion, total := service.calculateTokenUsage(responseWithUsage)
	if prompt != 50 || completion != 100 || total != 150 {
		t.Errorf("Expected tokens 50/100/150, got %d/%d/%d", prompt, completion, total)
	}
	
	// Test without usage information (fallback estimation)
	responseWithoutUsage := &core.OpenAIResponse{
		Choices: []core.Choice{
			{
				Message: &core.Message{
					Role:    "assistant",
					Content: []byte(`"This is a test response with some content"`),
				},
			},
		},
	}
	
	prompt, completion, total = service.calculateTokenUsage(responseWithoutUsage)
	if total == 0 {
		t.Error("Expected non-zero token estimation")
	}
	
	if prompt+completion != total {
		t.Error("Token calculation should be consistent")
	}
}

func TestProcessRequest(t *testing.T) {
	service, err := setupTestProxyService()
	if err != nil {
		t.Fatalf("Failed to create proxy service: %v", err)
	}
	
	if err := setupTestProxyData(service.db); err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Test successful request
	req := &ProxyRequest{
		Model: "gpt-4",
		Messages: []core.Message{
			{
				Role:    "user",
				Content: []byte(`"Hello, world!"`),
			},
		},
		MaxTokens: intPtr(100),
	}
	
	response, err := service.ProcessRequest(ctx, 1, req, "test-req-123", "127.0.0.1", "test-agent")
	if err != nil {
		t.Errorf("Expected successful request, got error: %v", err)
	}
	
	if response == nil {
		t.Fatal("Response is nil")
	}
	
	if response.Provider == "" {
		t.Error("Provider not set in response")
	}
	
	if response.RequestID != "test-req-123" {
		t.Errorf("Expected request ID 'test-req-123', got '%s'", response.RequestID)
	}
	
	if response.Duration == 0 {
		t.Error("Duration not set in response")
	}
}

func TestProcessRequestWithInvalidModel(t *testing.T) {
	service, err := setupTestProxyService()
	if err != nil {
		t.Fatalf("Failed to create proxy service: %v", err)
	}
	
	if err := setupTestProxyData(service.db); err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Test with model that has no provider
	req := &ProxyRequest{
		Model: "nonexistent-model",
		Messages: []core.Message{
			{
				Role:    "user",
				Content: []byte(`"Hello, world!"`),
			},
		},
	}
	
	_, err = service.ProcessRequest(ctx, 1, req, "test-req-124", "127.0.0.1", "test-agent")
	if err == nil {
		t.Error("Expected error for nonexistent model")
	}
	
	if !contains(err.Error(), "routing failed") {
		t.Errorf("Expected routing error, got: %v", err)
	}
}

func TestProcessStreamRequest(t *testing.T) {
	service, err := setupTestProxyService()
	if err != nil {
		t.Fatalf("Failed to create proxy service: %v", err)
	}
	
	if err := setupTestProxyData(service.db); err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	req := &ProxyRequest{
		Model: "gpt-4",
		Messages: []core.Message{
			{
				Role:    "user",
				Content: []byte(`"Hello, world!"`),
			},
		},
		Stream: true,
	}
	
	responseChan, errorChan, err := service.ProcessStreamRequest(ctx, 1, req, "test-req-125", "127.0.0.1", "test-agent")
	if err != nil {
		t.Errorf("Expected successful stream start, got error: %v", err)
	}
	
	if responseChan == nil {
		t.Error("Response channel is nil")
	}
	
	if errorChan == nil {
		t.Error("Error channel is nil")
	}
	
	// Wait for at least one response or error
	select {
	case response := <-responseChan:
		if response == nil {
			t.Error("Received nil response")
		}
		if response.Provider == "" {
			t.Error("Provider not set in streaming response")
		}
	case err := <-errorChan:
		t.Errorf("Received error in stream: %v", err)
	}
}

// Helper functions
func intPtr(i int) *int {
	return &i
}

func floatPtr(f float64) *float64 {
	return &f
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[:len(substr)] == substr || 
		   len(s) > len(substr) && s[len(s)-len(substr):] == substr ||
		   (len(s) > len(substr) && func() bool {
			   for i := 0; i <= len(s)-len(substr); i++ {
				   if s[i:i+len(substr)] == substr {
					   return true
				   }
			   }
			   return false
		   }())
}
