package router

import (
	"llm-proxy-system/internal/database"
	"llm-proxy-system/internal/handlers"
	"llm-proxy-system/internal/middleware"
	"llm-proxy-system/internal/services"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// Setup initializes and configures the router
func Setup(services *services.Container) *gin.Engine {
	r := gin.New()
	
	// Global middleware
	r.Use(gin.Logger())
	r.Use(gin.Recovery())
	r.Use(middleware.RequestID())
	r.Use(middleware.SecurityHeaders())
	
	// CORS configuration
	corsConfig := cors.Config{
		AllowOrigins:     services.Config.CORS.AllowedOrigins,
		AllowMethods:     services.Config.CORS.AllowedMethods,
		AllowHeaders:     services.Config.CORS.AllowedHeaders,
		AllowCredentials: true,
	}
	r.Use(cors.New(corsConfig))

	// Initialize handlers
	var authHandler *handlers.AuthHandler
	if services.AuthService != nil {
		authHandler = handlers.NewAuthHandler(services.AuthService)
	}

	var adapterHandler *handlers.AdapterHandler
	if services.AdapterService != nil {
		adapterHandler = handlers.NewAdapterHandler(services.AdapterService)
	}

	var routingHandler *handlers.RoutingHandler
	if services.RoutingService != nil {
		routingHandler = handlers.NewRoutingHandler(services.RoutingService, database.NewAdminGlobalRoutingRuleRepository(services.DB))
	}

	var proxyHandler *handlers.ProxyHandler
	if services.ProxyService != nil {
		proxyHandler = handlers.NewProxyHandler(services.ProxyService)
	}

	var keyHandler *handlers.KeyHandler
	if services.KeyService != nil {
		keyHandler = handlers.NewKeyHandler(services.KeyService)
	}

	var adminHandler *handlers.AdminHandler
	if services.AdminService != nil {
		adminHandler = handlers.NewAdminHandler(services.AdminService)
	}

	var webhookHandler *handlers.WebhookHandler
	if services.WebhookService != nil {
		webhookHandler = handlers.NewWebhookHandler(services.WebhookService)
	}

	var monitoringHandler *handlers.MonitoringHandler
	if services.MonitoringService != nil {
		monitoringHandler = handlers.NewMonitoringHandler(services.MonitoringService)
	}

	// Health check endpoint (no auth required)
	r.GET("/healthz", func(c *gin.Context) {
		if monitoringHandler != nil {
			monitoringHandler.GetHealthCheck(c)
		} else {
			c.JSON(200, gin.H{
				"status":  "healthy",
				"service": "llm-proxy-system",
			})
		}
	})
	
	// Metrics endpoint (no auth required)
	r.GET("/metrics", func(c *gin.Context) {
		// TODO: Implement Prometheus metrics
		c.JSON(200, gin.H{
			"message": "Metrics endpoint - TODO: Implement Prometheus metrics",
		})
	})
	
	// API routes
	api := r.Group("/api")
	{
		// Version 1 API
		v1 := api.Group("/v1")
		{
			// Authentication routes (no auth required)
			auth := v1.Group("/auth")
			{
				if authHandler != nil {
					auth.POST("/register", authHandler.Register)
					auth.POST("/login", authHandler.Login)
					auth.POST("/refresh", authHandler.RefreshToken)
					auth.POST("/logout", middleware.AuthRequired(services), authHandler.Logout)
					auth.GET("/me", middleware.AuthRequired(services), authHandler.GetMe)
					auth.POST("/change-password", middleware.AuthRequired(services), authHandler.ChangePassword)
				} else {
					// Fallback handlers when auth service is not available
					auth.POST("/register", func(c *gin.Context) {
						c.JSON(503, gin.H{"error": "Authentication service not available"})
					})
					auth.POST("/login", func(c *gin.Context) {
						c.JSON(503, gin.H{"error": "Authentication service not available"})
					})
					auth.POST("/refresh", func(c *gin.Context) {
						c.JSON(503, gin.H{"error": "Authentication service not available"})
					})
					auth.GET("/me", func(c *gin.Context) {
						c.JSON(503, gin.H{"error": "Authentication service not available"})
					})
				}
			}
			
			// Provider and adapter endpoints (some require auth)
			providers := v1.Group("/providers")
			{
				if adapterHandler != nil {
					// Public endpoints (no auth required)
					providers.GET("", adapterHandler.GetSupportedProviders)
					providers.GET("/:provider", adapterHandler.GetProviderInfo)
					providers.GET("/:provider/models", adapterHandler.GetProviderModels)
					providers.GET("/:provider/capabilities", adapterHandler.GetProviderCapabilities)
					providers.GET("/status", adapterHandler.GetProviderStatus)
					providers.GET("/model-routing", adapterHandler.GetProviderByModel)

					// Protected endpoints (require auth)
					providers.POST("/validate-key", middleware.AuthRequired(services), adapterHandler.ValidateProviderKey)
					providers.POST("/test-connection", middleware.AuthRequired(services), adapterHandler.TestProviderConnection)
					providers.GET("/metrics", middleware.AuthRequired(services), adapterHandler.GetAdapterMetrics)
				} else {
					providers.GET("", func(c *gin.Context) {
						c.JSON(503, gin.H{"error": "Adapter service not available"})
					})
				}
			}

			// Main LLM proxy endpoints (requires auth)
			llm := v1.Group("/llm")
			llm.Use(middleware.AuthRequired(services))
			llm.Use(middleware.RateLimit(services))
			{
				if proxyHandler != nil {
					llm.POST("/proxy", proxyHandler.ProxyLLMRequest)
					llm.POST("/validate", proxyHandler.ValidateRequest)
					llm.GET("/models", proxyHandler.GetSupportedModels)
					llm.GET("/history", proxyHandler.GetRequestHistory)
				} else {
					llm.POST("/proxy", func(c *gin.Context) {
						c.JSON(503, gin.H{"error": "Proxy service not available"})
					})
				}
			}

			// OpenAI-compatible endpoints (requires auth)
			openai := v1.Group("/chat")
			openai.Use(middleware.AuthRequired(services))
			openai.Use(middleware.RateLimit(services))
			{
				if proxyHandler != nil {
					openai.POST("/completions", proxyHandler.ProxyLLMRequest)
				} else {
					openai.POST("/completions", func(c *gin.Context) {
						c.JSON(503, gin.H{"error": "Proxy service not available"})
					})
				}
			}

			// Models endpoint (OpenAI compatibility)
			models := v1.Group("/models")
			{
				if proxyHandler != nil {
					models.GET("", proxyHandler.GetSupportedModels)
				} else {
					models.GET("", func(c *gin.Context) {
						c.JSON(503, gin.H{"error": "Proxy service not available"})
					})
				}
			}
			
			// User management routes (requires auth)
			users := v1.Group("/users")
			users.Use(middleware.AuthRequired(services))
			{
				me := users.Group("/me")
				{
					// User profile
					me.GET("", func(c *gin.Context) {
						// TODO: Implement get user profile
						c.JSON(200, gin.H{"message": "User profile - TODO: Implement"})
					})
					me.PUT("", func(c *gin.Context) {
						// TODO: Implement update user profile
						c.JSON(200, gin.H{"message": "Update profile - TODO: Implement"})
					})
					
					// User API keys
					keys := me.Group("/keys")
					{
						if keyHandler != nil {
							keys.GET("", keyHandler.GetKeys)
							keys.POST("", keyHandler.CreateKey)
							keys.GET("/stats", keyHandler.GetKeyStats)
							keys.POST("/validate", keyHandler.ValidateKeyFormat)
							keys.GET("/:id", keyHandler.GetKey)
							keys.PUT("/:id", keyHandler.UpdateKey)
							keys.DELETE("/:id", keyHandler.DeleteKey)
							keys.POST("/:id/test", keyHandler.TestKey)
						} else {
							keys.GET("", func(c *gin.Context) {
								c.JSON(503, gin.H{"error": "Key management service not available"})
							})
						}
					}

					// User webhooks
					webhooks := me.Group("/webhooks")
					{
						if webhookHandler != nil {
							webhooks.GET("", webhookHandler.GetWebhooks)
							webhooks.POST("", webhookHandler.CreateWebhook)
							webhooks.GET("/events", webhookHandler.GetSupportedEvents)
							webhooks.GET("/:id", webhookHandler.GetWebhook)
							webhooks.PUT("/:id", webhookHandler.UpdateWebhook)
							webhooks.DELETE("/:id", webhookHandler.DeleteWebhook)
							webhooks.POST("/:id/test", webhookHandler.TestWebhook)
						} else {
							webhooks.GET("", func(c *gin.Context) {
								c.JSON(503, gin.H{"error": "Webhook service not available"})
							})
						}
					}
				}
			}
			
			// Routing endpoints (some require auth)
			routing := v1.Group("/routing")
			{
				if routingHandler != nil {
					// Public endpoints
					routing.GET("/health", routingHandler.GetProviderHealth)
					routing.GET("/stats", middleware.AuthRequired(services), routingHandler.GetRoutingStats)

					// Protected endpoints
					routing.POST("/test", middleware.AuthRequired(services), routingHandler.TestRouting)
					routing.POST("/health", middleware.AuthRequired(services), middleware.AdminRequired(services), routingHandler.UpdateProviderHealth)
				} else {
					routing.GET("/health", func(c *gin.Context) {
						c.JSON(503, gin.H{"error": "Routing service not available"})
					})
				}
			}

			// Admin routes (requires admin auth)
			admin := v1.Group("/admin")
			admin.Use(middleware.AuthRequired(services))
			admin.Use(middleware.AdminRequired(services))
			{
				// Routing rules management
				routingRules := admin.Group("/routing-rules")
				{
					if routingHandler != nil {
						routingRules.GET("", routingHandler.GetRoutingRules)
						routingRules.POST("", routingHandler.CreateRoutingRule)
						routingRules.PUT("/:id", routingHandler.UpdateRoutingRule)
						routingRules.DELETE("/:id", routingHandler.DeleteRoutingRule)
						routingRules.PUT("/reorder", routingHandler.ReorderRoutingRules)
					} else {
						routingRules.GET("", func(c *gin.Context) {
							c.JSON(503, gin.H{"error": "Routing service not available"})
						})
					}
				}
				
				// System management
				system := admin.Group("/system")
				{
					if adminHandler != nil {
						system.GET("/stats", adminHandler.GetSystemStats)
						system.GET("/health", adminHandler.GetSystemHealth)
						system.GET("/logs", adminHandler.GetSystemLogs)
						system.GET("/metrics", adminHandler.GetSystemMetrics)
					} else {
						system.GET("/stats", func(c *gin.Context) {
							c.JSON(503, gin.H{"error": "Admin service not available"})
						})
					}
				}

				// User management
				users := admin.Group("/users")
				{
					if adminHandler != nil {
						users.GET("", adminHandler.GetUsers)
						users.GET("/:id", adminHandler.GetUser)
						users.PUT("/:id", adminHandler.UpdateUser)
						users.DELETE("/:id", adminHandler.DeleteUser)
						users.GET("/:id/keys", adminHandler.GetUserAPIKeys)
					} else {
						users.GET("", func(c *gin.Context) {
							c.JSON(503, gin.H{"error": "Admin service not available"})
						})
					}
				}

				// Webhook management
				webhookAdmin := admin.Group("/webhooks")
				{
					if webhookHandler != nil {
						webhookAdmin.POST("/trigger", webhookHandler.TriggerEvent)
					} else {
						webhookAdmin.POST("/trigger", func(c *gin.Context) {
							c.JSON(503, gin.H{"error": "Webhook service not available"})
						})
					}
				}

				// Monitoring and analytics
				monitoring := admin.Group("/monitoring")
				{
					if monitoringHandler != nil {
						monitoring.GET("/system", monitoringHandler.GetSystemMetrics)
						monitoring.GET("/business", monitoringHandler.GetBusinessMetrics)
						monitoring.GET("/analytics", monitoringHandler.GetAnalytics)
						monitoring.GET("/alerts", monitoringHandler.GetAlerts)
						monitoring.POST("/alerts", monitoringHandler.CreateAlert)
						monitoring.GET("/realtime", monitoringHandler.GetRealTimeMetrics)
						monitoring.GET("/providers/:provider", monitoringHandler.GetProviderMetrics)
						monitoring.GET("/metrics/:metric/history", monitoringHandler.GetMetricsHistory)
					} else {
						monitoring.GET("/system", func(c *gin.Context) {
							c.JSON(503, gin.H{"error": "Monitoring service not available"})
						})
					}
				}
			}
		}
	}
	
	// WebSocket endpoint for real-time updates
	r.GET("/ws", func(c *gin.Context) {
		// TODO: Implement WebSocket handler
		c.JSON(200, gin.H{"message": "WebSocket endpoint - TODO: Implement"})
	})
	
	// Webhook endpoints
	webhook := r.Group("/webhook")
	{
		webhook.POST("/discord", func(c *gin.Context) {
			// TODO: Implement Discord webhook
			c.JSON(200, gin.H{"message": "Discord webhook - TODO: Implement"})
		})
		webhook.POST("/telegram", func(c *gin.Context) {
			// TODO: Implement Telegram webhook
			c.JSON(200, gin.H{"message": "Telegram webhook - TODO: Implement"})
		})
	}
	
	return r
}
