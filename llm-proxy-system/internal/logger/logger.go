package logger

import (
	"os"

	"github.com/sirupsen/logrus"
)

var log *logrus.Logger

// Init initializes the logger
func Init() {
	log = logrus.New()
	
	// Set output to stdout
	log.SetOutput(os.Stdout)
	
	// Set log format based on environment
	if os.Getenv("ENVIRONMENT") == "production" {
		// JSON format for production (better for log aggregation)
		log.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02T15:04:05.000Z",
		})
		log.SetLevel(logrus.InfoLevel)
	} else {
		// Text format for development (more readable)
		log.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "2006-01-02 15:04:05",
			ForceColors:     true,
		})
		log.SetLevel(logrus.DebugLevel)
	}
}

// GetLogger returns the logger instance
func GetLogger() *logrus.Logger {
	if log == nil {
		Init()
	}
	return log
}

// Debug logs a debug message
func Debug(msg string, fields ...interface{}) {
	GetLogger().WithFields(parseFields(fields...)).Debug(msg)
}

// Info logs an info message
func Info(msg string, fields ...interface{}) {
	GetLogger().WithFields(parseFields(fields...)).Info(msg)
}

// Warn logs a warning message
func Warn(msg string, fields ...interface{}) {
	GetLogger().WithFields(parseFields(fields...)).Warn(msg)
}

// Error logs an error message
func Error(msg string, fields ...interface{}) {
	GetLogger().WithFields(parseFields(fields...)).Error(msg)
}

// Fatal logs a fatal message and exits
func Fatal(msg string, fields ...interface{}) {
	GetLogger().WithFields(parseFields(fields...)).Fatal(msg)
}

// WithFields creates a logger with fields
func WithFields(fields logrus.Fields) *logrus.Entry {
	return GetLogger().WithFields(fields)
}

// WithField creates a logger with a single field
func WithField(key string, value interface{}) *logrus.Entry {
	return GetLogger().WithField(key, value)
}

// WithError creates a logger with an error field
func WithError(err error) *logrus.Entry {
	return GetLogger().WithError(err)
}

// parseFields converts variadic arguments to logrus.Fields
func parseFields(fields ...interface{}) logrus.Fields {
	logFields := logrus.Fields{}
	
	// Parse key-value pairs
	for i := 0; i < len(fields); i += 2 {
		if i+1 < len(fields) {
			if key, ok := fields[i].(string); ok {
				logFields[key] = fields[i+1]
			}
		}
	}
	
	return logFields
}

// RequestLogger creates a logger for HTTP requests
func RequestLogger(method, path, userID string, statusCode int, duration float64) *logrus.Entry {
	return GetLogger().WithFields(logrus.Fields{
		"method":      method,
		"path":        path,
		"user_id":     userID,
		"status_code": statusCode,
		"duration_ms": duration,
		"type":        "http_request",
	})
}

// ProviderLogger creates a logger for provider interactions
func ProviderLogger(provider, model, userID string) *logrus.Entry {
	return GetLogger().WithFields(logrus.Fields{
		"provider": provider,
		"model":    model,
		"user_id":  userID,
		"type":     "provider_request",
	})
}

// DatabaseLogger creates a logger for database operations
func DatabaseLogger(operation, table string, duration float64) *logrus.Entry {
	return GetLogger().WithFields(logrus.Fields{
		"operation":   operation,
		"table":       table,
		"duration_ms": duration,
		"type":        "database_operation",
	})
}

// WebhookLogger creates a logger for webhook operations
func WebhookLogger(webhookType, destination string) *logrus.Entry {
	return GetLogger().WithFields(logrus.Fields{
		"webhook_type": webhookType,
		"destination":  destination,
		"type":         "webhook",
	})
}

// SecurityLogger creates a logger for security events
func SecurityLogger(event, userID, ip string) *logrus.Entry {
	return GetLogger().WithFields(logrus.Fields{
		"event":   event,
		"user_id": userID,
		"ip":      ip,
		"type":    "security",
	})
}
