package auth

import (
	"fmt"
	"time"

	"llm-proxy-system/internal/config"

	"github.com/golang-jwt/jwt/v5"
)

// Claims represents JWT claims
type Claims struct {
	UserID uint   `json:"user_id"`
	Email  string `json:"email"`
	Role   string `json:"role"`
	jwt.RegisteredClaims
}

// JWTService handles JWT token operations
type JWTService struct {
	secretKey []byte
	expiresIn time.Duration
}

// NewJWTService creates a new JWT service
func NewJWTService(cfg *config.Config) *JWTService {
	return &JWTService{
		secretKey: []byte(cfg.JWTSecret),
		expiresIn: cfg.JWTExpiresIn,
	}
}

// GenerateToken generates a new JWT token for a user
func (s *JWTService) GenerateToken(userID uint, email, role string) (string, error) {
	now := time.Now().UTC()
	
	claims := &Claims{
		UserID: userID,
		Email:  email,
		Role:   role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(s.expiresIn)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "llm-proxy-system",
			Subject:   fmt.Sprintf("user:%d", userID),
		},
	}
	
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(s.secretKey)
}

// GenerateRefreshToken generates a refresh token with longer expiration
func (s *JWTService) GenerateRefreshToken(userID uint, email, role string) (string, error) {
	now := time.Now().UTC()
	
	claims := &Claims{
		UserID: userID,
		Email:  email,
		Role:   role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(7 * 24 * time.Hour)), // 7 days
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "llm-proxy-system",
			Subject:   fmt.Sprintf("refresh:%d", userID),
		},
	}
	
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(s.secretKey)
}

// ValidateToken validates a JWT token and returns claims
func (s *JWTService) ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return s.secretKey, nil
	})
	
	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}
	
	claims, ok := token.Claims.(*Claims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("invalid token claims")
	}
	
	// Check if token is expired
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now().UTC()) {
		return nil, fmt.Errorf("token is expired")
	}
	
	return claims, nil
}

// RefreshToken validates a refresh token and generates new access token
func (s *JWTService) RefreshToken(refreshTokenString string) (string, string, error) {
	// Validate refresh token
	claims, err := s.ValidateToken(refreshTokenString)
	if err != nil {
		return "", "", fmt.Errorf("invalid refresh token: %w", err)
	}
	
	// Check if it's actually a refresh token
	if claims.Subject == "" || claims.Subject[:7] != "refresh" {
		return "", "", fmt.Errorf("not a refresh token")
	}
	
	// Generate new access token
	accessToken, err := s.GenerateToken(claims.UserID, claims.Email, claims.Role)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate access token: %w", err)
	}
	
	// Generate new refresh token
	refreshToken, err := s.GenerateRefreshToken(claims.UserID, claims.Email, claims.Role)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate refresh token: %w", err)
	}
	
	return accessToken, refreshToken, nil
}

// ExtractUserID extracts user ID from token claims
func (s *JWTService) ExtractUserID(tokenString string) (uint, error) {
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		return 0, err
	}
	return claims.UserID, nil
}

// ExtractUserRole extracts user role from token claims
func (s *JWTService) ExtractUserRole(tokenString string) (string, error) {
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		return "", err
	}
	return claims.Role, nil
}

// IsTokenExpired checks if a token is expired without validating signature
func (s *JWTService) IsTokenExpired(tokenString string) bool {
	token, _ := jwt.ParseWithClaims(tokenString, &Claims{}, nil)
	if token == nil {
		return true
	}
	
	claims, ok := token.Claims.(*Claims)
	if !ok {
		return true
	}
	
	return claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now().UTC())
}

// GetTokenTTL returns the time until token expiration
func (s *JWTService) GetTokenTTL(tokenString string) (time.Duration, error) {
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		return 0, err
	}
	
	if claims.ExpiresAt == nil {
		return 0, fmt.Errorf("token has no expiration")
	}
	
	ttl := time.Until(claims.ExpiresAt.Time)
	if ttl < 0 {
		return 0, fmt.Errorf("token is expired")
	}
	
	return ttl, nil
}
