package auth

import (
	"context"
	"fmt"
	"time"

	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/database"
	"llm-proxy-system/internal/logger"
	"llm-proxy-system/internal/utils"

	"github.com/redis/go-redis/v9"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// Service handles authentication operations
type Service struct {
	db         *gorm.DB
	redis      *redis.Client
	config     *config.Config
	jwtService *JWTService
	userRepo   *database.UserRepository
	crypto     *utils.CryptoService
}

// NewService creates a new authentication service
func NewService(db *gorm.DB, redis *redis.Client, cfg *config.Config) (*Service, error) {
	crypto, err := utils.NewCryptoService(cfg.EncryptionKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create crypto service: %w", err)
	}
	
	return &Service{
		db:         db,
		redis:      redis,
		config:     cfg,
		jwtService: NewJWTService(cfg),
		userRepo:   database.NewUserRepository(db),
		crypto:     crypto,
	}, nil
}

// RegisterRequest represents a user registration request
type RegisterRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=8"`
}

// LoginRequest represents a user login request
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// AuthResponse represents an authentication response
type AuthResponse struct {
	User         *UserResponse `json:"user"`
	AccessToken  string        `json:"access_token"`
	RefreshToken string        `json:"refresh_token"`
	ExpiresIn    int64         `json:"expires_in"`
}

// UserResponse represents a user in API responses
type UserResponse struct {
	ID           uint      `json:"id"`
	Email        string    `json:"email"`
	Role         string    `json:"role"`
	SystemAPIKey string    `json:"system_api_key"`
	IsActive     bool      `json:"is_active"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// Register registers a new user
func (s *Service) Register(ctx context.Context, req *RegisterRequest) (*AuthResponse, error) {
	logger.Info("User registration attempt", "email", req.Email)
	
	// Check if user already exists
	existingUser, err := s.userRepo.GetByEmail(req.Email)
	if err == nil && existingUser != nil {
		return nil, fmt.Errorf("user with email %s already exists", req.Email)
	}
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check existing user: %w", err)
	}
	
	// Hash password
	hashedPassword, err := s.hashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}
	
	// Generate system API key
	systemAPIKey, err := utils.GenerateAPIKey("sk-sys")
	if err != nil {
		return nil, fmt.Errorf("failed to generate system API key: %w", err)
	}
	
	// Create user
	user := &database.User{
		Email:        req.Email,
		PasswordHash: hashedPassword,
		SystemAPIKey: systemAPIKey,
		Role:         database.RoleUser,
		IsActive:     true,
	}
	
	if err := s.userRepo.Create(user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}
	
	logger.Info("User registered successfully", "user_id", user.ID, "email", user.Email)
	
	// Generate tokens
	return s.generateAuthResponse(user)
}

// Login authenticates a user and returns tokens
func (s *Service) Login(ctx context.Context, req *LoginRequest) (*AuthResponse, error) {
	logger.Info("User login attempt", "email", req.Email)
	
	// Get user by email
	user, err := s.userRepo.GetByEmail(req.Email)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("invalid email or password")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	
	// Check if user is active
	if !user.IsActive {
		return nil, fmt.Errorf("user account is disabled")
	}
	
	// Verify password
	if !s.verifyPassword(req.Password, user.PasswordHash) {
		logger.SecurityLogger("login_failed", fmt.Sprintf("%d", user.ID), "").Warn("Invalid password attempt")
		return nil, fmt.Errorf("invalid email or password")
	}
	
	logger.Info("User logged in successfully", "user_id", user.ID, "email", user.Email)
	
	// Generate tokens
	return s.generateAuthResponse(user)
}

// RefreshToken refreshes an access token using a refresh token
func (s *Service) RefreshToken(ctx context.Context, refreshToken string) (*AuthResponse, error) {
	logger.Debug("Token refresh attempt")
	
	// Validate and refresh token
	accessToken, newRefreshToken, err := s.jwtService.RefreshToken(refreshToken)
	if err != nil {
		return nil, fmt.Errorf("failed to refresh token: %w", err)
	}
	
	// Extract user info from new access token
	claims, err := s.jwtService.ValidateToken(accessToken)
	if err != nil {
		return nil, fmt.Errorf("failed to validate new token: %w", err)
	}
	
	// Get user details
	user, err := s.userRepo.GetByID(claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	
	return &AuthResponse{
		User:         s.userToResponse(user),
		AccessToken:  accessToken,
		RefreshToken: newRefreshToken,
		ExpiresIn:    int64(s.config.JWTExpiresIn.Seconds()),
	}, nil
}

// GetCurrentUser gets the current user from token
func (s *Service) GetCurrentUser(ctx context.Context, userID uint) (*UserResponse, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	
	return s.userToResponse(user), nil
}

// ValidateSystemAPIKey validates a system API key and returns the user
func (s *Service) ValidateSystemAPIKey(ctx context.Context, apiKey string) (*database.User, error) {
	if !utils.ValidateAPIKey(apiKey) {
		return nil, fmt.Errorf("invalid API key format")
	}
	
	user, err := s.userRepo.GetBySystemAPIKey(apiKey)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("invalid API key")
		}
		return nil, fmt.Errorf("failed to validate API key: %w", err)
	}
	
	if !user.IsActive {
		return nil, fmt.Errorf("user account is disabled")
	}
	
	return user, nil
}

// Logout invalidates a token (adds to Redis blacklist)
func (s *Service) Logout(ctx context.Context, token string) error {
	// Get token TTL
	ttl, err := s.jwtService.GetTokenTTL(token)
	if err != nil {
		// If token is already expired or invalid, consider logout successful
		return nil
	}
	
	// Add token to blacklist in Redis
	key := fmt.Sprintf("blacklist:%s", token)
	if err := s.redis.Set(ctx, key, "1", ttl).Err(); err != nil {
		logger.Error("Failed to blacklist token", "error", err)
		// Don't fail logout if Redis is down
	}
	
	return nil
}

// IsTokenBlacklisted checks if a token is blacklisted
func (s *Service) IsTokenBlacklisted(ctx context.Context, token string) bool {
	key := fmt.Sprintf("blacklist:%s", token)
	exists, err := s.redis.Exists(ctx, key).Result()
	if err != nil {
		logger.Error("Failed to check token blacklist", "error", err)
		return false // Fail open if Redis is down
	}
	return exists > 0
}

// ChangePassword changes a user's password
func (s *Service) ChangePassword(ctx context.Context, userID uint, oldPassword, newPassword string) error {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	
	// Verify old password
	if !s.verifyPassword(oldPassword, user.PasswordHash) {
		return fmt.Errorf("invalid current password")
	}
	
	// Hash new password
	hashedPassword, err := s.hashPassword(newPassword)
	if err != nil {
		return fmt.Errorf("failed to hash new password: %w", err)
	}
	
	// Update password
	user.PasswordHash = hashedPassword
	if err := s.userRepo.Update(user); err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}
	
	logger.SecurityLogger("password_changed", fmt.Sprintf("%d", userID), "").Info("Password changed successfully")
	return nil
}

// Helper methods

func (s *Service) generateAuthResponse(user *database.User) (*AuthResponse, error) {
	// Generate access token
	accessToken, err := s.jwtService.GenerateToken(user.ID, user.Email, user.Role)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}
	
	// Generate refresh token
	refreshToken, err := s.jwtService.GenerateRefreshToken(user.ID, user.Email, user.Role)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}
	
	return &AuthResponse{
		User:         s.userToResponse(user),
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int64(s.config.JWTExpiresIn.Seconds()),
	}, nil
}

func (s *Service) userToResponse(user *database.User) *UserResponse {
	return &UserResponse{
		ID:           user.ID,
		Email:        user.Email,
		Role:         user.Role,
		SystemAPIKey: user.SystemAPIKey,
		IsActive:     user.IsActive,
		CreatedAt:    user.CreatedAt,
		UpdatedAt:    user.UpdatedAt,
	}
}

func (s *Service) hashPassword(password string) (string, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hash), nil
}

func (s *Service) verifyPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}
