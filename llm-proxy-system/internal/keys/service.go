package keys

import (
	"context"
	"fmt"
	"time"

	"llm-proxy-system/internal/adapters"
	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/database"
	"llm-proxy-system/internal/logger"
	"llm-proxy-system/internal/utils"

	"gorm.io/gorm"
)

// Service handles API key management operations
type Service struct {
	db             *gorm.DB
	config         *config.Config
	adapterService *adapters.Service
	keyRepo        *database.UserAPIKeyRepository
	userRepo       *database.UserRepository
	crypto         *utils.CryptoService
}

// NewService creates a new key management service
func NewService(db *gorm.DB, cfg *config.Config, adapterService *adapters.Service) (*Service, error) {
	crypto, err := utils.NewCryptoService(cfg.EncryptionKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create crypto service: %w", err)
	}

	return &Service{
		db:             db,
		config:         cfg,
		adapterService: adapterService,
		keyRepo:        database.NewUserAPIKeyRepository(db),
		userRepo:       database.NewUserRepository(db),
		crypto:         crypto,
	}, nil
}

// CreateKeyRequest represents a request to create a new API key
type CreateKeyRequest struct {
	Provider string `json:"provider" binding:"required"`
	Name     string `json:"name" binding:"required"`
	APIKey   string `json:"api_key" binding:"required"`
}

// UpdateKeyRequest represents a request to update an API key
type UpdateKeyRequest struct {
	Name   string `json:"name,omitempty"`
	APIKey string `json:"api_key,omitempty"`
}

// KeyResponse represents an API key in responses
type KeyResponse struct {
	ID              uint      `json:"id"`
	Provider        string    `json:"provider"`
	Name            string    `json:"name"`
	Status          string    `json:"status"`
	LastTestedAt    *time.Time `json:"last_tested_at"`
	LastUsedAt      *time.Time `json:"last_used_at"`
	ErrorMessage    string    `json:"error_message,omitempty"`
	RequestCount    int64     `json:"request_count"`
	SuccessCount    int64     `json:"success_count"`
	ErrorCount      int64     `json:"error_count"`
	TotalTokensUsed int64     `json:"total_tokens_used"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	MaskedAPIKey    string    `json:"masked_api_key"`
}

// CreateKey creates a new API key for a user
func (s *Service) CreateKey(ctx context.Context, userID uint, req *CreateKeyRequest) (*KeyResponse, error) {
	logger.Info("Creating API key", "user_id", userID, "provider", req.Provider, "name", req.Name)

	// Validate provider
	providers := s.adapterService.GetSupportedProviders()
	validProvider := false
	for _, provider := range providers {
		if provider.Name == req.Provider {
			validProvider = true
			break
		}
	}
	if !validProvider {
		return nil, fmt.Errorf("unsupported provider: %s", req.Provider)
	}

	// Validate API key format
	if !utils.ValidateAPIKey(req.APIKey) {
		return nil, fmt.Errorf("invalid API key format")
	}

	// Test the API key with the provider
	err := s.adapterService.ValidateProviderKey(ctx, req.Provider, req.APIKey)
	if err != nil {
		logger.Warn("API key validation failed", "provider", req.Provider, "error", err)
		// Continue with creation but mark as invalid
	}

	// Encrypt the API key
	encryptedKey, err := s.crypto.Encrypt(req.APIKey)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt API key: %w", err)
	}

	// Create the key record
	key := &database.UserAPIKey{
		UserID:          userID,
		Provider:        req.Provider,
		Name:            req.Name,
		EncryptedAPIKey: encryptedKey,
		Status:          database.StatusTesting,
	}

	if err := s.keyRepo.Create(key); err != nil {
		return nil, fmt.Errorf("failed to create API key: %w", err)
	}

	// Update status based on validation result
	status := database.StatusActive
	errorMessage := ""
	if err != nil {
		status = database.StatusInvalid
		errorMessage = err.Error()
	}

	if err := s.keyRepo.UpdateStatus(key.ID, status, errorMessage); err != nil {
		logger.Error("Failed to update key status", "error", err, "key_id", key.ID)
	}

	logger.Info("API key created successfully", "key_id", key.ID, "user_id", userID, "provider", req.Provider, "status", status)

	// Return the key response
	return s.keyToResponse(key, req.APIKey), nil
}

// GetKeys returns all API keys for a user
func (s *Service) GetKeys(ctx context.Context, userID uint) ([]KeyResponse, error) {
	keys, err := s.keyRepo.GetByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get API keys: %w", err)
	}

	responses := make([]KeyResponse, len(keys))
	for i, key := range keys {
		responses[i] = *s.keyToResponse(&key, "")
	}

	logger.Info("Retrieved API keys", "user_id", userID, "count", len(responses))
	return responses, nil
}

// GetKey returns a specific API key for a user
func (s *Service) GetKey(ctx context.Context, userID, keyID uint) (*KeyResponse, error) {
	key, err := s.keyRepo.GetByID(keyID)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	// Verify ownership
	if key.UserID != userID {
		return nil, fmt.Errorf("API key not found")
	}

	return s.keyToResponse(key, ""), nil
}

// UpdateKey updates an existing API key
func (s *Service) UpdateKey(ctx context.Context, userID, keyID uint, req *UpdateKeyRequest) (*KeyResponse, error) {
	logger.Info("Updating API key", "user_id", userID, "key_id", keyID)

	key, err := s.keyRepo.GetByID(keyID)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	// Verify ownership
	if key.UserID != userID {
		return nil, fmt.Errorf("API key not found")
	}

	// Update fields
	updated := false
	if req.Name != "" && req.Name != key.Name {
		key.Name = req.Name
		updated = true
	}

	if req.APIKey != "" {
		// Validate new API key format
		if !utils.ValidateAPIKey(req.APIKey) {
			return nil, fmt.Errorf("invalid API key format")
		}

		// Test the new API key
		err := s.adapterService.ValidateProviderKey(ctx, key.Provider, req.APIKey)
		if err != nil {
			logger.Warn("New API key validation failed", "provider", key.Provider, "error", err)
		}

		// Encrypt the new API key
		encryptedKey, encErr := s.crypto.Encrypt(req.APIKey)
		if encErr != nil {
			return nil, fmt.Errorf("failed to encrypt API key: %w", encErr)
		}

		key.EncryptedAPIKey = encryptedKey
		key.Status = database.StatusTesting
		key.ErrorMessage = ""
		updated = true

		// Update status based on validation
		status := database.StatusActive
		errorMessage := ""
		if err != nil {
			status = database.StatusInvalid
			errorMessage = err.Error()
		}

		if err := s.keyRepo.UpdateStatus(key.ID, status, errorMessage); err != nil {
			logger.Error("Failed to update key status", "error", err, "key_id", key.ID)
		}
	}

	if updated {
		if err := s.keyRepo.Update(key); err != nil {
			return nil, fmt.Errorf("failed to update API key: %w", err)
		}
	}

	logger.Info("API key updated successfully", "key_id", keyID, "user_id", userID)
	return s.keyToResponse(key, ""), nil
}

// DeleteKey deletes an API key
func (s *Service) DeleteKey(ctx context.Context, userID, keyID uint) error {
	logger.Info("Deleting API key", "user_id", userID, "key_id", keyID)

	key, err := s.keyRepo.GetByID(keyID)
	if err != nil {
		return fmt.Errorf("failed to get API key: %w", err)
	}

	// Verify ownership
	if key.UserID != userID {
		return fmt.Errorf("API key not found")
	}

	if err := s.keyRepo.Delete(keyID); err != nil {
		return fmt.Errorf("failed to delete API key: %w", err)
	}

	logger.Info("API key deleted successfully", "key_id", keyID, "user_id", userID)
	return nil
}

// TestKey tests an API key with its provider
func (s *Service) TestKey(ctx context.Context, userID, keyID uint) (*KeyResponse, error) {
	logger.Info("Testing API key", "user_id", userID, "key_id", keyID)

	key, err := s.keyRepo.GetByID(keyID)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	// Verify ownership
	if key.UserID != userID {
		return nil, fmt.Errorf("API key not found")
	}

	// Decrypt the API key
	decryptedKey, err := s.crypto.Decrypt(key.EncryptedAPIKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt API key: %w", err)
	}

	// Test the API key
	err = s.adapterService.ValidateProviderKey(ctx, key.Provider, decryptedKey)

	// Update status based on test result
	status := database.StatusActive
	errorMessage := ""
	if err != nil {
		status = database.StatusInvalid
		errorMessage = err.Error()
		logger.Warn("API key test failed", "key_id", keyID, "provider", key.Provider, "error", err)
	} else {
		logger.Info("API key test successful", "key_id", keyID, "provider", key.Provider)
	}

	if updateErr := s.keyRepo.UpdateStatus(keyID, status, errorMessage); updateErr != nil {
		logger.Error("Failed to update key status after test", "error", updateErr, "key_id", keyID)
	}

	// Refresh the key data
	key, err = s.keyRepo.GetByID(keyID)
	if err != nil {
		return nil, fmt.Errorf("failed to refresh API key: %w", err)
	}

	return s.keyToResponse(key, ""), nil
}

// GetKeysByProvider returns all API keys for a specific provider
func (s *Service) GetKeysByProvider(ctx context.Context, userID uint, provider string) ([]KeyResponse, error) {
	keys, err := s.keyRepo.GetByUserIDAndProvider(userID, provider)
	if err != nil {
		return nil, fmt.Errorf("failed to get API keys for provider: %w", err)
	}

	responses := make([]KeyResponse, len(keys))
	for i, key := range keys {
		responses[i] = *s.keyToResponse(&key, "")
	}

	logger.Info("Retrieved API keys for provider", "user_id", userID, "provider", provider, "count", len(responses))
	return responses, nil
}

// GetKeyStats returns statistics about user's API keys
func (s *Service) GetKeyStats(ctx context.Context, userID uint) (map[string]interface{}, error) {
	keys, err := s.keyRepo.GetByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get API keys: %w", err)
	}

	stats := map[string]interface{}{
		"total_keys":    len(keys),
		"active_keys":   0,
		"invalid_keys":  0,
		"testing_keys":  0,
		"providers":     make(map[string]int),
		"total_requests": int64(0),
		"total_tokens":   int64(0),
	}

	providerCounts := make(map[string]int)
	for _, key := range keys {
		switch key.Status {
		case database.StatusActive:
			stats["active_keys"] = stats["active_keys"].(int) + 1
		case database.StatusInvalid:
			stats["invalid_keys"] = stats["invalid_keys"].(int) + 1
		case database.StatusTesting:
			stats["testing_keys"] = stats["testing_keys"].(int) + 1
		}

		providerCounts[key.Provider]++
		stats["total_requests"] = stats["total_requests"].(int64) + key.RequestCount
		stats["total_tokens"] = stats["total_tokens"].(int64) + key.TotalTokensUsed
	}

	stats["providers"] = providerCounts
	return stats, nil
}

// keyToResponse converts a database key to a response
func (s *Service) keyToResponse(key *database.UserAPIKey, plainKey string) *KeyResponse {
	maskedKey := ""
	if plainKey != "" {
		maskedKey = utils.MaskAPIKey(plainKey)
	} else {
		// Generate a generic masked key for display
		maskedKey = fmt.Sprintf("%s-****...****", key.Provider)
	}

	return &KeyResponse{
		ID:              key.ID,
		Provider:        key.Provider,
		Name:            key.Name,
		Status:          key.Status,
		LastTestedAt:    key.LastTestedAt,
		LastUsedAt:      key.LastUsedAt,
		ErrorMessage:    key.ErrorMessage,
		RequestCount:    key.RequestCount,
		SuccessCount:    key.SuccessCount,
		ErrorCount:      key.ErrorCount,
		TotalTokensUsed: key.TotalTokensUsed,
		CreatedAt:       key.CreatedAt,
		UpdatedAt:       key.UpdatedAt,
		MaskedAPIKey:    maskedKey,
	}
}
