package keys

import (
	"context"
	"testing"

	"llm-proxy-system/internal/adapters"
	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/database"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestKeyService() (*Service, error) {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	
	// Run migrations
	err = db.AutoMigrate(
		&database.User{},
		&database.UserAPIKey{},
		&database.AdminGlobalRoutingRule{},
		&database.UsageLog{},
		&database.WebhookConfig{},
	)
	if err != nil {
		return nil, err
	}
	
	// Setup test config
	cfg := &config.Config{
		EncryptionKey: "12345678901234567890123456789012", // 32 chars
		Provider: struct {
			TimeoutSeconds int `json:"timeout_seconds"`
			MaxRetries     int `json:"max_retries"`
		}{
			TimeoutSeconds: 30,
			MaxRetries:     3,
		},
	}
	
	// Setup adapter service
	adapterService, err := adapters.NewService(db, cfg)
	if err != nil {
		return nil, err
	}
	
	// Create key service
	return NewService(db, cfg, adapterService)
}

func setupTestKeyData(db *gorm.DB) (*database.User, error) {
	// Create test user
	user := &database.User{
		Email:        "<EMAIL>",
		PasswordHash: "hashed_password",
		SystemAPIKey: "sk-sys_test_key",
		Role:         database.RoleUser,
		IsActive:     true,
	}
	
	if err := db.Create(user).Error; err != nil {
		return nil, err
	}
	
	return user, nil
}

func TestNewService(t *testing.T) {
	service, err := setupTestKeyService()
	if err != nil {
		t.Fatalf("Failed to create key service: %v", err)
	}
	
	if service == nil {
		t.Fatal("Service is nil")
	}
	
	if service.keyRepo == nil {
		t.Error("Key repository is nil")
	}
	
	if service.userRepo == nil {
		t.Error("User repository is nil")
	}
	
	if service.crypto == nil {
		t.Error("Crypto service is nil")
	}
	
	if service.adapterService == nil {
		t.Error("Adapter service is nil")
	}
}

func TestCreateKey(t *testing.T) {
	service, err := setupTestKeyService()
	if err != nil {
		t.Fatalf("Failed to create key service: %v", err)
	}
	
	user, err := setupTestKeyData(service.db)
	if err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Test successful key creation
	req := &CreateKeyRequest{
		Provider: "openai",
		Name:     "Test OpenAI Key",
		APIKey:   "sk-test1234567890abcdef1234567890abcdef",
	}
	
	keyResponse, err := service.CreateKey(ctx, user.ID, req)
	if err != nil {
		t.Errorf("Failed to create key: %v", err)
	}
	
	if keyResponse == nil {
		t.Fatal("Key response is nil")
	}
	
	if keyResponse.Provider != req.Provider {
		t.Errorf("Expected provider %s, got %s", req.Provider, keyResponse.Provider)
	}
	
	if keyResponse.Name != req.Name {
		t.Errorf("Expected name %s, got %s", req.Name, keyResponse.Name)
	}
	
	if keyResponse.MaskedAPIKey == "" {
		t.Error("Masked API key is empty")
	}
	
	// Test invalid provider
	invalidReq := &CreateKeyRequest{
		Provider: "invalid-provider",
		Name:     "Test Invalid Key",
		APIKey:   "sk-test1234567890abcdef1234567890abcdef",
	}
	
	_, err = service.CreateKey(ctx, user.ID, invalidReq)
	if err == nil {
		t.Error("Expected error for invalid provider")
	}
	
	if err.Error() != "unsupported provider: invalid-provider" {
		t.Errorf("Expected unsupported provider error, got: %v", err)
	}
}

func TestGetKeys(t *testing.T) {
	service, err := setupTestKeyService()
	if err != nil {
		t.Fatalf("Failed to create key service: %v", err)
	}
	
	user, err := setupTestKeyData(service.db)
	if err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Create test keys
	keys := []*CreateKeyRequest{
		{
			Provider: "openai",
			Name:     "OpenAI Key 1",
			APIKey:   "sk-test1234567890abcdef1234567890abcdef",
		},
		{
			Provider: "gemini",
			Name:     "Gemini Key 1",
			APIKey:   "AIza1234567890abcdef1234567890abcdef",
		},
	}
	
	for _, keyReq := range keys {
		_, err := service.CreateKey(ctx, user.ID, keyReq)
		if err != nil {
			t.Fatalf("Failed to create test key: %v", err)
		}
	}
	
	// Test getting all keys
	keyResponses, err := service.GetKeys(ctx, user.ID)
	if err != nil {
		t.Errorf("Failed to get keys: %v", err)
	}
	
	if len(keyResponses) != len(keys) {
		t.Errorf("Expected %d keys, got %d", len(keys), len(keyResponses))
	}
	
	// Test getting keys by provider
	openaiKeys, err := service.GetKeysByProvider(ctx, user.ID, "openai")
	if err != nil {
		t.Errorf("Failed to get OpenAI keys: %v", err)
	}
	
	if len(openaiKeys) != 1 {
		t.Errorf("Expected 1 OpenAI key, got %d", len(openaiKeys))
	}
	
	if openaiKeys[0].Provider != "openai" {
		t.Errorf("Expected OpenAI provider, got %s", openaiKeys[0].Provider)
	}
}

func TestUpdateKey(t *testing.T) {
	service, err := setupTestKeyService()
	if err != nil {
		t.Fatalf("Failed to create key service: %v", err)
	}
	
	user, err := setupTestKeyData(service.db)
	if err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Create a test key
	createReq := &CreateKeyRequest{
		Provider: "openai",
		Name:     "Original Name",
		APIKey:   "sk-test1234567890abcdef1234567890abcdef",
	}
	
	keyResponse, err := service.CreateKey(ctx, user.ID, createReq)
	if err != nil {
		t.Fatalf("Failed to create test key: %v", err)
	}
	
	// Test updating name
	updateReq := &UpdateKeyRequest{
		Name: "Updated Name",
	}
	
	updatedKey, err := service.UpdateKey(ctx, user.ID, keyResponse.ID, updateReq)
	if err != nil {
		t.Errorf("Failed to update key: %v", err)
	}
	
	if updatedKey.Name != updateReq.Name {
		t.Errorf("Expected name %s, got %s", updateReq.Name, updatedKey.Name)
	}
	
	// Test updating API key
	updateReq = &UpdateKeyRequest{
		APIKey: "sk-new1234567890abcdef1234567890abcdef",
	}
	
	updatedKey, err = service.UpdateKey(ctx, user.ID, keyResponse.ID, updateReq)
	if err != nil {
		t.Errorf("Failed to update API key: %v", err)
	}
	
	// The masked key should be different
	if updatedKey.MaskedAPIKey == keyResponse.MaskedAPIKey {
		t.Error("Masked API key should have changed")
	}
}

func TestDeleteKey(t *testing.T) {
	service, err := setupTestKeyService()
	if err != nil {
		t.Fatalf("Failed to create key service: %v", err)
	}
	
	user, err := setupTestKeyData(service.db)
	if err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Create a test key
	createReq := &CreateKeyRequest{
		Provider: "openai",
		Name:     "Test Key",
		APIKey:   "sk-test1234567890abcdef1234567890abcdef",
	}
	
	keyResponse, err := service.CreateKey(ctx, user.ID, createReq)
	if err != nil {
		t.Fatalf("Failed to create test key: %v", err)
	}
	
	// Delete the key
	err = service.DeleteKey(ctx, user.ID, keyResponse.ID)
	if err != nil {
		t.Errorf("Failed to delete key: %v", err)
	}
	
	// Try to get the deleted key
	_, err = service.GetKey(ctx, user.ID, keyResponse.ID)
	if err == nil {
		t.Error("Expected error when getting deleted key")
	}
}

func TestGetKeyStats(t *testing.T) {
	service, err := setupTestKeyService()
	if err != nil {
		t.Fatalf("Failed to create key service: %v", err)
	}
	
	user, err := setupTestKeyData(service.db)
	if err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Create test keys
	keys := []*CreateKeyRequest{
		{
			Provider: "openai",
			Name:     "OpenAI Key 1",
			APIKey:   "sk-test1234567890abcdef1234567890abcdef",
		},
		{
			Provider: "openai",
			Name:     "OpenAI Key 2",
			APIKey:   "sk-test2234567890abcdef1234567890abcdef",
		},
		{
			Provider: "gemini",
			Name:     "Gemini Key 1",
			APIKey:   "AIza1234567890abcdef1234567890abcdef",
		},
	}
	
	for _, keyReq := range keys {
		_, err := service.CreateKey(ctx, user.ID, keyReq)
		if err != nil {
			t.Fatalf("Failed to create test key: %v", err)
		}
	}
	
	// Get key statistics
	stats, err := service.GetKeyStats(ctx, user.ID)
	if err != nil {
		t.Errorf("Failed to get key stats: %v", err)
	}
	
	if stats["total_keys"] != len(keys) {
		t.Errorf("Expected %d total keys, got %v", len(keys), stats["total_keys"])
	}
	
	providers, ok := stats["providers"].(map[string]int)
	if !ok {
		t.Error("Providers stats not in expected format")
	} else {
		if providers["openai"] != 2 {
			t.Errorf("Expected 2 OpenAI keys, got %d", providers["openai"])
		}
		if providers["gemini"] != 1 {
			t.Errorf("Expected 1 Gemini key, got %d", providers["gemini"])
		}
	}
}
