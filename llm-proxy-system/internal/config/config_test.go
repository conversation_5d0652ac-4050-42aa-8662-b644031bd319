package config

import (
	"os"
	"testing"
	"time"
)

func TestLoad(t *testing.T) {
	// Set required environment variables for testing
	os.Setenv("DATABASE_URL", "postgresql://test:test@localhost:5432/test")
	os.Setenv("JWT_SECRET", "test-jwt-secret-at-least-32-characters-long")
	os.Setenv("ENCRYPTION_KEY", "12345678901234567890123456789012") // Exactly 32 chars
	
	defer func() {
		// Clean up environment variables
		os.Unsetenv("DATABASE_URL")
		os.Unsetenv("JWT_SECRET")
		os.Unsetenv("ENCRYPTION_KEY")
	}()
	
	cfg, err := Load()
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	
	// Test default values
	if cfg.Port != 8080 {
		t.<PERSON><PERSON><PERSON>("Expected port 8080, got %d", cfg.Port)
	}
	
	if cfg.Environment != "development" {
		t.Errorf("Expected environment 'development', got %s", cfg.Environment)
	}
	
	if cfg.JWTExpiresIn != 24*time.Hour {
		t.Errorf("Expected JWT expires in 24h, got %v", cfg.JWTExpiresIn)
	}
	
	if cfg.RateLimit.RequestsPerMinute != 60 {
		t.Errorf("Expected rate limit 60 RPM, got %d", cfg.RateLimit.RequestsPerMinute)
	}
}

func TestValidation(t *testing.T) {
	tests := []struct {
		name        string
		envVars     map[string]string
		expectError bool
		errorMsg    string
	}{
		{
			name: "missing database URL",
			envVars: map[string]string{
				"JWT_SECRET":     "test-jwt-secret-at-least-32-characters-long",
				"ENCRYPTION_KEY": "12345678901234567890123456789012",
			},
			expectError: true,
			errorMsg:    "DATABASE_URL is required",
		},
		{
			name: "missing JWT secret",
			envVars: map[string]string{
				"DATABASE_URL":   "postgresql://test:test@localhost:5432/test",
				"ENCRYPTION_KEY": "12345678901234567890123456789012",
			},
			expectError: true,
			errorMsg:    "JWT_SECRET is required",
		},
		{
			name: "short JWT secret",
			envVars: map[string]string{
				"DATABASE_URL":   "postgresql://test:test@localhost:5432/test",
				"JWT_SECRET":     "short",
				"ENCRYPTION_KEY": "12345678901234567890123456789012",
			},
			expectError: true,
			errorMsg:    "JWT_SECRET must be at least 32 characters long",
		},
		{
			name: "missing encryption key",
			envVars: map[string]string{
				"DATABASE_URL": "postgresql://test:test@localhost:5432/test",
				"JWT_SECRET":   "test-jwt-secret-at-least-32-characters-long",
			},
			expectError: true,
			errorMsg:    "ENCRYPTION_KEY is required",
		},
		{
			name: "wrong encryption key length",
			envVars: map[string]string{
				"DATABASE_URL":   "postgresql://test:test@localhost:5432/test",
				"JWT_SECRET":     "test-jwt-secret-at-least-32-characters-long",
				"ENCRYPTION_KEY": "short",
			},
			expectError: true,
			errorMsg:    "ENCRYPTION_KEY must be exactly 32 characters long",
		},
		{
			name: "valid configuration",
			envVars: map[string]string{
				"DATABASE_URL":   "postgresql://test:test@localhost:5432/test",
				"JWT_SECRET":     "test-jwt-secret-at-least-32-characters-long",
				"ENCRYPTION_KEY": "12345678901234567890123456789012",
			},
			expectError: false,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clear all environment variables
			os.Clearenv()
			
			// Set test environment variables
			for key, value := range tt.envVars {
				os.Setenv(key, value)
			}
			
			_, err := Load()
			
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error, got nil")
				} else if err.Error() != "configuration validation failed: "+tt.errorMsg {
					t.Errorf("Expected error '%s', got '%s'", tt.errorMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error, got %v", err)
				}
			}
		})
	}
}

func TestEnvironmentMethods(t *testing.T) {
	cfg := &Config{Environment: "development"}
	
	if !cfg.IsDevelopment() {
		t.Error("Expected IsDevelopment() to return true")
	}
	
	if cfg.IsProduction() {
		t.Error("Expected IsProduction() to return false")
	}
	
	cfg.Environment = "production"
	
	if cfg.IsDevelopment() {
		t.Error("Expected IsDevelopment() to return false")
	}
	
	if !cfg.IsProduction() {
		t.Error("Expected IsProduction() to return true")
	}
}
