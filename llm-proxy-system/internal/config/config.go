package config

import (
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

// Config holds all configuration for the application
type Config struct {
	// Server configuration
	Port        int    `json:"port"`
	Environment string `json:"environment"`
	
	// Database configuration
	DatabaseURL string `json:"database_url"`
	RedisURL    string `json:"redis_url"`
	
	// JWT configuration
	JWTSecret    string        `json:"-"` // Don't expose in JSON
	JWTExpiresIn time.Duration `json:"jwt_expires_in"`
	
	// Encryption configuration
	EncryptionKey string `json:"-"` // Don't expose in JSON
	
	// Rate limiting configuration
	RateLimit struct {
		RequestsPerMinute int           `json:"requests_per_minute"`
		WindowSize        time.Duration `json:"window_size"`
	} `json:"rate_limit"`
	
	// Provider configuration
	Provider struct {
		TimeoutSeconds int `json:"timeout_seconds"`
		MaxRetries     int `json:"max_retries"`
	} `json:"provider"`
	
	// Webhook configuration
	Webhook struct {
		Enabled bool   `json:"enabled"`
		Secret  string `json:"-"` // Don't expose in JSON
	} `json:"webhook"`
	
	// CORS configuration
	CORS struct {
		AllowedOrigins []string `json:"allowed_origins"`
		AllowedMethods []string `json:"allowed_methods"`
		AllowedHeaders []string `json:"allowed_headers"`
	} `json:"cors"`
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	// Load .env file if it exists (for development)
	_ = godotenv.Load()
	
	cfg := &Config{
		Port:        getEnvAsInt("PORT", 8080),
		Environment: getEnv("ENVIRONMENT", "development"),
		DatabaseURL: getEnv("DATABASE_URL", ""),
		RedisURL:    getEnv("REDIS_URL", "redis://localhost:6379"),
		JWTSecret:   getEnv("JWT_SECRET", ""),
		EncryptionKey: getEnv("ENCRYPTION_KEY", ""),
	}
	
	// Parse JWT expiration
	jwtExpiresIn := getEnv("JWT_EXPIRES_IN", "24h")
	duration, err := time.ParseDuration(jwtExpiresIn)
	if err != nil {
		return nil, fmt.Errorf("invalid JWT_EXPIRES_IN format: %w", err)
	}
	cfg.JWTExpiresIn = duration
	
	// Rate limiting configuration
	cfg.RateLimit.RequestsPerMinute = getEnvAsInt("RATE_LIMIT_RPM", 60)
	cfg.RateLimit.WindowSize = time.Minute
	
	// Provider configuration
	cfg.Provider.TimeoutSeconds = getEnvAsInt("PROVIDER_TIMEOUT_SECONDS", 30)
	cfg.Provider.MaxRetries = getEnvAsInt("PROVIDER_MAX_RETRIES", 3)
	
	// Webhook configuration
	cfg.Webhook.Enabled = getEnvAsBool("WEBHOOK_ENABLED", true)
	cfg.Webhook.Secret = getEnv("WEBHOOK_SECRET", "")
	
	// CORS configuration
	cfg.CORS.AllowedOrigins = getEnvAsSlice("CORS_ALLOWED_ORIGINS", []string{"*"})
	cfg.CORS.AllowedMethods = getEnvAsSlice("CORS_ALLOWED_METHODS", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"})
	cfg.CORS.AllowedHeaders = getEnvAsSlice("CORS_ALLOWED_HEADERS", []string{"*"})
	
	// Validate required configuration
	if err := cfg.validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}
	
	return cfg, nil
}

// validate validates the configuration
func (c *Config) validate() error {
	if c.DatabaseURL == "" {
		return fmt.Errorf("DATABASE_URL is required")
	}
	
	if c.JWTSecret == "" {
		return fmt.Errorf("JWT_SECRET is required")
	}
	
	if len(c.JWTSecret) < 32 {
		return fmt.Errorf("JWT_SECRET must be at least 32 characters long")
	}
	
	if c.EncryptionKey == "" {
		return fmt.Errorf("ENCRYPTION_KEY is required")
	}
	
	if len(c.EncryptionKey) != 32 {
		return fmt.Errorf("ENCRYPTION_KEY must be exactly 32 characters long")
	}
	
	if c.Port < 1 || c.Port > 65535 {
		return fmt.Errorf("PORT must be between 1 and 65535")
	}
	
	return nil
}

// IsDevelopment returns true if the environment is development
func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}

// IsProduction returns true if the environment is production
func (c *Config) IsProduction() bool {
	return c.Environment == "production"
}

// Helper functions for environment variable parsing

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvAsSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		// Simple comma-separated parsing
		// In production, you might want more sophisticated parsing
		return []string{value}
	}
	return defaultValue
}
