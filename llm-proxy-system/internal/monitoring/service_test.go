package monitoring

import (
	"context"
	"testing"
	"time"

	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/database"

	"github.com/redis/go-redis/v9"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestMonitoringService() (*Service, error) {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	
	// Run migrations
	err = db.AutoMigrate(
		&database.User{},
		&database.UserAPIKey{},
		&database.AdminGlobalRoutingRule{},
		&database.UsageLog{},
		&database.WebhookConfig{},
	)
	if err != nil {
		return nil, err
	}
	
	// Setup test Redis (using miniredis for testing)
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   1, // Use test database
	})
	
	// Setup test config
	cfg := &config.Config{
		Environment: "test",
		EncryptionKey: "12345678901234567890123456789012", // 32 chars
	}
	
	// Create monitoring service
	return NewService(db, rdb, cfg), nil
}

func setupTestData(db *gorm.DB) error {
	// Create test users
	users := []database.User{
		{
			Email:        "<EMAIL>",
			PasswordHash: "hashed_password",
			SystemAPIKey: "sk-sys_test_key_1",
			Role:         database.RoleUser,
			IsActive:     true,
		},
		{
			Email:        "<EMAIL>",
			PasswordHash: "hashed_password",
			SystemAPIKey: "sk-sys_test_key_2",
			Role:         database.RoleUser,
			IsActive:     true,
		},
	}
	
	for _, user := range users {
		if err := db.Create(&user).Error; err != nil {
			return err
		}
	}
	
	// Create test API keys
	apiKeys := []database.UserAPIKey{
		{
			UserID:   1,
			Provider: "openai",
			Name:     "Test OpenAI Key",
			Status:   database.StatusActive,
		},
		{
			UserID:   1,
			Provider: "gemini",
			Name:     "Test Gemini Key",
			Status:   database.StatusActive,
		},
		{
			UserID:   2,
			Provider: "claude",
			Name:     "Test Claude Key",
			Status:   database.StatusInvalid,
		},
	}
	
	for _, key := range apiKeys {
		if err := db.Create(&key).Error; err != nil {
			return err
		}
	}
	
	// Create test usage logs
	usageLogs := []database.UsageLog{
		{
			UserID:      1,
			Provider:    "openai",
			Model:       "gpt-3.5-turbo",
			Status:      "success",
			Duration:    1500,
			TotalTokens: 150,
		},
		{
			UserID:      1,
			Provider:    "gemini",
			Model:       "gemini-pro",
			Status:      "success",
			Duration:    2000,
			TotalTokens: 200,
		},
		{
			UserID:      2,
			Provider:    "claude",
			Model:       "claude-3-sonnet",
			Status:      "error",
			Duration:    500,
			TotalTokens: 0,
			ErrorMessage: "API key invalid",
		},
	}
	
	for _, log := range usageLogs {
		if err := db.Create(&log).Error; err != nil {
			return err
		}
	}
	
	return nil
}

func TestNewService(t *testing.T) {
	service, err := setupTestMonitoringService()
	if err != nil {
		t.Fatalf("Failed to create monitoring service: %v", err)
	}
	
	if service == nil {
		t.Fatal("Service is nil")
	}
	
	if service.metrics == nil {
		t.Error("Metrics collector is nil")
	}
	
	if service.analytics == nil {
		t.Error("Analytics engine is nil")
	}
	
	if service.alerts == nil {
		t.Error("Alert manager is nil")
	}
}

func TestGetSystemMetrics(t *testing.T) {
	service, err := setupTestMonitoringService()
	if err != nil {
		t.Fatalf("Failed to create monitoring service: %v", err)
	}
	
	ctx := context.Background()
	
	metrics, err := service.GetSystemMetrics(ctx)
	if err != nil {
		t.Errorf("Failed to get system metrics: %v", err)
	}
	
	if metrics == nil {
		t.Fatal("Metrics is nil")
	}
	
	// Check that metrics have reasonable values
	if metrics.CPU.Cores <= 0 {
		t.Error("CPU cores should be greater than 0")
	}
	
	if metrics.Memory.TotalMB <= 0 {
		t.Error("Total memory should be greater than 0")
	}
	
	if metrics.Application.Uptime <= 0 {
		t.Error("Application uptime should be greater than 0")
	}
	
	if metrics.Application.Goroutines <= 0 {
		t.Error("Goroutines count should be greater than 0")
	}
}

func TestGetBusinessMetrics(t *testing.T) {
	service, err := setupTestMonitoringService()
	if err != nil {
		t.Fatalf("Failed to create monitoring service: %v", err)
	}
	
	// Setup test data
	if err := setupTestData(service.db); err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	metrics, err := service.GetBusinessMetrics(ctx)
	if err != nil {
		t.Errorf("Failed to get business metrics: %v", err)
	}
	
	if metrics == nil {
		t.Fatal("Metrics is nil")
	}
	
	// Check user metrics
	if metrics.Users.Total != 2 {
		t.Errorf("Expected 2 total users, got %d", metrics.Users.Total)
	}
	
	if metrics.Users.Active != 2 {
		t.Errorf("Expected 2 active users, got %d", metrics.Users.Active)
	}
	
	// Check API key metrics
	if metrics.APIKeys.Total != 3 {
		t.Errorf("Expected 3 total API keys, got %d", metrics.APIKeys.Total)
	}
	
	if metrics.APIKeys.Active != 2 {
		t.Errorf("Expected 2 active API keys, got %d", metrics.APIKeys.Active)
	}
	
	if metrics.APIKeys.Invalid != 1 {
		t.Errorf("Expected 1 invalid API key, got %d", metrics.APIKeys.Invalid)
	}
	
	// Check request metrics
	if metrics.Requests.Total != 3 {
		t.Errorf("Expected 3 total requests, got %d", metrics.Requests.Total)
	}
	
	expectedSuccessRate := float64(2) / float64(3) * 100
	if metrics.Requests.SuccessRate != expectedSuccessRate {
		t.Errorf("Expected success rate %.2f, got %.2f", expectedSuccessRate, metrics.Requests.SuccessRate)
	}
	
	// Check provider metrics
	if len(metrics.Providers) == 0 {
		t.Error("Expected provider metrics, got none")
	}
	
	if openaiMetrics, exists := metrics.Providers["openai"]; exists {
		if openaiMetrics.Requests != 1 {
			t.Errorf("Expected 1 OpenAI request, got %d", openaiMetrics.Requests)
		}
		if openaiMetrics.SuccessRate != 100.0 {
			t.Errorf("Expected 100%% OpenAI success rate, got %.2f", openaiMetrics.SuccessRate)
		}
	}
}

func TestRecordRequest(t *testing.T) {
	service, err := setupTestMonitoringService()
	if err != nil {
		t.Fatalf("Failed to create monitoring service: %v", err)
	}
	
	ctx := context.Background()
	
	metric := &RequestMetric{
		Timestamp:    time.Now(),
		UserID:       1,
		Provider:     "openai",
		Model:        "gpt-4",
		Method:       "POST /api/v1/llm/proxy",
		StatusCode:   200,
		ResponseTime: 1500.0,
		TokensUsed:   100,
		Success:      true,
		IPAddress:    "127.0.0.1",
		UserAgent:    "test-agent",
	}
	
	err = service.RecordRequest(ctx, metric)
	if err != nil {
		t.Errorf("Failed to record request: %v", err)
	}
}

func TestRecordAPIKeyUsage(t *testing.T) {
	service, err := setupTestMonitoringService()
	if err != nil {
		t.Fatalf("Failed to create monitoring service: %v", err)
	}
	
	ctx := context.Background()
	
	usage := &APIKeyUsage{
		Timestamp:  time.Now(),
		UserID:     1,
		APIKeyID:   1,
		Provider:   "openai",
		Requests:   1,
		TokensUsed: 100,
		Success:    true,
		ErrorCount: 0,
	}
	
	err = service.RecordAPIKeyUsage(ctx, usage)
	if err != nil {
		t.Errorf("Failed to record API key usage: %v", err)
	}
}

func TestGetAnalytics(t *testing.T) {
	service, err := setupTestMonitoringService()
	if err != nil {
		t.Fatalf("Failed to create monitoring service: %v", err)
	}
	
	// Setup test data
	if err := setupTestData(service.db); err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	endTime := time.Now()
	startTime := endTime.Add(-24 * time.Hour)
	
	analytics, err := service.GetAnalytics(ctx, "day", startTime, endTime)
	if err != nil {
		t.Errorf("Failed to get analytics: %v", err)
	}
	
	if analytics == nil {
		t.Fatal("Analytics is nil")
	}
	
	// Check overview
	if analytics.Overview.TotalRequests != 3 {
		t.Errorf("Expected 3 total requests, got %d", analytics.Overview.TotalRequests)
	}
	
	if analytics.Overview.SuccessfulReqs != 2 {
		t.Errorf("Expected 2 successful requests, got %d", analytics.Overview.SuccessfulReqs)
	}
	
	if analytics.Overview.FailedReqs != 1 {
		t.Errorf("Expected 1 failed request, got %d", analytics.Overview.FailedReqs)
	}
	
	// Check trends
	if len(analytics.Trends.RequestsOverTime) == 0 {
		t.Error("Expected request trend data, got none")
	}
	
	// Check providers
	if len(analytics.Providers) == 0 {
		t.Error("Expected provider analytics, got none")
	}
	
	// Check insights
	if len(analytics.Insights) == 0 {
		t.Error("Expected insights, got none")
	}
}

func TestAlertManagement(t *testing.T) {
	service, err := setupTestMonitoringService()
	if err != nil {
		t.Fatalf("Failed to create monitoring service: %v", err)
	}
	
	ctx := context.Background()
	
	// Test creating an alert
	rule := &AlertRule{
		Name:        "Test Alert",
		Description: "Test alert rule",
		Metric:      "cpu_usage",
		Condition:   "gt",
		Threshold:   80.0,
		Duration:    5 * time.Minute,
		Severity:    "warning",
		Enabled:     true,
		Channels:    []string{"email"},
	}
	
	err = service.CreateAlert(ctx, rule)
	if err != nil {
		t.Errorf("Failed to create alert: %v", err)
	}
	
	if rule.ID == "" {
		t.Error("Alert rule ID should be set after creation")
	}
	
	// Test getting alerts
	alerts, err := service.GetAlerts(ctx)
	if err != nil {
		t.Errorf("Failed to get alerts: %v", err)
	}
	
	// Should have no active alerts initially
	if len(alerts) != 0 {
		t.Errorf("Expected 0 active alerts, got %d", len(alerts))
	}
}
