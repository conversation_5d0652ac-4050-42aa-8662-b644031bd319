package monitoring

import (
	"context"
	"fmt"
	"sync"
	"time"

	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/logger"
)

// AlertManager handles alert rules and notifications
type AlertManager struct {
	config      *config.Config
	rules       map[string]*AlertRule
	activeAlerts map[string]*Alert
	mu          sync.RWMutex
}

// NewAlertManager creates a new alert manager
func NewAlertManager(cfg *config.Config) *AlertManager {
	am := &AlertManager{
		config:       cfg,
		rules:        make(map[string]*AlertRule),
		activeAlerts: make(map[string]*Alert),
	}
	
	// Initialize default alert rules
	am.initializeDefaultRules()
	
	return am
}

// AlertRule defines conditions for triggering alerts
type AlertRule struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Metric      string                 `json:"metric"`
	Condition   string                 `json:"condition"` // "gt", "lt", "eq"
	Threshold   float64                `json:"threshold"`
	Duration    time.Duration          `json:"duration"`
	Severity    string                 `json:"severity"` // "info", "warning", "error", "critical"
	Enabled     bool                   `json:"enabled"`
	Channels    []string               `json:"channels"` // "email", "webhook", "slack"
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// Alert represents an active alert
type Alert struct {
	ID          string                 `json:"id"`
	RuleID      string                 `json:"rule_id"`
	RuleName    string                 `json:"rule_name"`
	Severity    string                 `json:"severity"`
	Message     string                 `json:"message"`
	Value       float64                `json:"value"`
	Threshold   float64                `json:"threshold"`
	Status      string                 `json:"status"` // "firing", "resolved"
	StartTime   time.Time              `json:"start_time"`
	EndTime     *time.Time             `json:"end_time,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	NotifiedAt  *time.Time             `json:"notified_at,omitempty"`
}

// AlertCondition represents alert evaluation state
type AlertCondition struct {
	RuleID      string
	Value       float64
	Threshold   float64
	Met         bool
	FirstMetAt  time.Time
	LastChecked time.Time
}

// CreateRule creates a new alert rule
func (am *AlertManager) CreateRule(ctx context.Context, rule *AlertRule) error {
	am.mu.Lock()
	defer am.mu.Unlock()
	
	if rule.ID == "" {
		rule.ID = fmt.Sprintf("rule_%d", time.Now().Unix())
	}
	
	rule.CreatedAt = time.Now()
	rule.UpdatedAt = time.Now()
	
	am.rules[rule.ID] = rule
	
	logger.Info("Alert rule created", "rule_id", rule.ID, "name", rule.Name)
	return nil
}

// UpdateRule updates an existing alert rule
func (am *AlertManager) UpdateRule(ctx context.Context, ruleID string, updates *AlertRule) error {
	am.mu.Lock()
	defer am.mu.Unlock()
	
	rule, exists := am.rules[ruleID]
	if !exists {
		return fmt.Errorf("alert rule not found: %s", ruleID)
	}
	
	// Update fields
	if updates.Name != "" {
		rule.Name = updates.Name
	}
	if updates.Description != "" {
		rule.Description = updates.Description
	}
	if updates.Threshold != 0 {
		rule.Threshold = updates.Threshold
	}
	if updates.Duration != 0 {
		rule.Duration = updates.Duration
	}
	if updates.Severity != "" {
		rule.Severity = updates.Severity
	}
	if updates.Channels != nil {
		rule.Channels = updates.Channels
	}
	
	rule.UpdatedAt = time.Now()
	
	logger.Info("Alert rule updated", "rule_id", ruleID)
	return nil
}

// DeleteRule deletes an alert rule
func (am *AlertManager) DeleteRule(ctx context.Context, ruleID string) error {
	am.mu.Lock()
	defer am.mu.Unlock()
	
	if _, exists := am.rules[ruleID]; !exists {
		return fmt.Errorf("alert rule not found: %s", ruleID)
	}
	
	delete(am.rules, ruleID)
	
	// Resolve any active alerts for this rule
	for alertID, alert := range am.activeAlerts {
		if alert.RuleID == ruleID {
			alert.Status = "resolved"
			now := time.Now()
			alert.EndTime = &now
		}
	}
	
	logger.Info("Alert rule deleted", "rule_id", ruleID)
	return nil
}

// GetRules returns all alert rules
func (am *AlertManager) GetRules(ctx context.Context) ([]*AlertRule, error) {
	am.mu.RLock()
	defer am.mu.RUnlock()
	
	rules := make([]*AlertRule, 0, len(am.rules))
	for _, rule := range am.rules {
		rules = append(rules, rule)
	}
	
	return rules, nil
}

// GetActiveAlerts returns all active alerts
func (am *AlertManager) GetActiveAlerts(ctx context.Context) ([]Alert, error) {
	am.mu.RLock()
	defer am.mu.RUnlock()
	
	alerts := make([]Alert, 0, len(am.activeAlerts))
	for _, alert := range am.activeAlerts {
		if alert.Status == "firing" {
			alerts = append(alerts, *alert)
		}
	}
	
	return alerts, nil
}

// CheckAlerts evaluates alert conditions against current metrics
func (am *AlertManager) CheckAlerts(ctx context.Context, metrics *SystemMetrics) {
	am.mu.Lock()
	defer am.mu.Unlock()
	
	for _, rule := range am.rules {
		if !rule.Enabled {
			continue
		}
		
		// Extract metric value
		value := am.extractMetricValue(metrics, rule.Metric)
		
		// Evaluate condition
		conditionMet := am.evaluateCondition(value, rule.Condition, rule.Threshold)
		
		alertID := fmt.Sprintf("%s_%s", rule.ID, rule.Metric)
		existingAlert, alertExists := am.activeAlerts[alertID]
		
		if conditionMet {
			if !alertExists {
				// Create new alert
				alert := &Alert{
					ID:        alertID,
					RuleID:    rule.ID,
					RuleName:  rule.Name,
					Severity:  rule.Severity,
					Message:   fmt.Sprintf("%s: %s %.2f (threshold: %.2f)", rule.Name, rule.Metric, value, rule.Threshold),
					Value:     value,
					Threshold: rule.Threshold,
					Status:    "firing",
					StartTime: time.Now(),
				}
				
				am.activeAlerts[alertID] = alert
				
				// Send notification
				go am.sendNotification(alert, rule)
				
				logger.Warn("Alert triggered", "rule", rule.Name, "value", value, "threshold", rule.Threshold)
			} else if existingAlert.Status == "resolved" {
				// Re-trigger resolved alert
				existingAlert.Status = "firing"
				existingAlert.StartTime = time.Now()
				existingAlert.EndTime = nil
				existingAlert.Value = value
				
				go am.sendNotification(existingAlert, rule)
				
				logger.Warn("Alert re-triggered", "rule", rule.Name, "value", value)
			}
		} else {
			if alertExists && existingAlert.Status == "firing" {
				// Resolve alert
				existingAlert.Status = "resolved"
				now := time.Now()
				existingAlert.EndTime = &now
				
				logger.Info("Alert resolved", "rule", rule.Name, "value", value)
			}
		}
	}
}

// Helper methods

func (am *AlertManager) extractMetricValue(metrics *SystemMetrics, metricName string) float64 {
	switch metricName {
	case "cpu_usage":
		return metrics.CPU.UsagePercent
	case "memory_usage":
		return metrics.Memory.UsagePercent
	case "error_rate":
		return metrics.Application.ErrorRate
	case "response_time":
		return metrics.Application.AvgResponseTime
	case "requests_per_minute":
		return float64(metrics.Application.RequestsPerMin)
	case "database_connections":
		return float64(metrics.Database.Connections)
	case "cache_hit_rate":
		return metrics.Cache.HitRate
	default:
		return 0
	}
}

func (am *AlertManager) evaluateCondition(value float64, condition string, threshold float64) bool {
	switch condition {
	case "gt":
		return value > threshold
	case "lt":
		return value < threshold
	case "eq":
		return value == threshold
	case "gte":
		return value >= threshold
	case "lte":
		return value <= threshold
	default:
		return false
	}
}

func (am *AlertManager) sendNotification(alert *Alert, rule *AlertRule) {
	for _, channel := range rule.Channels {
		switch channel {
		case "email":
			am.sendEmailNotification(alert, rule)
		case "webhook":
			am.sendWebhookNotification(alert, rule)
		case "slack":
			am.sendSlackNotification(alert, rule)
		}
	}
	
	now := time.Now()
	alert.NotifiedAt = &now
}

func (am *AlertManager) sendEmailNotification(alert *Alert, rule *AlertRule) {
	// Mock email notification
	logger.Info("Email notification sent", 
		"alert_id", alert.ID, 
		"rule", rule.Name, 
		"severity", alert.Severity,
		"message", alert.Message)
}

func (am *AlertManager) sendWebhookNotification(alert *Alert, rule *AlertRule) {
	// Mock webhook notification
	logger.Info("Webhook notification sent", 
		"alert_id", alert.ID, 
		"rule", rule.Name, 
		"severity", alert.Severity)
}

func (am *AlertManager) sendSlackNotification(alert *Alert, rule *AlertRule) {
	// Mock Slack notification
	logger.Info("Slack notification sent", 
		"alert_id", alert.ID, 
		"rule", rule.Name, 
		"severity", alert.Severity)
}

func (am *AlertManager) initializeDefaultRules() {
	defaultRules := []*AlertRule{
		{
			ID:          "high_cpu_usage",
			Name:        "High CPU Usage",
			Description: "CPU usage is above 80%",
			Metric:      "cpu_usage",
			Condition:   "gt",
			Threshold:   80.0,
			Duration:    5 * time.Minute,
			Severity:    "warning",
			Enabled:     true,
			Channels:    []string{"email", "webhook"},
		},
		{
			ID:          "high_memory_usage",
			Name:        "High Memory Usage",
			Description: "Memory usage is above 85%",
			Metric:      "memory_usage",
			Condition:   "gt",
			Threshold:   85.0,
			Duration:    5 * time.Minute,
			Severity:    "warning",
			Enabled:     true,
			Channels:    []string{"email", "webhook"},
		},
		{
			ID:          "high_error_rate",
			Name:        "High Error Rate",
			Description: "Error rate is above 5%",
			Metric:      "error_rate",
			Condition:   "gt",
			Threshold:   5.0,
			Duration:    2 * time.Minute,
			Severity:    "error",
			Enabled:     true,
			Channels:    []string{"email", "webhook", "slack"},
		},
		{
			ID:          "slow_response_time",
			Name:        "Slow Response Time",
			Description: "Average response time is above 5 seconds",
			Metric:      "response_time",
			Condition:   "gt",
			Threshold:   5000.0,
			Duration:    3 * time.Minute,
			Severity:    "warning",
			Enabled:     true,
			Channels:    []string{"email"},
		},
		{
			ID:          "low_cache_hit_rate",
			Name:        "Low Cache Hit Rate",
			Description: "Cache hit rate is below 80%",
			Metric:      "cache_hit_rate",
			Condition:   "lt",
			Threshold:   80.0,
			Duration:    10 * time.Minute,
			Severity:    "info",
			Enabled:     true,
			Channels:    []string{"email"},
		},
	}
	
	for _, rule := range defaultRules {
		rule.CreatedAt = time.Now()
		rule.UpdatedAt = time.Now()
		am.rules[rule.ID] = rule
	}
	
	logger.Info("Default alert rules initialized", "count", len(defaultRules))
}
