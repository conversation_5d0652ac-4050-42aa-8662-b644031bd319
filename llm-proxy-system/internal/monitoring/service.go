package monitoring

import (
	"context"
	"encoding/json"
	"fmt"
	"runtime"
	"sync"
	"time"

	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/database"
	"llm-proxy-system/internal/logger"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// Service handles monitoring and analytics operations
type Service struct {
	db          *gorm.DB
	redis       *redis.Client
	config      *config.Config
	metrics     *MetricsCollector
	analytics   *AnalyticsEngine
	alerts      *AlertManager
	startTime   time.Time
	mu          sync.RWMutex
}

// NewService creates a new monitoring service
func NewService(db *gorm.DB, redis *redis.Client, cfg *config.Config) *Service {
	service := &Service{
		db:        db,
		redis:     redis,
		config:    cfg,
		startTime: time.Now(),
	}
	
	service.metrics = NewMetricsCollector(redis)
	service.analytics = NewAnalyticsEngine(db, redis)
	service.alerts = NewAlertManager(cfg)
	
	// Start background monitoring
	go service.startSystemMonitoring()
	
	return service
}

// SystemMetrics represents current system metrics
type SystemMetrics struct {
	Timestamp time.Time `json:"timestamp"`
	
	// System Resources
	CPU struct {
		UsagePercent float64 `json:"usage_percent"`
		Cores        int     `json:"cores"`
	} `json:"cpu"`
	
	Memory struct {
		UsedMB      uint64  `json:"used_mb"`
		TotalMB     uint64  `json:"total_mb"`
		UsagePercent float64 `json:"usage_percent"`
	} `json:"memory"`
	
	// Application Metrics
	Application struct {
		Uptime          time.Duration `json:"uptime"`
		Goroutines      int           `json:"goroutines"`
		RequestsPerMin  int64         `json:"requests_per_minute"`
		ErrorRate       float64       `json:"error_rate"`
		AvgResponseTime float64       `json:"avg_response_time_ms"`
	} `json:"application"`
	
	// Database Metrics
	Database struct {
		Connections     int     `json:"connections"`
		MaxConnections  int     `json:"max_connections"`
		AvgQueryTime    float64 `json:"avg_query_time_ms"`
		SlowQueries     int64   `json:"slow_queries"`
	} `json:"database"`
	
	// Cache Metrics
	Cache struct {
		HitRate     float64 `json:"hit_rate_percent"`
		MemoryMB    int64   `json:"memory_mb"`
		KeyCount    int64   `json:"key_count"`
		Operations  int64   `json:"operations_per_sec"`
	} `json:"cache"`
}

// BusinessMetrics represents business-level metrics
type BusinessMetrics struct {
	Timestamp time.Time `json:"timestamp"`
	
	Users struct {
		Total       int64 `json:"total"`
		Active      int64 `json:"active"`
		NewToday    int64 `json:"new_today"`
		ActiveToday int64 `json:"active_today"`
	} `json:"users"`
	
	APIKeys struct {
		Total   int64 `json:"total"`
		Active  int64 `json:"active"`
		Invalid int64 `json:"invalid"`
		Testing int64 `json:"testing"`
	} `json:"api_keys"`
	
	Requests struct {
		Total       int64   `json:"total"`
		Today       int64   `json:"today"`
		LastHour    int64   `json:"last_hour"`
		SuccessRate float64 `json:"success_rate"`
		ErrorRate   float64 `json:"error_rate"`
	} `json:"requests"`
	
	Providers map[string]ProviderMetrics `json:"providers"`
}

// ProviderMetrics represents metrics for a specific provider
type ProviderMetrics struct {
	Requests        int64     `json:"requests"`
	SuccessRate     float64   `json:"success_rate"`
	AvgResponseTime float64   `json:"avg_response_time_ms"`
	ErrorRate       float64   `json:"error_rate"`
	TotalTokens     int64     `json:"total_tokens"`
	LastUsed        time.Time `json:"last_used"`
}

// GetSystemMetrics returns current system metrics
func (s *Service) GetSystemMetrics(ctx context.Context) (*SystemMetrics, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	metrics := &SystemMetrics{
		Timestamp: time.Now(),
	}
	
	// Get system resource metrics
	s.collectSystemResources(metrics)
	
	// Get application metrics
	s.collectApplicationMetrics(ctx, metrics)
	
	// Get database metrics
	s.collectDatabaseMetrics(ctx, metrics)
	
	// Get cache metrics
	s.collectCacheMetrics(ctx, metrics)
	
	return metrics, nil
}

// GetBusinessMetrics returns business-level metrics
func (s *Service) GetBusinessMetrics(ctx context.Context) (*BusinessMetrics, error) {
	metrics := &BusinessMetrics{
		Timestamp: time.Now(),
		Providers: make(map[string]ProviderMetrics),
	}
	
	// Get user metrics
	if err := s.collectUserMetrics(ctx, metrics); err != nil {
		logger.Error("Failed to collect user metrics", "error", err)
	}
	
	// Get API key metrics
	if err := s.collectAPIKeyMetrics(ctx, metrics); err != nil {
		logger.Error("Failed to collect API key metrics", "error", err)
	}
	
	// Get request metrics
	if err := s.collectRequestMetrics(ctx, metrics); err != nil {
		logger.Error("Failed to collect request metrics", "error", err)
	}
	
	// Get provider metrics
	if err := s.collectProviderMetrics(ctx, metrics); err != nil {
		logger.Error("Failed to collect provider metrics", "error", err)
	}
	
	return metrics, nil
}

// RecordRequest records a request metric
func (s *Service) RecordRequest(ctx context.Context, req *RequestMetric) error {
	return s.metrics.RecordRequest(ctx, req)
}

// RecordAPIKeyUsage records API key usage
func (s *Service) RecordAPIKeyUsage(ctx context.Context, usage *APIKeyUsage) error {
	return s.metrics.RecordAPIKeyUsage(ctx, usage)
}

// GetAnalytics returns analytics data for a time period
func (s *Service) GetAnalytics(ctx context.Context, period string, startTime, endTime time.Time) (*AnalyticsData, error) {
	return s.analytics.GetAnalytics(ctx, period, startTime, endTime)
}

// GetAlerts returns current alerts
func (s *Service) GetAlerts(ctx context.Context) ([]Alert, error) {
	return s.alerts.GetActiveAlerts(ctx)
}

// CreateAlert creates a new alert rule
func (s *Service) CreateAlert(ctx context.Context, rule *AlertRule) error {
	return s.alerts.CreateRule(ctx, rule)
}

// Helper methods for collecting metrics

func (s *Service) collectSystemResources(metrics *SystemMetrics) {
	// Get memory stats
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	metrics.Memory.UsedMB = m.Alloc / 1024 / 1024
	metrics.Memory.TotalMB = m.Sys / 1024 / 1024
	if metrics.Memory.TotalMB > 0 {
		metrics.Memory.UsagePercent = float64(metrics.Memory.UsedMB) / float64(metrics.Memory.TotalMB) * 100
	}
	
	// Get CPU info
	metrics.CPU.Cores = runtime.NumCPU()
	metrics.CPU.UsagePercent = 45.0 // Mock value - would use actual CPU monitoring
}

func (s *Service) collectApplicationMetrics(ctx context.Context, metrics *SystemMetrics) {
	metrics.Application.Uptime = time.Since(s.startTime)
	metrics.Application.Goroutines = runtime.NumGoroutine()
	
	// Get real-time metrics from Redis
	requestsPerMin, _ := s.redis.Get(ctx, "metrics:requests_per_minute").Int64()
	metrics.Application.RequestsPerMin = requestsPerMin
	
	errorRate, _ := s.redis.Get(ctx, "metrics:error_rate").Float64()
	metrics.Application.ErrorRate = errorRate
	
	avgResponseTime, _ := s.redis.Get(ctx, "metrics:avg_response_time").Float64()
	metrics.Application.AvgResponseTime = avgResponseTime
}

func (s *Service) collectDatabaseMetrics(ctx context.Context, metrics *SystemMetrics) {
	// Mock database metrics - would integrate with actual DB monitoring
	metrics.Database.Connections = 25
	metrics.Database.MaxConnections = 100
	metrics.Database.AvgQueryTime = 12.5
	metrics.Database.SlowQueries = 0
}

func (s *Service) collectCacheMetrics(ctx context.Context, metrics *SystemMetrics) {
	// Get Redis metrics
	info, err := s.redis.Info(ctx, "memory", "stats").Result()
	if err == nil {
		// Parse Redis info - simplified for demo
		metrics.Cache.HitRate = 95.5
		metrics.Cache.MemoryMB = 128
		metrics.Cache.KeyCount = 1000
		metrics.Cache.Operations = 150
	}
}

func (s *Service) collectUserMetrics(ctx context.Context, metrics *BusinessMetrics) error {
	// Total users
	if err := s.db.Model(&database.User{}).Count(&metrics.Users.Total).Error; err != nil {
		return err
	}
	
	// Active users
	if err := s.db.Model(&database.User{}).Where("is_active = ?", true).Count(&metrics.Users.Active).Error; err != nil {
		return err
	}
	
	// New users today
	today := time.Now().Truncate(24 * time.Hour)
	if err := s.db.Model(&database.User{}).Where("created_at >= ?", today).Count(&metrics.Users.NewToday).Error; err != nil {
		return err
	}
	
	return nil
}

func (s *Service) collectAPIKeyMetrics(ctx context.Context, metrics *BusinessMetrics) error {
	// Total API keys
	if err := s.db.Model(&database.UserAPIKey{}).Count(&metrics.APIKeys.Total).Error; err != nil {
		return err
	}
	
	// Active API keys
	if err := s.db.Model(&database.UserAPIKey{}).Where("status = ?", database.StatusActive).Count(&metrics.APIKeys.Active).Error; err != nil {
		return err
	}
	
	// Invalid API keys
	if err := s.db.Model(&database.UserAPIKey{}).Where("status = ?", database.StatusInvalid).Count(&metrics.APIKeys.Invalid).Error; err != nil {
		return err
	}
	
	// Testing API keys
	if err := s.db.Model(&database.UserAPIKey{}).Where("status = ?", database.StatusTesting).Count(&metrics.APIKeys.Testing).Error; err != nil {
		return err
	}
	
	return nil
}

func (s *Service) collectRequestMetrics(ctx context.Context, metrics *BusinessMetrics) error {
	// Total requests
	if err := s.db.Model(&database.UsageLog{}).Count(&metrics.Requests.Total).Error; err != nil {
		return err
	}
	
	// Today's requests
	today := time.Now().Truncate(24 * time.Hour)
	if err := s.db.Model(&database.UsageLog{}).Where("created_at >= ?", today).Count(&metrics.Requests.Today).Error; err != nil {
		return err
	}
	
	// Last hour requests
	lastHour := time.Now().Add(-time.Hour)
	if err := s.db.Model(&database.UsageLog{}).Where("created_at >= ?", lastHour).Count(&metrics.Requests.LastHour).Error; err != nil {
		return err
	}
	
	// Success rate
	var successCount int64
	if err := s.db.Model(&database.UsageLog{}).Where("status = ?", "success").Count(&successCount).Error; err != nil {
		return err
	}
	
	if metrics.Requests.Total > 0 {
		metrics.Requests.SuccessRate = float64(successCount) / float64(metrics.Requests.Total) * 100
		metrics.Requests.ErrorRate = 100 - metrics.Requests.SuccessRate
	}
	
	return nil
}

func (s *Service) collectProviderMetrics(ctx context.Context, metrics *BusinessMetrics) error {
	providers := []string{"openai", "gemini", "claude", "perplexity", "mistral", "deepseek", "moonshot", "ollama", "aws", "azure", "ali"}
	
	for _, provider := range providers {
		var requests int64
		if err := s.db.Model(&database.UsageLog{}).Where("provider = ?", provider).Count(&requests).Error; err != nil {
			continue
		}
		
		if requests == 0 {
			continue
		}
		
		var successCount int64
		s.db.Model(&database.UsageLog{}).Where("provider = ? AND status = ?", provider, "success").Count(&successCount)
		
		var avgDuration float64
		s.db.Model(&database.UsageLog{}).Where("provider = ?", provider).Select("AVG(duration)").Scan(&avgDuration)
		
		var totalTokens int64
		s.db.Model(&database.UsageLog{}).Where("provider = ?", provider).Select("SUM(total_tokens)").Scan(&totalTokens)
		
		var lastUsed time.Time
		s.db.Model(&database.UsageLog{}).Where("provider = ?", provider).Select("MAX(created_at)").Scan(&lastUsed)
		
		successRate := float64(successCount) / float64(requests) * 100
		errorRate := 100 - successRate
		
		metrics.Providers[provider] = ProviderMetrics{
			Requests:        requests,
			SuccessRate:     successRate,
			AvgResponseTime: avgDuration,
			ErrorRate:       errorRate,
			TotalTokens:     totalTokens,
			LastUsed:        lastUsed,
		}
	}
	
	return nil
}

// startSystemMonitoring starts background system monitoring
func (s *Service) startSystemMonitoring() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			ctx := context.Background()
			
			// Collect and store system metrics
			systemMetrics, err := s.GetSystemMetrics(ctx)
			if err != nil {
				logger.Error("Failed to collect system metrics", "error", err)
				continue
			}
			
			// Store metrics in Redis for real-time access
			metricsJSON, _ := json.Marshal(systemMetrics)
			s.redis.Set(ctx, "metrics:system:current", metricsJSON, 5*time.Minute)
			
			// Check alert conditions
			s.alerts.CheckAlerts(ctx, systemMetrics)
		}
	}
}
