package monitoring

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// AnalyticsEngine processes and analyzes metrics data
type AnalyticsEngine struct {
	db    *gorm.DB
	redis *redis.Client
}

// NewAnalyticsEngine creates a new analytics engine
func NewAnalyticsEngine(db *gorm.DB, redis *redis.Client) *AnalyticsEngine {
	return &AnalyticsEngine{
		db:    db,
		redis: redis,
	}
}

// AnalyticsData represents processed analytics data
type AnalyticsData struct {
	Period    string    `json:"period"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	
	Overview struct {
		TotalRequests   int64   `json:"total_requests"`
		SuccessfulReqs  int64   `json:"successful_requests"`
		FailedReqs      int64   `json:"failed_requests"`
		SuccessRate     float64 `json:"success_rate"`
		AvgResponseTime float64 `json:"avg_response_time_ms"`
		TotalTokens     int64   `json:"total_tokens"`
		UniqueUsers     int64   `json:"unique_users"`
	} `json:"overview"`
	
	Trends struct {
		RequestsOverTime    []TimeSeriesPoint `json:"requests_over_time"`
		ResponseTimeOverTime []TimeSeriesPoint `json:"response_time_over_time"`
		ErrorRateOverTime   []TimeSeriesPoint `json:"error_rate_over_time"`
		TokensOverTime      []TimeSeriesPoint `json:"tokens_over_time"`
	} `json:"trends"`
	
	Providers map[string]ProviderAnalytics `json:"providers"`
	
	TopUsers []UserAnalytics `json:"top_users"`
	
	Insights []Insight `json:"insights"`
}

// TimeSeriesPoint represents a point in time series data
type TimeSeriesPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
}

// ProviderAnalytics represents analytics for a specific provider
type ProviderAnalytics struct {
	Name            string  `json:"name"`
	Requests        int64   `json:"requests"`
	SuccessRate     float64 `json:"success_rate"`
	AvgResponseTime float64 `json:"avg_response_time_ms"`
	TotalTokens     int64   `json:"total_tokens"`
	ErrorTypes      map[string]int64 `json:"error_types"`
	Usage           []TimeSeriesPoint `json:"usage_over_time"`
}

// UserAnalytics represents analytics for a user
type UserAnalytics struct {
	UserID      uint    `json:"user_id"`
	Email       string  `json:"email"`
	Requests    int64   `json:"requests"`
	Tokens      int64   `json:"tokens"`
	SuccessRate float64 `json:"success_rate"`
	LastActive  time.Time `json:"last_active"`
}

// Insight represents an analytical insight
type Insight struct {
	Type        string                 `json:"type"`
	Severity    string                 `json:"severity"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Value       float64                `json:"value"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// GetAnalytics returns analytics data for a time period
func (ae *AnalyticsEngine) GetAnalytics(ctx context.Context, period string, startTime, endTime time.Time) (*AnalyticsData, error) {
	data := &AnalyticsData{
		Period:    period,
		StartTime: startTime,
		EndTime:   endTime,
		Providers: make(map[string]ProviderAnalytics),
	}
	
	// Get overview metrics
	if err := ae.getOverviewMetrics(ctx, data); err != nil {
		return nil, fmt.Errorf("failed to get overview metrics: %w", err)
	}
	
	// Get trend data
	if err := ae.getTrendData(ctx, data); err != nil {
		return nil, fmt.Errorf("failed to get trend data: %w", err)
	}
	
	// Get provider analytics
	if err := ae.getProviderAnalytics(ctx, data); err != nil {
		return nil, fmt.Errorf("failed to get provider analytics: %w", err)
	}
	
	// Get top users
	if err := ae.getTopUsers(ctx, data); err != nil {
		return nil, fmt.Errorf("failed to get top users: %w", err)
	}
	
	// Generate insights
	ae.generateInsights(data)
	
	return data, nil
}

func (ae *AnalyticsEngine) getOverviewMetrics(ctx context.Context, data *AnalyticsData) error {
	// Query usage logs for the time period
	query := ae.db.Table("usage_logs").Where("created_at BETWEEN ? AND ?", data.StartTime, data.EndTime)
	
	// Total requests
	if err := query.Count(&data.Overview.TotalRequests).Error; err != nil {
		return err
	}
	
	// Successful requests
	if err := query.Where("status = ?", "success").Count(&data.Overview.SuccessfulReqs).Error; err != nil {
		return err
	}
	
	data.Overview.FailedReqs = data.Overview.TotalRequests - data.Overview.SuccessfulReqs
	
	// Success rate
	if data.Overview.TotalRequests > 0 {
		data.Overview.SuccessRate = float64(data.Overview.SuccessfulReqs) / float64(data.Overview.TotalRequests) * 100
	}
	
	// Average response time
	var avgDuration sql.NullFloat64
	if err := query.Select("AVG(duration)").Scan(&avgDuration).Error; err != nil {
		return err
	}
	if avgDuration.Valid {
		data.Overview.AvgResponseTime = avgDuration.Float64
	}
	
	// Total tokens
	var totalTokens sql.NullInt64
	if err := query.Select("SUM(total_tokens)").Scan(&totalTokens).Error; err != nil {
		return err
	}
	if totalTokens.Valid {
		data.Overview.TotalTokens = totalTokens.Int64
	}
	
	// Unique users
	if err := query.Distinct("user_id").Count(&data.Overview.UniqueUsers).Error; err != nil {
		return err
	}
	
	return nil
}

func (ae *AnalyticsEngine) getTrendData(ctx context.Context, data *AnalyticsData) error {
	// Determine time bucket size based on period
	var bucketSize time.Duration
	var bucketFormat string
	
	switch data.Period {
	case "hour":
		bucketSize = 5 * time.Minute
		bucketFormat = "2006-01-02 15:04"
	case "day":
		bucketSize = time.Hour
		bucketFormat = "2006-01-02 15"
	case "week":
		bucketSize = 6 * time.Hour
		bucketFormat = "2006-01-02 15"
	case "month":
		bucketSize = 24 * time.Hour
		bucketFormat = "2006-01-02"
	default:
		bucketSize = time.Hour
		bucketFormat = "2006-01-02 15"
	}
	
	// Generate time buckets
	current := data.StartTime.Truncate(bucketSize)
	for current.Before(data.EndTime) {
		timestamp := current
		
		// Requests over time
		var requestCount int64
		ae.db.Table("usage_logs").
			Where("created_at BETWEEN ? AND ?", current, current.Add(bucketSize)).
			Count(&requestCount)
		
		data.Trends.RequestsOverTime = append(data.Trends.RequestsOverTime, TimeSeriesPoint{
			Timestamp: timestamp,
			Value:     float64(requestCount),
		})
		
		// Response time over time
		var avgResponseTime sql.NullFloat64
		ae.db.Table("usage_logs").
			Where("created_at BETWEEN ? AND ?", current, current.Add(bucketSize)).
			Select("AVG(duration)").Scan(&avgResponseTime)
		
		responseTime := 0.0
		if avgResponseTime.Valid {
			responseTime = avgResponseTime.Float64
		}
		
		data.Trends.ResponseTimeOverTime = append(data.Trends.ResponseTimeOverTime, TimeSeriesPoint{
			Timestamp: timestamp,
			Value:     responseTime,
		})
		
		// Error rate over time
		var totalReqs, errorReqs int64
		ae.db.Table("usage_logs").
			Where("created_at BETWEEN ? AND ?", current, current.Add(bucketSize)).
			Count(&totalReqs)
		ae.db.Table("usage_logs").
			Where("created_at BETWEEN ? AND ? AND status != ?", current, current.Add(bucketSize), "success").
			Count(&errorReqs)
		
		errorRate := 0.0
		if totalReqs > 0 {
			errorRate = float64(errorReqs) / float64(totalReqs) * 100
		}
		
		data.Trends.ErrorRateOverTime = append(data.Trends.ErrorRateOverTime, TimeSeriesPoint{
			Timestamp: timestamp,
			Value:     errorRate,
		})
		
		// Tokens over time
		var totalTokens sql.NullInt64
		ae.db.Table("usage_logs").
			Where("created_at BETWEEN ? AND ?", current, current.Add(bucketSize)).
			Select("SUM(total_tokens)").Scan(&totalTokens)
		
		tokens := int64(0)
		if totalTokens.Valid {
			tokens = totalTokens.Int64
		}
		
		data.Trends.TokensOverTime = append(data.Trends.TokensOverTime, TimeSeriesPoint{
			Timestamp: timestamp,
			Value:     float64(tokens),
		})
		
		current = current.Add(bucketSize)
	}
	
	return nil
}

func (ae *AnalyticsEngine) getProviderAnalytics(ctx context.Context, data *AnalyticsData) error {
	providers := []string{"openai", "gemini", "claude", "perplexity", "mistral", "deepseek", "moonshot", "ollama", "aws", "azure", "ali"}
	
	for _, provider := range providers {
		query := ae.db.Table("usage_logs").
			Where("created_at BETWEEN ? AND ? AND provider = ?", data.StartTime, data.EndTime, provider)
		
		var requests int64
		if err := query.Count(&requests).Error; err != nil {
			continue
		}
		
		if requests == 0 {
			continue
		}
		
		analytics := ProviderAnalytics{
			Name:       provider,
			Requests:   requests,
			ErrorTypes: make(map[string]int64),
		}
		
		// Success rate
		var successCount int64
		query.Where("status = ?", "success").Count(&successCount)
		if requests > 0 {
			analytics.SuccessRate = float64(successCount) / float64(requests) * 100
		}
		
		// Average response time
		var avgDuration sql.NullFloat64
		query.Select("AVG(duration)").Scan(&avgDuration)
		if avgDuration.Valid {
			analytics.AvgResponseTime = avgDuration.Float64
		}
		
		// Total tokens
		var totalTokens sql.NullInt64
		query.Select("SUM(total_tokens)").Scan(&totalTokens)
		if totalTokens.Valid {
			analytics.TotalTokens = totalTokens.Int64
		}
		
		// Error types
		var errorResults []struct {
			ErrorMessage string
			Count        int64
		}
		ae.db.Table("usage_logs").
			Select("error_message, COUNT(*) as count").
			Where("created_at BETWEEN ? AND ? AND provider = ? AND status != ?", 
				data.StartTime, data.EndTime, provider, "success").
			Group("error_message").
			Scan(&errorResults)
		
		for _, result := range errorResults {
			if result.ErrorMessage != "" {
				analytics.ErrorTypes[result.ErrorMessage] = result.Count
			}
		}
		
		data.Providers[provider] = analytics
	}
	
	return nil
}

func (ae *AnalyticsEngine) getTopUsers(ctx context.Context, data *AnalyticsData) error {
	var results []struct {
		UserID      uint
		Email       string
		Requests    int64
		Tokens      int64
		SuccessRate float64
		LastActive  time.Time
	}
	
	err := ae.db.Table("usage_logs ul").
		Select(`ul.user_id, u.email, 
			COUNT(*) as requests, 
			SUM(ul.total_tokens) as tokens,
			AVG(CASE WHEN ul.status = 'success' THEN 1.0 ELSE 0.0 END) * 100 as success_rate,
			MAX(ul.created_at) as last_active`).
		Joins("JOIN users u ON u.id = ul.user_id").
		Where("ul.created_at BETWEEN ? AND ?", data.StartTime, data.EndTime).
		Group("ul.user_id, u.email").
		Order("requests DESC").
		Limit(10).
		Scan(&results).Error
	
	if err != nil {
		return err
	}
	
	for _, result := range results {
		data.TopUsers = append(data.TopUsers, UserAnalytics{
			UserID:      result.UserID,
			Email:       result.Email,
			Requests:    result.Requests,
			Tokens:      result.Tokens,
			SuccessRate: result.SuccessRate,
			LastActive:  result.LastActive,
		})
	}
	
	return nil
}

func (ae *AnalyticsEngine) generateInsights(data *AnalyticsData) {
	// High error rate insight
	if data.Overview.SuccessRate < 95.0 {
		data.Insights = append(data.Insights, Insight{
			Type:        "error_rate",
			Severity:    "warning",
			Title:       "High Error Rate Detected",
			Description: fmt.Sprintf("Success rate is %.1f%%, below the recommended 95%% threshold", data.Overview.SuccessRate),
			Value:       100 - data.Overview.SuccessRate,
		})
	}
	
	// Slow response time insight
	if data.Overview.AvgResponseTime > 5000 {
		data.Insights = append(data.Insights, Insight{
			Type:        "performance",
			Severity:    "warning",
			Title:       "Slow Response Times",
			Description: fmt.Sprintf("Average response time is %.0fms, consider optimization", data.Overview.AvgResponseTime),
			Value:       data.Overview.AvgResponseTime,
		})
	}
	
	// Provider performance insights
	for name, provider := range data.Providers {
		if provider.SuccessRate < 90.0 {
			data.Insights = append(data.Insights, Insight{
				Type:        "provider",
				Severity:    "error",
				Title:       fmt.Sprintf("Poor %s Performance", name),
				Description: fmt.Sprintf("Provider %s has %.1f%% success rate", name, provider.SuccessRate),
				Value:       provider.SuccessRate,
				Metadata:    map[string]interface{}{"provider": name},
			})
		}
	}
	
	// Usage growth insight
	if len(data.Trends.RequestsOverTime) >= 2 {
		firstHalf := data.Trends.RequestsOverTime[:len(data.Trends.RequestsOverTime)/2]
		secondHalf := data.Trends.RequestsOverTime[len(data.Trends.RequestsOverTime)/2:]
		
		firstAvg := 0.0
		for _, point := range firstHalf {
			firstAvg += point.Value
		}
		firstAvg /= float64(len(firstHalf))
		
		secondAvg := 0.0
		for _, point := range secondHalf {
			secondAvg += point.Value
		}
		secondAvg /= float64(len(secondHalf))
		
		if secondAvg > firstAvg*1.5 {
			growth := (secondAvg - firstAvg) / firstAvg * 100
			data.Insights = append(data.Insights, Insight{
				Type:        "growth",
				Severity:    "info",
				Title:       "Significant Usage Growth",
				Description: fmt.Sprintf("Request volume increased by %.1f%% in the latter half of the period", growth),
				Value:       growth,
			})
		}
	}
}
