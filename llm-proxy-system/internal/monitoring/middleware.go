package monitoring

import (
	"strconv"
	"time"

	"llm-proxy-system/internal/logger"

	"github.com/gin-gonic/gin"
)

// RequestMonitoringMiddleware creates middleware for monitoring HTTP requests
func RequestMonitoringMiddleware(service *Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// Process request
		c.Next()

		// Calculate response time
		duration := time.Since(startTime)
		responseTime := float64(duration.Nanoseconds()) / 1e6 // Convert to milliseconds

		// Extract user ID if available
		var userID uint
		if userIDValue, exists := c.Get("user_id"); exists {
			if uid, ok := userIDValue.(uint); ok {
				userID = uid
			}
		}

		// Determine success status
		statusCode := c.Writer.Status()
		success := statusCode >= 200 && statusCode < 400

		// Extract provider and model from context if available
		provider := ""
		model := ""
		if providerValue, exists := c.Get("provider"); exists {
			if p, ok := providerValue.(string); ok {
				provider = p
			}
		}
		if modelValue, exists := c.Get("model"); exists {
			if m, ok := modelValue.(string); ok {
				model = m
			}
		}

		// Extract tokens used if available
		tokensUsed := 0
		if tokensValue, exists := c.Get("tokens_used"); exists {
			if tokens, ok := tokensValue.(int); ok {
				tokensUsed = tokens
			}
		}

		// Determine error type for failed requests
		errorType := ""
		if !success {
			if statusCode >= 400 && statusCode < 500 {
				errorType = "client_error"
			} else if statusCode >= 500 {
				errorType = "server_error"
			}
		}

		// Create request metric
		metric := &RequestMetric{
			Timestamp:    startTime,
			UserID:       userID,
			Provider:     provider,
			Model:        model,
			Method:       c.Request.Method + " " + c.FullPath(),
			StatusCode:   statusCode,
			ResponseTime: responseTime,
			TokensUsed:   tokensUsed,
			Success:      success,
			ErrorType:    errorType,
			IPAddress:    c.ClientIP(),
			UserAgent:    c.Request.UserAgent(),
		}

		// Record the metric asynchronously
		go func() {
			if err := service.RecordRequest(c.Request.Context(), metric); err != nil {
				logger.Error("Failed to record request metric", "error", err)
			}
		}()

		// Log request for debugging (optional)
		logger.Debug("Request processed",
			"method", c.Request.Method,
			"path", c.FullPath(),
			"status", statusCode,
			"duration_ms", responseTime,
			"user_id", userID,
			"ip", c.ClientIP(),
		)
	}
}

// LLMRequestMonitoringMiddleware creates middleware specifically for LLM requests
func LLMRequestMonitoringMiddleware(service *Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// Process request
		c.Next()

		// Only monitor LLM-related endpoints
		if !isLLMEndpoint(c.FullPath()) {
			return
		}

		duration := time.Since(startTime)
		responseTime := float64(duration.Nanoseconds()) / 1e6

		// Extract LLM-specific data
		var userID uint
		if userIDValue, exists := c.Get("user_id"); exists {
			if uid, ok := userIDValue.(uint); ok {
				userID = uid
			}
		}

		var apiKeyID uint
		if keyIDValue, exists := c.Get("api_key_id"); exists {
			if kid, ok := keyIDValue.(uint); ok {
				apiKeyID = kid
			}
		}

		provider := ""
		if providerValue, exists := c.Get("provider"); exists {
			if p, ok := providerValue.(string); ok {
				provider = p
			}
		}

		model := ""
		if modelValue, exists := c.Get("model"); exists {
			if m, ok := modelValue.(string); ok {
				model = m
			}
		}

		tokensUsed := 0
		if tokensValue, exists := c.Get("tokens_used"); exists {
			if tokens, ok := tokensValue.(int); ok {
				tokensUsed = tokens
			}
		}

		statusCode := c.Writer.Status()
		success := statusCode >= 200 && statusCode < 400

		// Record API key usage if available
		if apiKeyID > 0 {
			usage := &APIKeyUsage{
				Timestamp:  startTime,
				UserID:     userID,
				APIKeyID:   apiKeyID,
				Provider:   provider,
				Requests:   1,
				TokensUsed: tokensUsed,
				Success:    success,
				ErrorCount: 0,
			}

			if !success {
				usage.ErrorCount = 1
			}

			go func() {
				if err := service.RecordAPIKeyUsage(c.Request.Context(), usage); err != nil {
					logger.Error("Failed to record API key usage", "error", err)
				}
			}()
		}

		// Record system event for significant LLM operations
		if tokensUsed > 1000 || responseTime > 10000 || !success {
			severity := "info"
			if !success {
				severity = "error"
			} else if responseTime > 10000 {
				severity = "warning"
			}

			event := &SystemEvent{
				Timestamp: startTime,
				Type:      "llm_request",
				Severity:  severity,
				Message:   formatLLMEventMessage(provider, model, tokensUsed, responseTime, success),
				Component: "llm_proxy",
				Metadata: map[string]interface{}{
					"provider":      provider,
					"model":         model,
					"tokens_used":   tokensUsed,
					"response_time": responseTime,
					"success":       success,
					"user_id":       userID,
				},
			}

			go func() {
				if err := service.metrics.RecordSystemEvent(c.Request.Context(), event); err != nil {
					logger.Error("Failed to record system event", "error", err)
				}
			}()
		}
	}
}

// PerformanceMonitoringMiddleware creates middleware for performance monitoring
func PerformanceMonitoringMiddleware(service *Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// Process request
		c.Next()

		duration := time.Since(startTime)
		responseTime := float64(duration.Nanoseconds()) / 1e6

		// Record performance events for slow requests
		if responseTime > 5000 { // 5 seconds
			event := &SystemEvent{
				Timestamp: startTime,
				Type:      "performance",
				Severity:  "warning",
				Message:   "Slow request detected",
				Component: "http_server",
				Metadata: map[string]interface{}{
					"method":        c.Request.Method,
					"path":          c.FullPath(),
					"response_time": responseTime,
					"status_code":   c.Writer.Status(),
					"ip_address":    c.ClientIP(),
				},
			}

			go func() {
				if err := service.metrics.RecordSystemEvent(c.Request.Context(), event); err != nil {
					logger.Error("Failed to record performance event", "error", err)
				}
			}()
		}

		// Record error events
		statusCode := c.Writer.Status()
		if statusCode >= 500 {
			event := &SystemEvent{
				Timestamp: startTime,
				Type:      "error",
				Severity:  "error",
				Message:   "Server error occurred",
				Component: "http_server",
				Metadata: map[string]interface{}{
					"method":      c.Request.Method,
					"path":        c.FullPath(),
					"status_code": statusCode,
					"ip_address":  c.ClientIP(),
					"user_agent":  c.Request.UserAgent(),
				},
			}

			go func() {
				if err := service.metrics.RecordSystemEvent(c.Request.Context(), event); err != nil {
					logger.Error("Failed to record error event", "error", err)
				}
			}()
		}
	}
}

// Helper functions

func isLLMEndpoint(path string) bool {
	llmPaths := []string{
		"/api/v1/llm/proxy",
		"/api/v1/llm/validate",
		"/api/v1/chat/completions",
		"/api/v1/completions",
		"/api/v1/models",
	}

	for _, llmPath := range llmPaths {
		if path == llmPath {
			return true
		}
	}

	return false
}

func formatLLMEventMessage(provider, model string, tokens int, responseTime float64, success bool) string {
	status := "completed"
	if !success {
		status = "failed"
	}

	return "LLM request " + status + " - Provider: " + provider + 
		", Model: " + model + 
		", Tokens: " + strconv.Itoa(tokens) + 
		", Response Time: " + strconv.FormatFloat(responseTime, 'f', 2, 64) + "ms"
}

// MetricsCollectionMiddleware creates middleware for collecting custom metrics
func MetricsCollectionMiddleware(service *Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Set monitoring service in context for use by handlers
		c.Set("monitoring_service", service)

		// Process request
		c.Next()
	}
}

// GetMonitoringService extracts the monitoring service from Gin context
func GetMonitoringService(c *gin.Context) *Service {
	if service, exists := c.Get("monitoring_service"); exists {
		if monitoringService, ok := service.(*Service); ok {
			return monitoringService
		}
	}
	return nil
}
