package monitoring

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
)

// MetricsCollector handles real-time metrics collection
type MetricsCollector struct {
	redis *redis.Client
}

// NewMetricsCollector creates a new metrics collector
func NewMetricsCollector(redis *redis.Client) *MetricsCollector {
	return &MetricsCollector{
		redis: redis,
	}
}

// RequestMetric represents a single request metric
type RequestMetric struct {
	Timestamp    time.Time `json:"timestamp"`
	UserID       uint      `json:"user_id"`
	Provider     string    `json:"provider"`
	Model        string    `json:"model"`
	Method       string    `json:"method"`
	StatusCode   int       `json:"status_code"`
	ResponseTime float64   `json:"response_time_ms"`
	TokensUsed   int       `json:"tokens_used"`
	Success      bool      `json:"success"`
	ErrorType    string    `json:"error_type,omitempty"`
	IPAddress    string    `json:"ip_address"`
	UserAgent    string    `json:"user_agent"`
}

// APIKeyUsage represents API key usage metrics
type APIKeyUsage struct {
	Timestamp   time.Time `json:"timestamp"`
	UserID      uint      `json:"user_id"`
	APIKeyID    uint      `json:"api_key_id"`
	Provider    string    `json:"provider"`
	Requests    int       `json:"requests"`
	TokensUsed  int       `json:"tokens_used"`
	Success     bool      `json:"success"`
	ErrorCount  int       `json:"error_count"`
}

// SystemEvent represents a system-level event
type SystemEvent struct {
	Timestamp   time.Time              `json:"timestamp"`
	Type        string                 `json:"type"`
	Severity    string                 `json:"severity"`
	Message     string                 `json:"message"`
	Component   string                 `json:"component"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// RecordRequest records a request metric
func (mc *MetricsCollector) RecordRequest(ctx context.Context, metric *RequestMetric) error {
	// Store individual request
	key := fmt.Sprintf("metrics:requests:%s", metric.Timestamp.Format("2006-01-02:15"))
	data, _ := json.Marshal(metric)
	
	if err := mc.redis.LPush(ctx, key, data).Err(); err != nil {
		return err
	}
	
	// Set expiration for hourly buckets (keep for 7 days)
	mc.redis.Expire(ctx, key, 7*24*time.Hour)
	
	// Update real-time counters
	mc.updateRequestCounters(ctx, metric)
	
	// Update provider-specific metrics
	mc.updateProviderMetrics(ctx, metric)
	
	return nil
}

// RecordAPIKeyUsage records API key usage
func (mc *MetricsCollector) RecordAPIKeyUsage(ctx context.Context, usage *APIKeyUsage) error {
	key := fmt.Sprintf("metrics:api_keys:%d:%s", usage.APIKeyID, usage.Timestamp.Format("2006-01-02"))
	data, _ := json.Marshal(usage)
	
	if err := mc.redis.LPush(ctx, key, data).Err(); err != nil {
		return err
	}
	
	// Set expiration for daily buckets (keep for 30 days)
	mc.redis.Expire(ctx, key, 30*24*time.Hour)
	
	return nil
}

// RecordSystemEvent records a system event
func (mc *MetricsCollector) RecordSystemEvent(ctx context.Context, event *SystemEvent) error {
	key := "metrics:system_events"
	data, _ := json.Marshal(event)
	
	if err := mc.redis.LPush(ctx, key, data).Err(); err != nil {
		return err
	}
	
	// Keep only last 1000 events
	mc.redis.LTrim(ctx, key, 0, 999)
	
	return nil
}

// GetRequestMetrics returns request metrics for a time period
func (mc *MetricsCollector) GetRequestMetrics(ctx context.Context, startTime, endTime time.Time) ([]RequestMetric, error) {
	var metrics []RequestMetric
	
	// Generate hourly keys for the time range
	current := startTime.Truncate(time.Hour)
	for current.Before(endTime) {
		key := fmt.Sprintf("metrics:requests:%s", current.Format("2006-01-02:15"))
		
		results, err := mc.redis.LRange(ctx, key, 0, -1).Result()
		if err != nil {
			current = current.Add(time.Hour)
			continue
		}
		
		for _, result := range results {
			var metric RequestMetric
			if err := json.Unmarshal([]byte(result), &metric); err == nil {
				if metric.Timestamp.After(startTime) && metric.Timestamp.Before(endTime) {
					metrics = append(metrics, metric)
				}
			}
		}
		
		current = current.Add(time.Hour)
	}
	
	return metrics, nil
}

// GetRealTimeMetrics returns current real-time metrics
func (mc *MetricsCollector) GetRealTimeMetrics(ctx context.Context) (map[string]interface{}, error) {
	metrics := make(map[string]interface{})
	
	// Get request rate (requests per minute)
	requestsPerMin, _ := mc.redis.Get(ctx, "metrics:requests_per_minute").Int64()
	metrics["requests_per_minute"] = requestsPerMin
	
	// Get error rate
	errorRate, _ := mc.redis.Get(ctx, "metrics:error_rate").Float64()
	metrics["error_rate"] = errorRate
	
	// Get average response time
	avgResponseTime, _ := mc.redis.Get(ctx, "metrics:avg_response_time").Float64()
	metrics["avg_response_time_ms"] = avgResponseTime
	
	// Get active users
	activeUsers, _ := mc.redis.SCard(ctx, "metrics:active_users").Result()
	metrics["active_users"] = activeUsers
	
	// Get provider status
	providers := []string{"openai", "gemini", "claude", "perplexity", "mistral"}
	providerMetrics := make(map[string]interface{})
	
	for _, provider := range providers {
		successRate, _ := mc.redis.Get(ctx, fmt.Sprintf("metrics:provider:%s:success_rate", provider)).Float64()
		avgLatency, _ := mc.redis.Get(ctx, fmt.Sprintf("metrics:provider:%s:avg_latency", provider)).Float64()
		
		providerMetrics[provider] = map[string]interface{}{
			"success_rate":   successRate,
			"avg_latency_ms": avgLatency,
		}
	}
	metrics["providers"] = providerMetrics
	
	return metrics, nil
}

// GetSystemEvents returns recent system events
func (mc *MetricsCollector) GetSystemEvents(ctx context.Context, limit int) ([]SystemEvent, error) {
	results, err := mc.redis.LRange(ctx, "metrics:system_events", 0, int64(limit-1)).Result()
	if err != nil {
		return nil, err
	}
	
	var events []SystemEvent
	for _, result := range results {
		var event SystemEvent
		if err := json.Unmarshal([]byte(result), &event); err == nil {
			events = append(events, event)
		}
	}
	
	return events, nil
}

// Helper methods for updating real-time counters

func (mc *MetricsCollector) updateRequestCounters(ctx context.Context, metric *RequestMetric) {
	now := time.Now()
	minute := now.Truncate(time.Minute)
	
	// Update requests per minute counter
	minuteKey := fmt.Sprintf("metrics:rpm:%s", minute.Format("2006-01-02:15:04"))
	mc.redis.Incr(ctx, minuteKey)
	mc.redis.Expire(ctx, minuteKey, 5*time.Minute)
	
	// Calculate requests per minute (average over last 5 minutes)
	totalRequests := int64(0)
	for i := 0; i < 5; i++ {
		key := fmt.Sprintf("metrics:rpm:%s", minute.Add(-time.Duration(i)*time.Minute).Format("2006-01-02:15:04"))
		count, _ := mc.redis.Get(ctx, key).Int64()
		totalRequests += count
	}
	mc.redis.Set(ctx, "metrics:requests_per_minute", totalRequests/5, time.Minute)
	
	// Update error rate
	if metric.Success {
		mc.redis.Incr(ctx, "metrics:success_count")
	} else {
		mc.redis.Incr(ctx, "metrics:error_count")
	}
	
	// Calculate error rate (over last hour)
	successCount, _ := mc.redis.Get(ctx, "metrics:success_count").Int64()
	errorCount, _ := mc.redis.Get(ctx, "metrics:error_count").Int64()
	totalCount := successCount + errorCount
	
	if totalCount > 0 {
		errorRate := float64(errorCount) / float64(totalCount) * 100
		mc.redis.Set(ctx, "metrics:error_rate", errorRate, time.Hour)
	}
	
	// Update average response time (sliding window)
	responseTimeKey := "metrics:response_times"
	mc.redis.LPush(ctx, responseTimeKey, metric.ResponseTime)
	mc.redis.LTrim(ctx, responseTimeKey, 0, 999) // Keep last 1000 response times
	
	// Calculate average
	responseTimes, _ := mc.redis.LRange(ctx, responseTimeKey, 0, -1).Result()
	if len(responseTimes) > 0 {
		total := 0.0
		count := 0
		for _, rt := range responseTimes {
			if val, err := strconv.ParseFloat(rt, 64); err == nil {
				total += val
				count++
			}
		}
		if count > 0 {
			avgResponseTime := total / float64(count)
			mc.redis.Set(ctx, "metrics:avg_response_time", avgResponseTime, time.Hour)
		}
	}
	
	// Track active users
	if metric.UserID > 0 {
		mc.redis.SAdd(ctx, "metrics:active_users", metric.UserID)
		mc.redis.Expire(ctx, "metrics:active_users", 15*time.Minute)
	}
}

func (mc *MetricsCollector) updateProviderMetrics(ctx context.Context, metric *RequestMetric) {
	if metric.Provider == "" {
		return
	}
	
	// Update provider success rate
	successKey := fmt.Sprintf("metrics:provider:%s:success", metric.Provider)
	totalKey := fmt.Sprintf("metrics:provider:%s:total", metric.Provider)
	
	mc.redis.Incr(ctx, totalKey)
	if metric.Success {
		mc.redis.Incr(ctx, successKey)
	}
	
	// Set expiration
	mc.redis.Expire(ctx, successKey, time.Hour)
	mc.redis.Expire(ctx, totalKey, time.Hour)
	
	// Calculate success rate
	successCount, _ := mc.redis.Get(ctx, successKey).Int64()
	totalCount, _ := mc.redis.Get(ctx, totalKey).Int64()
	
	if totalCount > 0 {
		successRate := float64(successCount) / float64(totalCount) * 100
		rateKey := fmt.Sprintf("metrics:provider:%s:success_rate", metric.Provider)
		mc.redis.Set(ctx, rateKey, successRate, time.Hour)
	}
	
	// Update provider latency
	latencyKey := fmt.Sprintf("metrics:provider:%s:latencies", metric.Provider)
	mc.redis.LPush(ctx, latencyKey, metric.ResponseTime)
	mc.redis.LTrim(ctx, latencyKey, 0, 99) // Keep last 100 latencies
	mc.redis.Expire(ctx, latencyKey, time.Hour)
	
	// Calculate average latency
	latencies, _ := mc.redis.LRange(ctx, latencyKey, 0, -1).Result()
	if len(latencies) > 0 {
		total := 0.0
		count := 0
		for _, lat := range latencies {
			if val, err := strconv.ParseFloat(lat, 64); err == nil {
				total += val
				count++
			}
		}
		if count > 0 {
			avgLatency := total / float64(count)
			avgKey := fmt.Sprintf("metrics:provider:%s:avg_latency", metric.Provider)
			mc.redis.Set(ctx, avgKey, avgLatency, time.Hour)
		}
	}
}

// CleanupOldMetrics removes old metrics data
func (mc *MetricsCollector) CleanupOldMetrics(ctx context.Context) error {
	// This would be called periodically to clean up old data
	// Implementation would scan for expired keys and remove them
	
	// For now, we rely on Redis TTL for cleanup
	return nil
}
