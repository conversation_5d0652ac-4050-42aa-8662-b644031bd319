package handlers

import (
	"net/http"
	"strconv"
	"time"

	"llm-proxy-system/internal/logger"
	"llm-proxy-system/internal/utils"
	"llm-proxy-system/internal/webhooks"

	"github.com/gin-gonic/gin"
)

// WebhookHandler handles webhook-related endpoints
type WebhookHandler struct {
	webhookService *webhooks.Service
}

// NewWebhookHandler creates a new webhook handler
func NewWebhookHandler(webhookService *webhooks.Service) *WebhookHandler {
	return &WebhookHandler{
		webhookService: webhookService,
	}
}

// CreateWebhook creates a new webhook for the authenticated user
func (h *WebhookHandler) CreateWebhook(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}

	var req webhooks.CreateWebhookRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}

	// Additional validation
	if len(req.Name) < 3 || len(req.Name) > 100 {
		utils.ValidationErrorResponse(c, "Webhook name must be between 3 and 100 characters")
		return
	}

	if len(req.Events) == 0 {
		utils.ValidationErrorResponse(c, "At least one event must be specified")
		return
	}

	webhookResponse, err := h.webhookService.CreateWebhook(c.Request.Context(), userIDUint, &req)
	if err != nil {
		logger.Error("Failed to create webhook", "error", err, "user_id", userIDUint)
		
		if err.Error()[:11] == "unsupported" {
			utils.ValidationErrorResponse(c, "Unsupported event", err.Error())
			return
		}
		
		if err.Error() == "invalid URL format" {
			utils.ValidationErrorResponse(c, "Invalid URL format", err.Error())
			return
		}
		
		utils.InternalErrorResponse(c, "Failed to create webhook")
		return
	}

	logger.Info("Webhook created successfully", "user_id", userIDUint, "webhook_id", webhookResponse.ID)
	utils.CreatedResponse(c, webhookResponse)
}

// GetWebhooks returns all webhooks for the authenticated user
func (h *WebhookHandler) GetWebhooks(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}

	webhookResponses, err := h.webhookService.GetWebhooks(c.Request.Context(), userIDUint)
	if err != nil {
		logger.Error("Failed to get webhooks", "error", err, "user_id", userIDUint)
		utils.InternalErrorResponse(c, "Failed to retrieve webhooks")
		return
	}

	utils.SuccessResponse(c, gin.H{
		"webhooks": webhookResponses,
		"count":    len(webhookResponses),
	})
}

// GetWebhook returns a specific webhook for the authenticated user
func (h *WebhookHandler) GetWebhook(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}

	webhookIDStr := c.Param("id")
	webhookID, err := strconv.ParseUint(webhookIDStr, 10, 32)
	if err != nil {
		utils.ValidationErrorResponse(c, "Invalid webhook ID")
		return
	}

	webhookResponse, err := h.webhookService.GetWebhook(c.Request.Context(), userIDUint, uint(webhookID))
	if err != nil {
		if err.Error() == "webhook not found" {
			utils.NotFoundResponse(c, "Webhook not found")
			return
		}
		
		logger.Error("Failed to get webhook", "error", err, "user_id", userIDUint, "webhook_id", webhookID)
		utils.InternalErrorResponse(c, "Failed to retrieve webhook")
		return
	}

	utils.SuccessResponse(c, webhookResponse)
}

// UpdateWebhook updates an existing webhook
func (h *WebhookHandler) UpdateWebhook(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}

	webhookIDStr := c.Param("id")
	webhookID, err := strconv.ParseUint(webhookIDStr, 10, 32)
	if err != nil {
		utils.ValidationErrorResponse(c, "Invalid webhook ID")
		return
	}

	var req webhooks.UpdateWebhookRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}

	// Validation
	if req.Name != "" && (len(req.Name) < 3 || len(req.Name) > 100) {
		utils.ValidationErrorResponse(c, "Webhook name must be between 3 and 100 characters")
		return
	}

	webhookResponse, err := h.webhookService.UpdateWebhook(c.Request.Context(), userIDUint, uint(webhookID), &req)
	if err != nil {
		if err.Error() == "webhook not found" {
			utils.NotFoundResponse(c, "Webhook not found")
			return
		}
		
		if err.Error()[:11] == "unsupported" {
			utils.ValidationErrorResponse(c, "Unsupported event", err.Error())
			return
		}
		
		if err.Error() == "invalid URL format" {
			utils.ValidationErrorResponse(c, "Invalid URL format", err.Error())
			return
		}
		
		logger.Error("Failed to update webhook", "error", err, "user_id", userIDUint, "webhook_id", webhookID)
		utils.InternalErrorResponse(c, "Failed to update webhook")
		return
	}

	logger.Info("Webhook updated successfully", "user_id", userIDUint, "webhook_id", webhookID)
	utils.SuccessResponse(c, webhookResponse)
}

// DeleteWebhook deletes a webhook
func (h *WebhookHandler) DeleteWebhook(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}

	webhookIDStr := c.Param("id")
	webhookID, err := strconv.ParseUint(webhookIDStr, 10, 32)
	if err != nil {
		utils.ValidationErrorResponse(c, "Invalid webhook ID")
		return
	}

	err = h.webhookService.DeleteWebhook(c.Request.Context(), userIDUint, uint(webhookID))
	if err != nil {
		if err.Error() == "webhook not found" {
			utils.NotFoundResponse(c, "Webhook not found")
			return
		}
		
		logger.Error("Failed to delete webhook", "error", err, "user_id", userIDUint, "webhook_id", webhookID)
		utils.InternalErrorResponse(c, "Failed to delete webhook")
		return
	}

	logger.Info("Webhook deleted successfully", "user_id", userIDUint, "webhook_id", webhookID)
	utils.SuccessResponse(c, gin.H{"message": "Webhook deleted successfully"})
}

// TestWebhook tests a webhook by sending a test event
func (h *WebhookHandler) TestWebhook(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}

	webhookIDStr := c.Param("id")
	webhookID, err := strconv.ParseUint(webhookIDStr, 10, 32)
	if err != nil {
		utils.ValidationErrorResponse(c, "Invalid webhook ID")
		return
	}

	err = h.webhookService.TestWebhook(c.Request.Context(), userIDUint, uint(webhookID))
	if err != nil {
		if err.Error() == "webhook not found" {
			utils.NotFoundResponse(c, "Webhook not found")
			return
		}
		
		logger.Error("Failed to test webhook", "error", err, "user_id", userIDUint, "webhook_id", webhookID)
		utils.InternalErrorResponse(c, "Failed to test webhook")
		return
	}

	logger.Info("Webhook tested successfully", "user_id", userIDUint, "webhook_id", webhookID)
	utils.SuccessResponse(c, gin.H{
		"message": "Test webhook sent successfully",
		"webhook_id": webhookID,
	})
}

// GetSupportedEvents returns all supported webhook events
func (h *WebhookHandler) GetSupportedEvents(c *gin.Context) {
	events := h.webhookService.GetSupportedEvents()
	
	// Create detailed event information
	eventDetails := []gin.H{
		{"type": "user.registered", "description": "User account created"},
		{"type": "user.login", "description": "User logged in"},
		{"type": "api_key.created", "description": "API key created"},
		{"type": "api_key.deleted", "description": "API key deleted"},
		{"type": "api_key.invalid", "description": "API key validation failed"},
		{"type": "llm_request.success", "description": "LLM request completed successfully"},
		{"type": "llm_request.failure", "description": "LLM request failed"},
		{"type": "system.alert", "description": "System alert triggered"},
		{"type": "quota.exceeded", "description": "Usage quota exceeded"},
	}
	
	utils.SuccessResponse(c, gin.H{
		"events": eventDetails,
		"count":  len(events),
	})
}

// TriggerEvent manually triggers a webhook event (admin only)
func (h *WebhookHandler) TriggerEvent(c *gin.Context) {
	var req struct {
		Type   string                 `json:"type" binding:"required"`
		UserID uint                   `json:"user_id,omitempty"`
		Data   map[string]interface{} `json:"data,omitempty"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}
	
	// Validate event type
	supportedEvents := h.webhookService.GetSupportedEvents()
	eventSupported := false
	for _, event := range supportedEvents {
		if event == req.Type {
			eventSupported = true
			break
		}
	}
	
	if !eventSupported {
		utils.ValidationErrorResponse(c, "Unsupported event type", req.Type)
		return
	}
	
	// Create webhook event
	event := &webhooks.WebhookEvent{
		Type:      req.Type,
		Timestamp: time.Now().UTC(),
		UserID:    req.UserID,
		Data:      req.Data,
	}
	
	if event.Data == nil {
		event.Data = make(map[string]interface{})
	}
	event.Data["triggered_by"] = "admin"
	
	err := h.webhookService.TriggerEvent(c.Request.Context(), event)
	if err != nil {
		logger.Error("Failed to trigger webhook event", "error", err, "event_type", req.Type)
		utils.InternalErrorResponse(c, "Failed to trigger webhook event")
		return
	}
	
	logger.Info("Webhook event triggered manually", "event_type", req.Type, "user_id", req.UserID)
	utils.SuccessResponse(c, gin.H{
		"message": "Webhook event triggered successfully",
		"event_type": req.Type,
		"user_id": req.UserID,
	})
}


