package handlers

import (
	"net/http"
	"strconv"

	"llm-proxy-system/internal/keys"
	"llm-proxy-system/internal/logger"
	"llm-proxy-system/internal/utils"

	"github.com/gin-gonic/gin"
)

// KeyHandler handles API key management endpoints
type KeyHandler struct {
	keyService *keys.Service
}

// NewKeyHandler creates a new key handler
func NewKeyHandler(keyService *keys.Service) *KeyHandler {
	return &KeyHandler{
		keyService: keyService,
	}
}

// Create<PERSON>ey creates a new API key for the authenticated user
func (h *KeyHandler) CreateKey(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}

	var req keys.CreateKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}

	// Additional validation
	if len(req.Name) < 3 || len(req.Name) > 100 {
		utils.ValidationErrorResponse(c, "Key name must be between 3 and 100 characters")
		return
	}

	if len(req.APIKey) < 10 {
		utils.ValidationErrorResponse(c, "API key appears to be too short")
		return
	}

	keyResponse, err := h.keyService.CreateKey(c.Request.Context(), userIDUint, &req)
	if err != nil {
		logger.Error("Failed to create API key", "error", err, "user_id", userIDUint, "provider", req.Provider)
		
		if err.Error() == "unsupported provider: "+req.Provider {
			utils.ValidationErrorResponse(c, "Unsupported provider", err.Error())
			return
		}
		
		if err.Error() == "invalid API key format" {
			utils.ValidationErrorResponse(c, "Invalid API key format", err.Error())
			return
		}
		
		utils.InternalErrorResponse(c, "Failed to create API key")
		return
	}

	logger.Info("API key created successfully", "user_id", userIDUint, "key_id", keyResponse.ID, "provider", req.Provider)
	utils.CreatedResponse(c, keyResponse)
}

// GetKeys returns all API keys for the authenticated user
func (h *KeyHandler) GetKeys(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}

	// Check for provider filter
	provider := c.Query("provider")

	var keyResponses []keys.KeyResponse
	var err error

	if provider != "" {
		keyResponses, err = h.keyService.GetKeysByProvider(c.Request.Context(), userIDUint, provider)
	} else {
		keyResponses, err = h.keyService.GetKeys(c.Request.Context(), userIDUint)
	}

	if err != nil {
		logger.Error("Failed to get API keys", "error", err, "user_id", userIDUint)
		utils.InternalErrorResponse(c, "Failed to retrieve API keys")
		return
	}

	utils.SuccessResponse(c, gin.H{
		"keys":  keyResponses,
		"count": len(keyResponses),
	})
}

// GetKey returns a specific API key for the authenticated user
func (h *KeyHandler) GetKey(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}

	keyIDStr := c.Param("id")
	keyID, err := strconv.ParseUint(keyIDStr, 10, 32)
	if err != nil {
		utils.ValidationErrorResponse(c, "Invalid key ID")
		return
	}

	keyResponse, err := h.keyService.GetKey(c.Request.Context(), userIDUint, uint(keyID))
	if err != nil {
		if err.Error() == "API key not found" {
			utils.NotFoundResponse(c, "API key not found")
			return
		}
		
		logger.Error("Failed to get API key", "error", err, "user_id", userIDUint, "key_id", keyID)
		utils.InternalErrorResponse(c, "Failed to retrieve API key")
		return
	}

	utils.SuccessResponse(c, keyResponse)
}

// UpdateKey updates an existing API key
func (h *KeyHandler) UpdateKey(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}

	keyIDStr := c.Param("id")
	keyID, err := strconv.ParseUint(keyIDStr, 10, 32)
	if err != nil {
		utils.ValidationErrorResponse(c, "Invalid key ID")
		return
	}

	var req keys.UpdateKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}

	// Validation
	if req.Name != "" && (len(req.Name) < 3 || len(req.Name) > 100) {
		utils.ValidationErrorResponse(c, "Key name must be between 3 and 100 characters")
		return
	}

	if req.APIKey != "" && len(req.APIKey) < 10 {
		utils.ValidationErrorResponse(c, "API key appears to be too short")
		return
	}

	keyResponse, err := h.keyService.UpdateKey(c.Request.Context(), userIDUint, uint(keyID), &req)
	if err != nil {
		if err.Error() == "API key not found" {
			utils.NotFoundResponse(c, "API key not found")
			return
		}
		
		if err.Error() == "invalid API key format" {
			utils.ValidationErrorResponse(c, "Invalid API key format")
			return
		}
		
		logger.Error("Failed to update API key", "error", err, "user_id", userIDUint, "key_id", keyID)
		utils.InternalErrorResponse(c, "Failed to update API key")
		return
	}

	logger.Info("API key updated successfully", "user_id", userIDUint, "key_id", keyID)
	utils.SuccessResponse(c, keyResponse)
}

// DeleteKey deletes an API key
func (h *KeyHandler) DeleteKey(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}

	keyIDStr := c.Param("id")
	keyID, err := strconv.ParseUint(keyIDStr, 10, 32)
	if err != nil {
		utils.ValidationErrorResponse(c, "Invalid key ID")
		return
	}

	err = h.keyService.DeleteKey(c.Request.Context(), userIDUint, uint(keyID))
	if err != nil {
		if err.Error() == "API key not found" {
			utils.NotFoundResponse(c, "API key not found")
			return
		}
		
		logger.Error("Failed to delete API key", "error", err, "user_id", userIDUint, "key_id", keyID)
		utils.InternalErrorResponse(c, "Failed to delete API key")
		return
	}

	logger.Info("API key deleted successfully", "user_id", userIDUint, "key_id", keyID)
	utils.SuccessResponse(c, gin.H{"message": "API key deleted successfully"})
}

// TestKey tests an API key with its provider
func (h *KeyHandler) TestKey(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}

	keyIDStr := c.Param("id")
	keyID, err := strconv.ParseUint(keyIDStr, 10, 32)
	if err != nil {
		utils.ValidationErrorResponse(c, "Invalid key ID")
		return
	}

	keyResponse, err := h.keyService.TestKey(c.Request.Context(), userIDUint, uint(keyID))
	if err != nil {
		if err.Error() == "API key not found" {
			utils.NotFoundResponse(c, "API key not found")
			return
		}
		
		logger.Error("Failed to test API key", "error", err, "user_id", userIDUint, "key_id", keyID)
		utils.InternalErrorResponse(c, "Failed to test API key")
		return
	}

	logger.Info("API key tested", "user_id", userIDUint, "key_id", keyID, "status", keyResponse.Status)
	utils.SuccessResponse(c, keyResponse)
}

// GetKeyStats returns statistics about the user's API keys
func (h *KeyHandler) GetKeyStats(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}

	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}

	stats, err := h.keyService.GetKeyStats(c.Request.Context(), userIDUint)
	if err != nil {
		logger.Error("Failed to get key statistics", "error", err, "user_id", userIDUint)
		utils.InternalErrorResponse(c, "Failed to retrieve key statistics")
		return
	}

	utils.SuccessResponse(c, stats)
}

// ValidateKeyFormat validates an API key format without storing it
func (h *KeyHandler) ValidateKeyFormat(c *gin.Context) {
	var req struct {
		Provider string `json:"provider" binding:"required"`
		APIKey   string `json:"api_key" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}

	// Basic format validation
	if !utils.ValidateAPIKey(req.APIKey) {
		utils.ValidationErrorResponse(c, "Invalid API key format")
		return
	}

	// Test with provider (without storing)
	// Note: This would require exposing the adapter service or creating a validation method
	// For now, we'll just validate the format
	err := error(nil) // Placeholder - would call adapter service validation
	
	response := gin.H{
		"provider":    req.Provider,
		"valid_format": true,
		"provider_test": err == nil,
	}
	
	if err != nil {
		response["error"] = err.Error()
	}

	utils.SuccessResponse(c, response)
}
