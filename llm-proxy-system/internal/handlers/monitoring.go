package handlers

import (
	"net/http"
	"strconv"
	"time"

	"llm-proxy-system/internal/logger"
	"llm-proxy-system/internal/monitoring"
	"llm-proxy-system/internal/utils"

	"github.com/gin-gonic/gin"
)

// MonitoringHandler handles monitoring-related endpoints
type MonitoringHandler struct {
	monitoringService *monitoring.Service
}

// NewMonitoringHandler creates a new monitoring handler
func NewMonitoringHandler(monitoringService *monitoring.Service) *MonitoringHandler {
	return &MonitoringHandler{
		monitoringService: monitoringService,
	}
}

// GetSystemMetrics returns current system metrics
func (h *MonitoringHandler) GetSystemMetrics(c *gin.Context) {
	metrics, err := h.monitoringService.GetSystemMetrics(c.Request.Context())
	if err != nil {
		logger.Error("Failed to get system metrics", "error", err)
		utils.InternalErrorResponse(c, "Failed to retrieve system metrics")
		return
	}

	utils.SuccessResponse(c, metrics)
}

// GetBusinessMetrics returns business-level metrics
func (h *MonitoringHandler) GetBusinessMetrics(c *gin.Context) {
	metrics, err := h.monitoringService.GetBusinessMetrics(c.Request.Context())
	if err != nil {
		logger.Error("Failed to get business metrics", "error", err)
		utils.InternalErrorResponse(c, "Failed to retrieve business metrics")
		return
	}

	utils.SuccessResponse(c, metrics)
}

// GetAnalytics returns analytics data for a time period
func (h *MonitoringHandler) GetAnalytics(c *gin.Context) {
	// Parse query parameters
	period := c.DefaultQuery("period", "day")
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	// Default to last 24 hours if not specified
	endTime := time.Now()
	startTime := endTime.Add(-24 * time.Hour)

	if startTimeStr != "" {
		if parsed, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			startTime = parsed
		}
	}

	if endTimeStr != "" {
		if parsed, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			endTime = parsed
		}
	}

	// Validate period
	validPeriods := map[string]bool{
		"hour": true, "day": true, "week": true, "month": true,
	}
	if !validPeriods[period] {
		utils.ValidationErrorResponse(c, "Invalid period. Must be one of: hour, day, week, month")
		return
	}

	analytics, err := h.monitoringService.GetAnalytics(c.Request.Context(), period, startTime, endTime)
	if err != nil {
		logger.Error("Failed to get analytics", "error", err, "period", period)
		utils.InternalErrorResponse(c, "Failed to retrieve analytics data")
		return
	}

	utils.SuccessResponse(c, analytics)
}

// GetAlerts returns current alerts
func (h *MonitoringHandler) GetAlerts(c *gin.Context) {
	alerts, err := h.monitoringService.GetAlerts(c.Request.Context())
	if err != nil {
		logger.Error("Failed to get alerts", "error", err)
		utils.InternalErrorResponse(c, "Failed to retrieve alerts")
		return
	}

	utils.SuccessResponse(c, gin.H{
		"alerts": alerts,
		"count":  len(alerts),
	})
}

// CreateAlert creates a new alert rule
func (h *MonitoringHandler) CreateAlert(c *gin.Context) {
	var req monitoring.AlertRule
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}

	// Validate required fields
	if req.Name == "" {
		utils.ValidationErrorResponse(c, "Alert name is required")
		return
	}

	if req.Metric == "" {
		utils.ValidationErrorResponse(c, "Metric is required")
		return
	}

	if req.Condition == "" {
		utils.ValidationErrorResponse(c, "Condition is required")
		return
	}

	if req.Threshold == 0 {
		utils.ValidationErrorResponse(c, "Threshold is required")
		return
	}

	// Validate condition
	validConditions := map[string]bool{
		"gt": true, "lt": true, "eq": true, "gte": true, "lte": true,
	}
	if !validConditions[req.Condition] {
		utils.ValidationErrorResponse(c, "Invalid condition. Must be one of: gt, lt, eq, gte, lte")
		return
	}

	// Validate severity
	validSeverities := map[string]bool{
		"info": true, "warning": true, "error": true, "critical": true,
	}
	if req.Severity != "" && !validSeverities[req.Severity] {
		utils.ValidationErrorResponse(c, "Invalid severity. Must be one of: info, warning, error, critical")
		return
	}

	if req.Severity == "" {
		req.Severity = "warning"
	}

	// Set default duration if not provided
	if req.Duration == 0 {
		req.Duration = 5 * time.Minute
	}

	// Set default enabled state
	req.Enabled = true

	err := h.monitoringService.CreateAlert(c.Request.Context(), &req)
	if err != nil {
		logger.Error("Failed to create alert", "error", err)
		utils.InternalErrorResponse(c, "Failed to create alert rule")
		return
	}

	logger.Info("Alert rule created", "name", req.Name, "metric", req.Metric)
	utils.CreatedResponse(c, &req)
}

// GetRealTimeMetrics returns real-time metrics
func (h *MonitoringHandler) GetRealTimeMetrics(c *gin.Context) {
	// This would typically get real-time metrics from Redis
	// For now, we'll return current system metrics
	systemMetrics, err := h.monitoringService.GetSystemMetrics(c.Request.Context())
	if err != nil {
		logger.Error("Failed to get real-time metrics", "error", err)
		utils.InternalErrorResponse(c, "Failed to retrieve real-time metrics")
		return
	}

	businessMetrics, err := h.monitoringService.GetBusinessMetrics(c.Request.Context())
	if err != nil {
		logger.Error("Failed to get business metrics for real-time", "error", err)
		utils.InternalErrorResponse(c, "Failed to retrieve business metrics")
		return
	}

	realTimeData := gin.H{
		"timestamp": time.Now(),
		"system":    systemMetrics,
		"business":  businessMetrics,
		"status":    "healthy",
	}

	utils.SuccessResponse(c, realTimeData)
}

// GetProviderMetrics returns provider-specific metrics
func (h *MonitoringHandler) GetProviderMetrics(c *gin.Context) {
	provider := c.Param("provider")
	if provider == "" {
		utils.ValidationErrorResponse(c, "Provider parameter is required")
		return
	}

	// Parse time range
	endTime := time.Now()
	startTime := endTime.Add(-24 * time.Hour)

	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if parsed, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			startTime = parsed
		}
	}

	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if parsed, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			endTime = parsed
		}
	}

	// Get analytics data and extract provider metrics
	analytics, err := h.monitoringService.GetAnalytics(c.Request.Context(), "day", startTime, endTime)
	if err != nil {
		logger.Error("Failed to get provider metrics", "error", err, "provider", provider)
		utils.InternalErrorResponse(c, "Failed to retrieve provider metrics")
		return
	}

	providerMetrics, exists := analytics.Providers[provider]
	if !exists {
		utils.NotFoundResponse(c, "Provider metrics not found")
		return
	}

	utils.SuccessResponse(c, providerMetrics)
}

// GetHealthCheck returns system health status
func (h *MonitoringHandler) GetHealthCheck(c *gin.Context) {
	systemMetrics, err := h.monitoringService.GetSystemMetrics(c.Request.Context())
	if err != nil {
		logger.Error("Failed to get system metrics for health check", "error", err)
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status":    "unhealthy",
			"timestamp": time.Now(),
			"error":     "Failed to retrieve system metrics",
		})
		return
	}

	// Determine overall health status
	status := "healthy"
	issues := []string{}

	if systemMetrics.CPU.UsagePercent > 90 {
		status = "degraded"
		issues = append(issues, "High CPU usage")
	}

	if systemMetrics.Memory.UsagePercent > 90 {
		status = "degraded"
		issues = append(issues, "High memory usage")
	}

	if systemMetrics.Application.ErrorRate > 10 {
		status = "unhealthy"
		issues = append(issues, "High error rate")
	}

	if systemMetrics.Application.AvgResponseTime > 10000 {
		status = "degraded"
		issues = append(issues, "Slow response times")
	}

	healthData := gin.H{
		"status":     status,
		"timestamp":  time.Now(),
		"uptime":     systemMetrics.Application.Uptime.String(),
		"version":    "1.0.0",
		"checks": gin.H{
			"cpu_usage":     systemMetrics.CPU.UsagePercent,
			"memory_usage":  systemMetrics.Memory.UsagePercent,
			"error_rate":    systemMetrics.Application.ErrorRate,
			"response_time": systemMetrics.Application.AvgResponseTime,
		},
	}

	if len(issues) > 0 {
		healthData["issues"] = issues
	}

	statusCode := http.StatusOK
	if status == "unhealthy" {
		statusCode = http.StatusServiceUnavailable
	} else if status == "degraded" {
		statusCode = http.StatusPartialContent
	}

	c.JSON(statusCode, healthData)
}

// GetMetricsHistory returns historical metrics data
func (h *MonitoringHandler) GetMetricsHistory(c *gin.Context) {
	metric := c.Param("metric")
	if metric == "" {
		utils.ValidationErrorResponse(c, "Metric parameter is required")
		return
	}

	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "100")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 1000 {
		limit = 100
	}

	// Parse time range
	endTime := time.Now()
	startTime := endTime.Add(-24 * time.Hour)

	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if parsed, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			startTime = parsed
		}
	}

	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if parsed, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			endTime = parsed
		}
	}

	// Get analytics data
	analytics, err := h.monitoringService.GetAnalytics(c.Request.Context(), "hour", startTime, endTime)
	if err != nil {
		logger.Error("Failed to get metrics history", "error", err, "metric", metric)
		utils.InternalErrorResponse(c, "Failed to retrieve metrics history")
		return
	}

	// Extract the requested metric from trends
	var data interface{}
	switch metric {
	case "requests":
		data = analytics.Trends.RequestsOverTime
	case "response_time":
		data = analytics.Trends.ResponseTimeOverTime
	case "error_rate":
		data = analytics.Trends.ErrorRateOverTime
	case "tokens":
		data = analytics.Trends.TokensOverTime
	default:
		utils.ValidationErrorResponse(c, "Invalid metric. Must be one of: requests, response_time, error_rate, tokens")
		return
	}

	utils.SuccessResponse(c, gin.H{
		"metric":     metric,
		"start_time": startTime,
		"end_time":   endTime,
		"data":       data,
	})
}
