package handlers

import (
	"net/http"
	"strconv"

	"llm-proxy-system/internal/admin"
	"llm-proxy-system/internal/logger"
	"llm-proxy-system/internal/utils"

	"github.com/gin-gonic/gin"
)

// AdminHandler handles admin-related endpoints
type AdminHandler struct {
	adminService *admin.Service
}

// NewAdminHandler creates a new admin handler
func NewAdminHandler(adminService *admin.Service) *AdminHandler {
	return &AdminHandler{
		adminService: adminService,
	}
}

// GetSystemStats returns comprehensive system statistics
func (h *AdminHandler) GetSystemStats(c *gin.Context) {
	stats, err := h.adminService.GetSystemStats(c.Request.Context())
	if err != nil {
		logger.Error("Failed to get system stats", "error", err)
		utils.InternalErrorResponse(c, "Failed to retrieve system statistics")
		return
	}

	logger.Info("System statistics retrieved successfully")
	utils.SuccessResponse(c, stats)
}

// GetUsers returns all users with pagination
func (h *AdminHandler) GetUsers(c *gin.Context) {
	// Parse pagination parameters
	page := 1
	limit := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	users, total, err := h.adminService.GetUsers(c.Request.Context(), page, limit)
	if err != nil {
		logger.Error("Failed to get users", "error", err)
		utils.InternalErrorResponse(c, "Failed to retrieve users")
		return
	}

	pagination := utils.CalculatePagination(page, limit, int(total))

	logger.Info("Users retrieved successfully", "count", len(users), "total", total)
	utils.PaginatedSuccessResponse(c, users, pagination)
}

// GetUser returns a specific user with detailed information
func (h *AdminHandler) GetUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.ValidationErrorResponse(c, "Invalid user ID")
		return
	}

	user, err := h.adminService.GetUser(c.Request.Context(), uint(userID))
	if err != nil {
		if err.Error() == "record not found" {
			utils.NotFoundResponse(c, "User not found")
			return
		}
		
		logger.Error("Failed to get user", "error", err, "user_id", userID)
		utils.InternalErrorResponse(c, "Failed to retrieve user")
		return
	}

	utils.SuccessResponse(c, user)
}

// UpdateUser updates a user's information
func (h *AdminHandler) UpdateUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.ValidationErrorResponse(c, "Invalid user ID")
		return
	}

	var req admin.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}

	// Validate role if provided
	if req.Role != "" && req.Role != "user" && req.Role != "admin" {
		utils.ValidationErrorResponse(c, "Invalid role. Must be 'user' or 'admin'")
		return
	}

	user, err := h.adminService.UpdateUser(c.Request.Context(), uint(userID), &req)
	if err != nil {
		if err.Error() == "record not found" {
			utils.NotFoundResponse(c, "User not found")
			return
		}
		
		logger.Error("Failed to update user", "error", err, "user_id", userID)
		utils.InternalErrorResponse(c, "Failed to update user")
		return
	}

	logger.Info("User updated successfully", "user_id", userID)
	utils.SuccessResponse(c, user)
}

// DeleteUser deletes a user (soft delete)
func (h *AdminHandler) DeleteUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.ValidationErrorResponse(c, "Invalid user ID")
		return
	}

	err = h.adminService.DeleteUser(c.Request.Context(), uint(userID))
	if err != nil {
		if err.Error() == "record not found" {
			utils.NotFoundResponse(c, "User not found")
			return
		}
		
		logger.Error("Failed to delete user", "error", err, "user_id", userID)
		utils.InternalErrorResponse(c, "Failed to delete user")
		return
	}

	logger.Info("User deleted successfully", "user_id", userID)
	utils.SuccessResponse(c, gin.H{"message": "User deleted successfully"})
}

// GetUserAPIKeys returns all API keys for a specific user
func (h *AdminHandler) GetUserAPIKeys(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.ValidationErrorResponse(c, "Invalid user ID")
		return
	}

	keys, err := h.adminService.GetUserAPIKeys(c.Request.Context(), uint(userID))
	if err != nil {
		logger.Error("Failed to get user API keys", "error", err, "user_id", userID)
		utils.InternalErrorResponse(c, "Failed to retrieve user API keys")
		return
	}

	// Mask the API keys for security
	maskedKeys := make([]gin.H, len(keys))
	for i, key := range keys {
		maskedKeys[i] = gin.H{
			"id":               key.ID,
			"provider":         key.Provider,
			"name":             key.Name,
			"status":           key.Status,
			"last_tested_at":   key.LastTestedAt,
			"last_used_at":     key.LastUsedAt,
			"error_message":    key.ErrorMessage,
			"request_count":    key.RequestCount,
			"success_count":    key.SuccessCount,
			"error_count":      key.ErrorCount,
			"total_tokens_used": key.TotalTokensUsed,
			"created_at":       key.CreatedAt,
			"updated_at":       key.UpdatedAt,
			"masked_api_key":   utils.MaskAPIKey(key.Provider + "-****"),
		}
	}

	utils.SuccessResponse(c, gin.H{
		"keys":  maskedKeys,
		"count": len(maskedKeys),
	})
}

// GetSystemHealth returns system health information
func (h *AdminHandler) GetSystemHealth(c *gin.Context) {
	health, err := h.adminService.GetSystemHealth(c.Request.Context())
	if err != nil {
		logger.Error("Failed to get system health", "error", err)
		utils.InternalErrorResponse(c, "Failed to retrieve system health")
		return
	}

	// Set appropriate HTTP status based on health
	status := http.StatusOK
	if health["status"] == "unhealthy" {
		status = http.StatusServiceUnavailable
	}

	c.JSON(status, health)
}

// GetSystemLogs returns recent system logs (mock implementation)
func (h *AdminHandler) GetSystemLogs(c *gin.Context) {
	// Parse parameters
	level := c.DefaultQuery("level", "all")
	limit := 100

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 1000 {
			limit = l
		}
	}

	// Mock log entries
	logs := []gin.H{
		{
			"timestamp": "2024-01-01T12:00:00Z",
			"level":     "info",
			"message":   "User authentication successful",
			"user_id":   123,
			"ip":        "*************",
		},
		{
			"timestamp": "2024-01-01T11:59:30Z",
			"level":     "warn",
			"message":   "API key validation failed",
			"provider":  "openai",
			"user_id":   456,
		},
		{
			"timestamp": "2024-01-01T11:58:15Z",
			"level":     "error",
			"message":   "Database connection timeout",
			"duration":  "5.2s",
		},
		{
			"timestamp": "2024-01-01T11:57:00Z",
			"level":     "info",
			"message":   "LLM request processed successfully",
			"provider":  "gemini",
			"model":     "gemini-1.5-pro",
			"tokens":    150,
		},
	}

	// Filter by level if specified
	if level != "all" {
		filteredLogs := []gin.H{}
		for _, log := range logs {
			if log["level"] == level {
				filteredLogs = append(filteredLogs, log)
			}
		}
		logs = filteredLogs
	}

	// Apply limit
	if len(logs) > limit {
		logs = logs[:limit]
	}

	utils.SuccessResponse(c, gin.H{
		"logs":  logs,
		"count": len(logs),
		"level": level,
		"limit": limit,
	})
}

// GetSystemMetrics returns system performance metrics (mock implementation)
func (h *AdminHandler) GetSystemMetrics(c *gin.Context) {
	// Parse time range
	hours := 24
	if hoursStr := c.Query("hours"); hoursStr != "" {
		if h, err := strconv.Atoi(hoursStr); err == nil && h > 0 && h <= 168 {
			hours = h
		}
	}

	// Mock metrics data
	metrics := gin.H{
		"time_range_hours": hours,
		"cpu": gin.H{
			"usage_percent": 45.2,
			"cores":         4,
		},
		"memory": gin.H{
			"used_mb":      512,
			"total_mb":     2048,
			"usage_percent": 25.0,
		},
		"disk": gin.H{
			"used_gb":      15.5,
			"total_gb":     100.0,
			"usage_percent": 15.5,
		},
		"network": gin.H{
			"requests_per_minute": 150,
			"bandwidth_mbps":      10.5,
		},
		"database": gin.H{
			"connections":     25,
			"max_connections": 100,
			"query_time_ms":   12.5,
		},
		"cache": gin.H{
			"hit_rate_percent": 95.5,
			"memory_mb":        128,
		},
	}

	utils.SuccessResponse(c, metrics)
}
