package handlers

import (
	"bufio"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"llm-proxy-system/internal/logger"
	"llm-proxy-system/internal/proxy"
	"llm-proxy-system/internal/utils"

	"github.com/gin-gonic/gin"
)

// Proxy<PERSON><PERSON><PERSON> handles LLM proxy requests
type Proxy<PERSON><PERSON><PERSON> struct {
	proxyService *proxy.Service
}

// NewProxyHandler creates a new proxy handler
func NewProxyHandler(proxyService *proxy.Service) *ProxyHandler {
	return &ProxyHandler{
		proxyService: proxyService,
	}
}

// ProxyLLMRequest handles the main LLM proxy endpoint
func (h *ProxyHandler) ProxyLLMRequest(c *gin.Context) {
	// Get user context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}
	
	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}
	
	// Get request metadata
	requestID := utils.GetRequestID(c)
	ipAddress := c.ClientIP()
	userAgent := c.<PERSON>eader("User-Agent")
	
	// Parse request body
	var req proxy.ProxyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request format", err.Error())
		return
	}
	
	// Validate required fields
	if req.Model == "" {
		utils.ValidationErrorResponse(c, "Model is required")
		return
	}
	
	// Check if streaming is requested
	if req.Stream {
		h.handleStreamingRequest(c, userIDUint, &req, requestID, ipAddress, userAgent)
		return
	}
	
	// Process non-streaming request
	response, err := h.proxyService.ProcessRequest(c.Request.Context(), userIDUint, &req, requestID, ipAddress, userAgent)
	if err != nil {
		logger.Error("LLM request failed", "error", err, "user_id", userIDUint, "model", req.Model, "request_id", requestID)
		
		// Determine error type and status code
		statusCode := http.StatusInternalServerError
		errorCode := "INTERNAL_ERROR"
		
		if strings.Contains(err.Error(), "routing failed") {
			statusCode = http.StatusBadRequest
			errorCode = "ROUTING_ERROR"
		} else if strings.Contains(err.Error(), "no active API key") {
			statusCode = http.StatusBadRequest
			errorCode = "NO_API_KEY"
		} else if strings.Contains(err.Error(), "provider") && strings.Contains(err.Error(), "not healthy") {
			statusCode = http.StatusServiceUnavailable
			errorCode = "PROVIDER_UNAVAILABLE"
		}
		
		utils.ErrorResponse(c, statusCode, errorCode, "LLM request failed", err.Error())
		return
	}
	
	logger.Info("LLM request completed", "user_id", userIDUint, "model", req.Model, "provider", response.Provider, "tokens", response.TokensUsed, "duration_ms", response.Duration.Milliseconds())
	
	// Return OpenAI-compatible response
	c.JSON(http.StatusOK, response.OpenAIResponse)
}

// handleStreamingRequest handles streaming LLM requests
func (h *ProxyHandler) handleStreamingRequest(c *gin.Context, userID uint, req *proxy.ProxyRequest, requestID, ipAddress, userAgent string) {
	// Set up Server-Sent Events headers
	utils.StreamingResponse(c)
	
	// Start streaming
	responseChan, errorChan, err := h.proxyService.ProcessStreamRequest(c.Request.Context(), userID, req, requestID, ipAddress, userAgent)
	if err != nil {
		logger.Error("Failed to start streaming request", "error", err, "user_id", userID, "model", req.Model)
		utils.WriteSSEEvent(c, "error", fmt.Sprintf(`{"error": {"message": "%s", "type": "stream_error"}}`, err.Error()))
		return
	}
	
	logger.Info("Started streaming request", "user_id", userID, "model", req.Model, "request_id", requestID)
	
	// Stream responses
	for {
		select {
		case response, ok := <-responseChan:
			if !ok {
				// Channel closed, streaming complete
				utils.WriteSSEEvent(c, "", "[DONE]")
				return
			}
			
			// Convert response to JSON and send as SSE
			responseJSON, err := json.Marshal(response.OpenAIResponse)
			if err != nil {
				logger.Error("Failed to marshal streaming response", "error", err)
				utils.WriteSSEEvent(c, "error", fmt.Sprintf(`{"error": {"message": "Failed to marshal response", "type": "marshal_error"}}`))
				return
			}
			
			utils.WriteSSEEvent(c, "", string(responseJSON))
			
		case err, ok := <-errorChan:
			if !ok {
				// Error channel closed
				return
			}
			
			logger.Error("Streaming request error", "error", err, "user_id", userID, "model", req.Model)
			utils.WriteSSEEvent(c, "error", fmt.Sprintf(`{"error": {"message": "%s", "type": "stream_error"}}`, err.Error()))
			return
			
		case <-c.Request.Context().Done():
			// Client disconnected
			logger.Info("Client disconnected from stream", "user_id", userID, "request_id", requestID)
			return
		}
	}
}

// GetSupportedModels returns all supported models across all providers
func (h *ProxyHandler) GetSupportedModels(c *gin.Context) {
	// This would typically aggregate models from all providers
	// For now, return a mock response
	
	models := []gin.H{
		{
			"id":       "gpt-4",
			"object":   "model",
			"provider": "openai",
			"capabilities": []string{"chat", "completion", "vision"},
		},
		{
			"id":       "gpt-3.5-turbo",
			"object":   "model",
			"provider": "openai",
			"capabilities": []string{"chat", "completion"},
		},
		{
			"id":       "gemini-1.5-pro",
			"object":   "model",
			"provider": "gemini",
			"capabilities": []string{"chat", "completion", "vision"},
		},
		{
			"id":       "claude-3-5-sonnet-20241022",
			"object":   "model",
			"provider": "claude",
			"capabilities": []string{"chat", "completion", "vision"},
		},
		{
			"id":       "mistral-large-latest",
			"object":   "model",
			"provider": "mistral",
			"capabilities": []string{"chat", "completion", "function_calling"},
		},
	}
	
	response := gin.H{
		"object": "list",
		"data":   models,
	}
	
	logger.Info("Retrieved supported models", "count", len(models))
	c.JSON(http.StatusOK, response)
}

// ValidateRequest validates an LLM request without executing it
func (h *ProxyHandler) ValidateRequest(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}
	
	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}
	
	var req proxy.ProxyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request format", err.Error())
		return
	}
	
	// Validate required fields
	validationErrors := []string{}
	
	if req.Model == "" {
		validationErrors = append(validationErrors, "model is required")
	}
	
	if len(req.Messages) == 0 && req.Prompt == nil {
		validationErrors = append(validationErrors, "either messages or prompt is required")
	}
	
	if req.MaxTokens != nil && *req.MaxTokens <= 0 {
		validationErrors = append(validationErrors, "max_tokens must be positive")
	}
	
	if req.Temperature != nil && (*req.Temperature < 0 || *req.Temperature > 2) {
		validationErrors = append(validationErrors, "temperature must be between 0 and 2")
	}
	
	if req.TopP != nil && (*req.TopP < 0 || *req.TopP > 1) {
		validationErrors = append(validationErrors, "top_p must be between 0 and 1")
	}
	
	if len(validationErrors) > 0 {
		utils.ValidationErrorResponse(c, "Request validation failed", strings.Join(validationErrors, "; "))
		return
	}
	
	logger.Info("Request validation successful", "user_id", userIDUint, "model", req.Model)
	utils.SuccessResponse(c, gin.H{
		"valid":   true,
		"message": "Request is valid",
		"model":   req.Model,
	})
}

// GetRequestHistory returns the user's request history
func (h *ProxyHandler) GetRequestHistory(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}
	
	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}
	
	// Parse pagination parameters
	page := 1
	limit := 20
	
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := fmt.Sscanf(pageStr, "%d", &page); err != nil || p != 1 || page < 1 {
			page = 1
		}
	}
	
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := fmt.Sscanf(limitStr, "%d", &limit); err != nil || l != 1 || limit < 1 || limit > 100 {
			limit = 20
		}
	}
	
	offset := (page - 1) * limit
	
	// For now, return mock data
	// In a full implementation, this would query the usage logs
	history := []gin.H{
		{
			"id":         "req_123",
			"model":      "gpt-4",
			"provider":   "openai",
			"status":     "success",
			"tokens":     150,
			"duration":   1200,
			"created_at": "2024-01-01T12:00:00Z",
		},
		{
			"id":         "req_124",
			"model":      "gemini-1.5-pro",
			"provider":   "gemini",
			"status":     "success",
			"tokens":     200,
			"duration":   800,
			"created_at": "2024-01-01T11:30:00Z",
		},
	}
	
	pagination := utils.CalculatePagination(page, limit, len(history))
	
	logger.Info("Retrieved request history", "user_id", userIDUint, "page", page, "limit", limit)
	utils.PaginatedSuccessResponse(c, history, pagination)
}
