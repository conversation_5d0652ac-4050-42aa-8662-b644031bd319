package handlers

import (
	"net/http"
	"strconv"

	"llm-proxy-system/internal/database"
	"llm-proxy-system/internal/logger"
	"llm-proxy-system/internal/routing"
	"llm-proxy-system/internal/utils"

	"github.com/gin-gonic/gin"
)

// RoutingHandler handles routing-related endpoints
type RoutingHandler struct {
	routingService *routing.Service
	ruleRepo       *database.AdminGlobalRoutingRuleRepository
}

// NewRoutingHandler creates a new routing handler
func NewRoutingHandler(routingService *routing.Service, ruleRepo *database.AdminGlobalRoutingRuleRepository) *RoutingHandler {
	return &RoutingHandler{
		routingService: routingService,
		ruleRepo:       ruleRepo,
	}
}

// TestRouting tests the routing logic for a given request
func (h *RoutingHandler) TestRouting(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}
	
	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}
	
	var req struct {
		Model        string   `json:"model" binding:"required"`
		Provider     string   `json:"provider,omitempty"`
		RequiredCaps []string `json:"required_caps,omitempty"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}
	
	routingReq := &routing.RoutingRequest{
		UserID:       userIDUint,
		Model:        req.Model,
		Provider:     req.Provider,
		RequiredCaps: req.RequiredCaps,
	}
	
	result, err := h.routingService.RouteRequest(c.Request.Context(), routingReq)
	if err != nil {
		logger.Error("Routing test failed", "error", err, "user_id", userIDUint, "model", req.Model)
		utils.ValidationErrorResponse(c, "Routing failed", err.Error())
		return
	}
	
	logger.Info("Routing test successful", "user_id", userIDUint, "model", req.Model, "provider", result.Provider)
	
	// Remove adapter from response (not serializable)
	response := map[string]interface{}{
		"provider":      result.Provider,
		"user_api_key_id": result.UserAPIKeyID,
		"priority":      result.Priority,
		"reason":        result.Reason,
		"fallbacks":     result.Fallbacks,
		"duration_ms":   result.Duration.Milliseconds(),
	}
	
	utils.SuccessResponse(c, response)
}

// GetRoutingRules returns all routing rules
func (h *RoutingHandler) GetRoutingRules(c *gin.Context) {
	rules, err := h.ruleRepo.GetAll()
	if err != nil {
		logger.Error("Failed to get routing rules", "error", err)
		utils.InternalErrorResponse(c, "Failed to retrieve routing rules")
		return
	}
	
	logger.Info("Retrieved routing rules", "count", len(rules))
	utils.SuccessResponse(c, gin.H{
		"rules": rules,
		"count": len(rules),
	})
}

// CreateRoutingRule creates a new routing rule
func (h *RoutingHandler) CreateRoutingRule(c *gin.Context) {
	var req struct {
		Provider    string `json:"provider" binding:"required"`
		Priority    int    `json:"priority" binding:"required"`
		IsEnabled   bool   `json:"is_enabled"`
		ModelFilter string `json:"model_filter,omitempty"`
		Description string `json:"description,omitempty"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}
	
	// Validate provider exists
	// This would typically check against supported providers
	
	rule := &database.AdminGlobalRoutingRule{
		Provider:    req.Provider,
		Priority:    req.Priority,
		IsEnabled:   req.IsEnabled,
		ModelFilter: req.ModelFilter,
		Description: req.Description,
	}
	
	if err := h.ruleRepo.Create(rule); err != nil {
		logger.Error("Failed to create routing rule", "error", err, "provider", req.Provider)
		utils.InternalErrorResponse(c, "Failed to create routing rule")
		return
	}
	
	logger.Info("Created routing rule", "id", rule.ID, "provider", rule.Provider, "priority", rule.Priority)
	utils.CreatedResponse(c, rule)
}

// UpdateRoutingRule updates an existing routing rule
func (h *RoutingHandler) UpdateRoutingRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.ValidationErrorResponse(c, "Invalid rule ID")
		return
	}
	
	rule, err := h.ruleRepo.GetByID(uint(id))
	if err != nil {
		utils.NotFoundResponse(c, "Routing rule not found")
		return
	}
	
	var req struct {
		Provider    string `json:"provider,omitempty"`
		Priority    *int   `json:"priority,omitempty"`
		IsEnabled   *bool  `json:"is_enabled,omitempty"`
		ModelFilter string `json:"model_filter,omitempty"`
		Description string `json:"description,omitempty"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}
	
	// Update fields if provided
	if req.Provider != "" {
		rule.Provider = req.Provider
	}
	if req.Priority != nil {
		rule.Priority = *req.Priority
	}
	if req.IsEnabled != nil {
		rule.IsEnabled = *req.IsEnabled
	}
	if req.ModelFilter != "" {
		rule.ModelFilter = req.ModelFilter
	}
	if req.Description != "" {
		rule.Description = req.Description
	}
	
	if err := h.ruleRepo.Update(rule); err != nil {
		logger.Error("Failed to update routing rule", "error", err, "id", rule.ID)
		utils.InternalErrorResponse(c, "Failed to update routing rule")
		return
	}
	
	logger.Info("Updated routing rule", "id", rule.ID, "provider", rule.Provider)
	utils.SuccessResponse(c, rule)
}

// DeleteRoutingRule deletes a routing rule
func (h *RoutingHandler) DeleteRoutingRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.ValidationErrorResponse(c, "Invalid rule ID")
		return
	}
	
	if err := h.ruleRepo.Delete(uint(id)); err != nil {
		logger.Error("Failed to delete routing rule", "error", err, "id", id)
		utils.InternalErrorResponse(c, "Failed to delete routing rule")
		return
	}
	
	logger.Info("Deleted routing rule", "id", id)
	utils.SuccessResponse(c, gin.H{"message": "Routing rule deleted successfully"})
}

// ReorderRoutingRules reorders routing rules by priority
func (h *RoutingHandler) ReorderRoutingRules(c *gin.Context) {
	var req struct {
		Rules []struct {
			ID       uint `json:"id" binding:"required"`
			Priority int  `json:"priority" binding:"required"`
		} `json:"rules" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}
	
	// Prepare updates
	updates := make([]struct {
		ID       uint
		Priority int
	}, len(req.Rules))
	
	for i, rule := range req.Rules {
		updates[i] = struct {
			ID       uint
			Priority int
		}{
			ID:       rule.ID,
			Priority: rule.Priority,
		}
	}
	
	if err := h.ruleRepo.UpdatePriorities(updates); err != nil {
		logger.Error("Failed to reorder routing rules", "error", err)
		utils.InternalErrorResponse(c, "Failed to reorder routing rules")
		return
	}
	
	logger.Info("Reordered routing rules", "count", len(updates))
	utils.SuccessResponse(c, gin.H{
		"message": "Routing rules reordered successfully",
		"updated": len(updates),
	})
}

// GetProviderHealth returns the health status of providers
func (h *RoutingHandler) GetProviderHealth(c *gin.Context) {
	provider := c.Query("provider")
	
	if provider != "" {
		// Get health for specific provider
		healthy, err := h.routingService.GetProviderHealth(c.Request.Context(), provider)
		if err != nil {
			logger.Error("Failed to get provider health", "error", err, "provider", provider)
			utils.InternalErrorResponse(c, "Failed to get provider health")
			return
		}
		
		utils.SuccessResponse(c, gin.H{
			"provider": provider,
			"healthy":  healthy,
			"status":   map[bool]string{true: "healthy", false: "unhealthy"}[healthy],
		})
		return
	}
	
	// Get health for all providers (mock implementation)
	providers := []string{"openai", "gemini", "claude", "perplexity", "mistral", "deepseek", "moonshot", "ollama", "aws", "azure", "ali"}
	healthStatus := make(map[string]interface{})
	
	for _, p := range providers {
		healthy, _ := h.routingService.GetProviderHealth(c.Request.Context(), p)
		healthStatus[p] = gin.H{
			"healthy": healthy,
			"status":  map[bool]string{true: "healthy", false: "unhealthy"}[healthy],
		}
	}
	
	utils.SuccessResponse(c, gin.H{
		"providers": healthStatus,
		"timestamp": gin.H{},
	})
}

// UpdateProviderHealth updates the health status of a provider
func (h *RoutingHandler) UpdateProviderHealth(c *gin.Context) {
	var req struct {
		Provider string `json:"provider" binding:"required"`
		Healthy  bool   `json:"healthy"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}
	
	if err := h.routingService.UpdateProviderHealth(c.Request.Context(), req.Provider, req.Healthy); err != nil {
		logger.Error("Failed to update provider health", "error", err, "provider", req.Provider)
		utils.InternalErrorResponse(c, "Failed to update provider health")
		return
	}
	
	logger.Info("Updated provider health", "provider", req.Provider, "healthy", req.Healthy)
	utils.SuccessResponse(c, gin.H{
		"provider": req.Provider,
		"healthy":  req.Healthy,
		"message":  "Provider health updated successfully",
	})
}

// GetRoutingStats returns routing statistics
func (h *RoutingHandler) GetRoutingStats(c *gin.Context) {
	hours := c.DefaultQuery("hours", "24")
	hoursInt, err := strconv.Atoi(hours)
	if err != nil || hoursInt < 1 {
		hoursInt = 24
	}
	
	stats, err := h.routingService.GetRoutingStats(c.Request.Context(), hoursInt)
	if err != nil {
		logger.Error("Failed to get routing stats", "error", err)
		utils.InternalErrorResponse(c, "Failed to get routing statistics")
		return
	}
	
	logger.Info("Retrieved routing stats", "time_range_hours", hoursInt)
	utils.SuccessResponse(c, stats)
}
