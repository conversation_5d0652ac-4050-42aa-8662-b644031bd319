package handlers

import (
	"net/http"
	"strings"

	"llm-proxy-system/internal/auth"
	"llm-proxy-system/internal/logger"
	"llm-proxy-system/internal/utils"

	"github.com/gin-gonic/gin"
)

// AuthHandler handles authentication endpoints
type AuthHandler struct {
	authService *auth.Service
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(authService *auth.Service) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

// Register handles user registration
func (h *AuthHandler) Register(c *gin.Context) {
	var req auth.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}
	
	// Additional validation
	if len(req.Password) < 8 {
		utils.ValidationErrorResponse(c, "Password must be at least 8 characters long")
		return
	}
	
	response, err := h.authService.Register(c.Request.Context(), &req)
	if err != nil {
		if strings.Contains(err.Error(), "already exists") {
			utils.ConflictResponse(c, err.Error())
			return
		}
		
		logger.Error("Registration failed", "error", err, "email", req.Email)
		utils.InternalErrorResponse(c, "Registration failed")
		return
	}
	
	logger.Info("User registered successfully", "user_id", response.User.ID, "email", response.User.Email)
	utils.CreatedResponse(c, response)
}

// Login handles user login
func (h *AuthHandler) Login(c *gin.Context) {
	var req auth.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}
	
	response, err := h.authService.Login(c.Request.Context(), &req)
	if err != nil {
		if strings.Contains(err.Error(), "invalid email or password") || 
		   strings.Contains(err.Error(), "disabled") {
			utils.UnauthorizedResponse(c, err.Error())
			return
		}
		
		logger.Error("Login failed", "error", err, "email", req.Email)
		utils.InternalErrorResponse(c, "Login failed")
		return
	}
	
	logger.Info("User logged in successfully", "user_id", response.User.ID, "email", response.User.Email)
	utils.SuccessResponse(c, response)
}

// RefreshToken handles token refresh
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req struct {
		RefreshToken string `json:"refresh_token" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}
	
	response, err := h.authService.RefreshToken(c.Request.Context(), req.RefreshToken)
	if err != nil {
		if strings.Contains(err.Error(), "invalid") || strings.Contains(err.Error(), "expired") {
			utils.UnauthorizedResponse(c, "Invalid or expired refresh token")
			return
		}
		
		logger.Error("Token refresh failed", "error", err)
		utils.InternalErrorResponse(c, "Token refresh failed")
		return
	}
	
	logger.Debug("Token refreshed successfully", "user_id", response.User.ID)
	utils.SuccessResponse(c, response)
}

// GetMe handles getting current user info
func (h *AuthHandler) GetMe(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}
	
	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}
	
	user, err := h.authService.GetCurrentUser(c.Request.Context(), userIDUint)
	if err != nil {
		logger.Error("Failed to get current user", "error", err, "user_id", userIDUint)
		utils.InternalErrorResponse(c, "Failed to get user information")
		return
	}
	
	utils.SuccessResponse(c, user)
}

// Logout handles user logout
func (h *AuthHandler) Logout(c *gin.Context) {
	// Extract token from Authorization header
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" || len(authHeader) < 7 || authHeader[:7] != "Bearer " {
		utils.UnauthorizedResponse(c, "Invalid authorization header")
		return
	}
	
	token := authHeader[7:]
	
	if err := h.authService.Logout(c.Request.Context(), token); err != nil {
		logger.Error("Logout failed", "error", err)
		utils.InternalErrorResponse(c, "Logout failed")
		return
	}
	
	utils.SuccessResponse(c, gin.H{"message": "Logged out successfully"})
}

// ChangePassword handles password change
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}
	
	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}
	
	var req struct {
		CurrentPassword string `json:"current_password" binding:"required"`
		NewPassword     string `json:"new_password" binding:"required,min=8"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}
	
	if err := h.authService.ChangePassword(c.Request.Context(), userIDUint, req.CurrentPassword, req.NewPassword); err != nil {
		if strings.Contains(err.Error(), "invalid current password") {
			utils.UnauthorizedResponse(c, err.Error())
			return
		}
		
		logger.Error("Password change failed", "error", err, "user_id", userIDUint)
		utils.InternalErrorResponse(c, "Password change failed")
		return
	}
	
	logger.Info("Password changed successfully", "user_id", userIDUint)
	utils.SuccessResponse(c, gin.H{"message": "Password changed successfully"})
}

// ValidateAPIKey middleware validates system API keys
func (h *AuthHandler) ValidateAPIKey(c *gin.Context) {
	// Extract API key from Authorization header
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		utils.UnauthorizedResponse(c, "Authorization header required")
		c.Abort()
		return
	}
	
	var apiKey string
	if strings.HasPrefix(authHeader, "Bearer ") {
		apiKey = authHeader[7:]
	} else {
		apiKey = authHeader
	}
	
	user, err := h.authService.ValidateSystemAPIKey(c.Request.Context(), apiKey)
	if err != nil {
		logger.SecurityLogger("api_key_validation_failed", "", c.ClientIP()).Warn("Invalid API key attempt")
		utils.UnauthorizedResponse(c, "Invalid API key")
		c.Abort()
		return
	}
	
	// Set user information in context
	c.Set("user_id", user.ID)
	c.Set("user_email", user.Email)
	c.Set("user_role", user.Role)
	c.Set("auth_method", "api_key")
	
	c.Next()
}

// ValidateJWT middleware validates JWT tokens
func (h *AuthHandler) ValidateJWT(c *gin.Context) {
	// Extract token from Authorization header
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" || len(authHeader) < 7 || authHeader[:7] != "Bearer " {
		utils.UnauthorizedResponse(c, "Authorization header required")
		c.Abort()
		return
	}
	
	token := authHeader[7:]
	
	// Check if token is blacklisted
	if h.authService.IsTokenBlacklisted(c.Request.Context(), token) {
		utils.UnauthorizedResponse(c, "Token has been revoked")
		c.Abort()
		return
	}
	
	// TODO: Implement JWT validation through auth service
	// For now, just pass through with placeholder validation
	logger.SecurityLogger("jwt_validation_placeholder", "", c.ClientIP()).Warn("JWT validation not fully implemented")

	// Placeholder claims
	userID := uint(1)
	email := "<EMAIL>"
	role := "user"
	
	// Set user information in context
	c.Set("user_id", userID)
	c.Set("user_email", email)
	c.Set("user_role", role)
	c.Set("auth_method", "jwt")
	
	c.Next()
}

// RequireAdmin middleware ensures user has admin role
func (h *AuthHandler) RequireAdmin(c *gin.Context) {
	role, exists := c.Get("user_role")
	if !exists {
		utils.ForbiddenResponse(c, "User role not found")
		c.Abort()
		return
	}
	
	userRole, ok := role.(string)
	if !ok || userRole != "admin" {
		utils.ForbiddenResponse(c, "Admin access required")
		c.Abort()
		return
	}
	
	c.Next()
}
