package handlers

import (
	"net/http"
	"strconv"

	"llm-proxy-system/internal/adapters"
	"llm-proxy-system/internal/logger"
	"llm-proxy-system/internal/utils"

	"github.com/gin-gonic/gin"
)

// AdapterHandler handles adapter-related endpoints
type AdapterHandler struct {
	adapterService *adapters.Service
}

// NewAdapterHandler creates a new adapter handler
func NewAdapterHandler(adapterService *adapters.Service) *AdapterHandler {
	return &AdapterHandler{
		adapterService: adapterService,
	}
}

// GetSupportedProviders returns a list of all supported providers
func (h *AdapterHandler) GetSupportedProviders(c *gin.Context) {
	providers := h.adapterService.GetSupportedProviders()
	
	logger.Info("Retrieved supported providers", "count", len(providers))
	utils.SuccessResponse(c, gin.H{
		"providers": providers,
		"count":     len(providers),
	})
}

// GetProviderInfo returns detailed information about a specific provider
func (h *AdapterHandler) GetProviderInfo(c *gin.Context) {
	providerName := c.Param("provider")
	if providerName == "" {
		utils.ValidationErrorResponse(c, "Provider name is required")
		return
	}
	
	providers := h.adapterService.GetSupportedProviders()
	
	var providerInfo *adapters.ProviderInfo
	for _, provider := range providers {
		if provider.Name == providerName {
			providerInfo = &provider
			break
		}
	}
	
	if providerInfo == nil {
		utils.NotFoundResponse(c, "Provider not found")
		return
	}
	
	logger.Info("Retrieved provider info", "provider", providerName)
	utils.SuccessResponse(c, providerInfo)
}

// GetProviderByModel returns which provider should handle a specific model
func (h *AdapterHandler) GetProviderByModel(c *gin.Context) {
	model := c.Query("model")
	if model == "" {
		utils.ValidationErrorResponse(c, "Model parameter is required")
		return
	}
	
	provider, err := h.adapterService.GetProviderByModel(model)
	if err != nil {
		logger.Error("Failed to get provider for model", "error", err, "model", model)
		utils.NotFoundResponse(c, "No provider found for model")
		return
	}
	
	logger.Info("Found provider for model", "model", model, "provider", provider)
	utils.SuccessResponse(c, gin.H{
		"model":    model,
		"provider": provider,
	})
}

// ValidateProviderKey validates an API key for a specific provider
func (h *AdapterHandler) ValidateProviderKey(c *gin.Context) {
	var req struct {
		Provider string `json:"provider" binding:"required"`
		APIKey   string `json:"api_key" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}
	
	// Validate the provider key
	err := h.adapterService.ValidateProviderKey(c.Request.Context(), req.Provider, req.APIKey)
	if err != nil {
		logger.Error("Provider key validation failed", "error", err, "provider", req.Provider)
		utils.ValidationErrorResponse(c, "Invalid API key for provider", err.Error())
		return
	}
	
	logger.Info("Provider key validated successfully", "provider", req.Provider)
	utils.SuccessResponse(c, gin.H{
		"provider": req.Provider,
		"valid":    true,
		"message":  "API key is valid",
	})
}

// TestProviderConnection tests the connection to a provider with a simple request
func (h *AdapterHandler) TestProviderConnection(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.UnauthorizedResponse(c, "User not authenticated")
		return
	}
	
	userIDUint, ok := userID.(uint)
	if !ok {
		utils.InternalErrorResponse(c, "Invalid user ID")
		return
	}
	
	var req struct {
		Provider string `json:"provider" binding:"required"`
		Model    string `json:"model,omitempty"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "Invalid request data", err.Error())
		return
	}
	
	// Create adapter for the user and provider
	adapter, err := h.adapterService.CreateProviderAdapter(c.Request.Context(), userIDUint, req.Provider)
	if err != nil {
		logger.Error("Failed to create provider adapter", "error", err, "provider", req.Provider, "user_id", userIDUint)
		utils.ValidationErrorResponse(c, "Failed to create adapter for provider", err.Error())
		return
	}
	
	// Use default model if not specified
	model := req.Model
	if model == "" {
		models := adapter.GetSupportedModels()
		if len(models) > 0 {
			model = models[0]
		} else {
			model = "gpt-3.5-turbo" // Fallback
		}
	}
	
	logger.Info("Testing provider connection", "provider", req.Provider, "model", model, "user_id", userIDUint)
	
	// For now, just return success if adapter was created
	// In a full implementation, we would make a test request
	utils.SuccessResponse(c, gin.H{
		"provider":   req.Provider,
		"model":      model,
		"connected":  true,
		"message":    "Connection test successful",
		"adapter":    adapter.GetProviderName(),
	})
}

// GetProviderModels returns the supported models for a specific provider
func (h *AdapterHandler) GetProviderModels(c *gin.Context) {
	providerName := c.Param("provider")
	if providerName == "" {
		utils.ValidationErrorResponse(c, "Provider name is required")
		return
	}
	
	providers := h.adapterService.GetSupportedProviders()
	
	var models []string
	for _, provider := range providers {
		if provider.Name == providerName {
			models = provider.Models
			break
		}
	}
	
	if models == nil {
		utils.NotFoundResponse(c, "Provider not found")
		return
	}
	
	logger.Info("Retrieved provider models", "provider", providerName, "count", len(models))
	utils.SuccessResponse(c, gin.H{
		"provider": providerName,
		"models":   models,
		"count":    len(models),
	})
}

// GetProviderCapabilities returns the capabilities of a specific provider
func (h *AdapterHandler) GetProviderCapabilities(c *gin.Context) {
	providerName := c.Param("provider")
	if providerName == "" {
		utils.ValidationErrorResponse(c, "Provider name is required")
		return
	}
	
	providers := h.adapterService.GetSupportedProviders()
	
	var capabilities []string
	for _, provider := range providers {
		if provider.Name == providerName {
			capabilities = provider.Capabilities
			break
		}
	}
	
	if capabilities == nil {
		utils.NotFoundResponse(c, "Provider not found")
		return
	}
	
	logger.Info("Retrieved provider capabilities", "provider", providerName, "count", len(capabilities))
	utils.SuccessResponse(c, gin.H{
		"provider":     providerName,
		"capabilities": capabilities,
		"count":        len(capabilities),
	})
}

// GetProviderStatus returns the status of all providers or a specific provider
func (h *AdapterHandler) GetProviderStatus(c *gin.Context) {
	providerName := c.Query("provider")
	
	providers := h.adapterService.GetSupportedProviders()
	
	if providerName != "" {
		// Return status for specific provider
		for _, provider := range providers {
			if provider.Name == providerName {
				utils.SuccessResponse(c, gin.H{
					"provider": provider.Name,
					"status":   "active",
					"active":   provider.IsActive,
					"models":   len(provider.Models),
				})
				return
			}
		}
		utils.NotFoundResponse(c, "Provider not found")
		return
	}
	
	// Return status for all providers
	statuses := make([]gin.H, len(providers))
	for i, provider := range providers {
		statuses[i] = gin.H{
			"provider": provider.Name,
			"status":   "active",
			"active":   provider.IsActive,
			"models":   len(provider.Models),
		}
	}
	
	logger.Info("Retrieved provider statuses", "count", len(statuses))
	utils.SuccessResponse(c, gin.H{
		"providers": statuses,
		"count":     len(statuses),
	})
}

// GetAdapterMetrics returns metrics about adapter usage
func (h *AdapterHandler) GetAdapterMetrics(c *gin.Context) {
	// Get optional time range parameters
	hours := c.DefaultQuery("hours", "24")
	hoursInt, err := strconv.Atoi(hours)
	if err != nil || hoursInt < 1 {
		hoursInt = 24
	}
	
	// For now, return mock metrics
	// In a full implementation, this would query the usage logs
	metrics := gin.H{
		"time_range_hours": hoursInt,
		"total_requests":   1000,
		"successful_requests": 950,
		"failed_requests":  50,
		"success_rate":     95.0,
		"providers": gin.H{
			"openai":     gin.H{"requests": 400, "success_rate": 98.0},
			"gemini":     gin.H{"requests": 300, "success_rate": 96.0},
			"claude":     gin.H{"requests": 200, "success_rate": 94.0},
			"perplexity": gin.H{"requests": 100, "success_rate": 92.0},
		},
		"models": gin.H{
			"gpt-4":              gin.H{"requests": 250, "success_rate": 98.5},
			"gpt-3.5-turbo":      gin.H{"requests": 150, "success_rate": 97.0},
			"gemini-1.5-pro":     gin.H{"requests": 200, "success_rate": 96.0},
			"claude-3-sonnet":    gin.H{"requests": 150, "success_rate": 94.0},
		},
	}
	
	logger.Info("Retrieved adapter metrics", "time_range_hours", hoursInt)
	utils.SuccessResponse(c, metrics)
}
