package adapters

import (
	"context"
	"testing"

	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/database"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestDB() (*gorm.DB, error) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	
	// Run migrations
	err = db.AutoMigrate(
		&database.User{},
		&database.UserAPIKey{},
		&database.AdminGlobalRoutingRule{},
		&database.UsageLog{},
		&database.WebhookConfig{},
	)
	if err != nil {
		return nil, err
	}
	
	return db, nil
}

func setupTestService() (*Service, error) {
	db, err := setupTestDB()
	if err != nil {
		return nil, err
	}
	
	cfg := &config.Config{
		EncryptionKey: "12345678901234567890123456789012", // 32 chars
		Provider: struct {
			TimeoutSeconds int `json:"timeout_seconds"`
			MaxRetries     int `json:"max_retries"`
		}{
			TimeoutSeconds: 30,
			MaxRetries:     3,
		},
	}
	
	return NewService(db, cfg)
}

func TestNewService(t *testing.T) {
	service, err := setupTestService()
	if err != nil {
		t.Fatalf("Failed to create service: %v", err)
	}
	
	if service == nil {
		t.Fatal("Service is nil")
	}
	
	if service.registry == nil {
		t.Error("Registry is nil")
	}
	
	if service.factory == nil {
		t.Error("Factory is nil")
	}
}

func TestGetSupportedProviders(t *testing.T) {
	service, err := setupTestService()
	if err != nil {
		t.Fatalf("Failed to create service: %v", err)
	}
	
	providers := service.GetSupportedProviders()
	
	if len(providers) == 0 {
		t.Error("No providers returned")
	}
	
	// Check for expected providers
	expectedProviders := []string{"openai", "gemini", "claude", "perplexity", "mistral"}
	providerMap := make(map[string]bool)
	for _, provider := range providers {
		providerMap[provider.Name] = true
	}
	
	for _, expected := range expectedProviders {
		if !providerMap[expected] {
			t.Errorf("Expected provider %s not found", expected)
		}
	}
	
	// Validate provider structure
	for _, provider := range providers {
		if provider.Name == "" {
			t.Error("Provider name is empty")
		}
		if provider.DisplayName == "" {
			t.Error("Provider display name is empty")
		}
		if len(provider.Models) == 0 {
			t.Errorf("Provider %s has no models", provider.Name)
		}
		if len(provider.Capabilities) == 0 {
			t.Errorf("Provider %s has no capabilities", provider.Name)
		}
	}
}

func TestGetProviderByModel(t *testing.T) {
	service, err := setupTestService()
	if err != nil {
		t.Fatalf("Failed to create service: %v", err)
	}
	
	testCases := []struct {
		model            string
		expectedProvider string
	}{
		{"gpt-4", "openai"},
		{"gpt-3.5-turbo", "openai"},
		{"gemini-1.5-pro", "gemini"},
		{"claude-3-sonnet", "claude"},
		{"mistral-large", "mistral"},
		{"deepseek-chat", "deepseek"},
		{"llama3.2", "ollama"},
	}
	
	for _, tc := range testCases {
		provider, err := service.GetProviderByModel(tc.model)
		if err != nil {
			t.Errorf("Failed to get provider for model %s: %v", tc.model, err)
			continue
		}
		
		if provider != tc.expectedProvider {
			t.Errorf("Expected provider %s for model %s, got %s", tc.expectedProvider, tc.model, provider)
		}
	}
}

func TestValidateProviderKey(t *testing.T) {
	service, err := setupTestService()
	if err != nil {
		t.Fatalf("Failed to create service: %v", err)
	}
	
	ctx := context.Background()
	
	// Test with mock provider and key
	err = service.ValidateProviderKey(ctx, "openai", "test-api-key")
	if err != nil {
		t.Errorf("Provider key validation failed: %v", err)
	}
	
	// Test with invalid provider
	err = service.ValidateProviderKey(ctx, "invalid-provider", "test-api-key")
	if err == nil {
		t.Error("Expected error for invalid provider")
	}
}

func TestCreateProviderAdapter(t *testing.T) {
	service, err := setupTestService()
	if err != nil {
		t.Fatalf("Failed to create service: %v", err)
	}
	
	// Create test user and API key
	user := &database.User{
		Email:        "<EMAIL>",
		PasswordHash: "hashed_password",
		SystemAPIKey: "sk-sys_test_key",
		Role:         database.RoleUser,
		IsActive:     true,
	}
	
	if err := service.db.Create(user).Error; err != nil {
		t.Fatalf("Failed to create test user: %v", err)
	}
	
	// Encrypt test API key
	encryptedKey, err := service.crypto.Encrypt("test-openai-key")
	if err != nil {
		t.Fatalf("Failed to encrypt test key: %v", err)
	}
	
	apiKey := &database.UserAPIKey{
		UserID:          user.ID,
		Provider:        "openai",
		Name:            "Test OpenAI Key",
		EncryptedAPIKey: encryptedKey,
		Status:          database.StatusActive,
	}
	
	if err := service.db.Create(apiKey).Error; err != nil {
		t.Fatalf("Failed to create test API key: %v", err)
	}
	
	ctx := context.Background()
	
	// Test creating adapter
	adapter, err := service.CreateProviderAdapter(ctx, user.ID, "openai")
	if err != nil {
		t.Errorf("Failed to create provider adapter: %v", err)
	}
	
	if adapter == nil {
		t.Error("Adapter is nil")
	}
	
	if adapter.GetProviderName() != "openai" {
		t.Errorf("Expected provider name 'openai', got '%s'", adapter.GetProviderName())
	}
	
	// Test with non-existent provider
	_, err = service.CreateProviderAdapter(ctx, user.ID, "nonexistent")
	if err == nil {
		t.Error("Expected error for non-existent provider")
	}
	
	// Test with user that has no API keys for provider
	_, err = service.CreateProviderAdapter(ctx, user.ID, "gemini")
	if err == nil {
		t.Error("Expected error for provider with no API keys")
	}
}

func TestGetTestModel(t *testing.T) {
	service, err := setupTestService()
	if err != nil {
		t.Fatalf("Failed to create service: %v", err)
	}
	
	testCases := []struct {
		provider      string
		expectedModel string
	}{
		{"openai", "gpt-3.5-turbo"},
		{"gemini", "gemini-1.5-flash"},
		{"claude", "claude-3-haiku-20240307"},
		{"unknown", "gpt-3.5-turbo"}, // fallback
	}
	
	for _, tc := range testCases {
		model := service.getTestModel(tc.provider)
		if model != tc.expectedModel {
			t.Errorf("Expected test model %s for provider %s, got %s", tc.expectedModel, tc.provider, model)
		}
	}
}
