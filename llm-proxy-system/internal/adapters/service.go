package adapters

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/database"
	"llm-proxy-system/internal/logger"
	"llm-proxy-system/internal/utils"

	// Import the existing adapter system
	"llm-proxy-system/pkg/adapters/core"
	"llm-proxy-system/pkg/adapters/providers"

	"gorm.io/gorm"
)

// Service handles adapter operations and provider management
type Service struct {
	db       *gorm.DB
	config   *config.Config
	registry *providers.Registry
	factory  *providers.AdapterFactory
	keyRepo  *database.UserAPIKeyRepository
	crypto   *utils.CryptoService
}

// NewService creates a new adapter service
func NewService(db *gorm.DB, cfg *config.Config) (*Service, error) {
	crypto, err := utils.NewCryptoService(cfg.EncryptionKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create crypto service: %w", err)
	}

	return &Service{
		db:       db,
		config:   cfg,
		registry: providers.DefaultRegistry(),
		factory:  providers.NewAdapterFactory(),
		keyRepo:  database.NewUserAPIKeyRepository(db),
		crypto:   crypto,
	}, nil
}

// ProviderInfo represents information about a supported provider
type ProviderInfo struct {
	Name         string   `json:"name"`
	DisplayName  string   `json:"display_name"`
	Description  string   `json:"description"`
	Models       []string `json:"models"`
	Capabilities []string `json:"capabilities"`
	AuthType     string   `json:"auth_type"`
	BaseURL      string   `json:"base_url"`
	IsActive     bool     `json:"is_active"`
}

// ProxyRequest represents a request to be proxied to a provider
type ProxyRequest struct {
	UserID       uint                   `json:"user_id"`
	Provider     string                 `json:"provider,omitempty"` // Optional: force specific provider
	Model        string                 `json:"model"`
	Messages     []core.Message         `json:"messages,omitempty"`
	Prompt       interface{}            `json:"prompt,omitempty"`
	MaxTokens    *int                   `json:"max_tokens,omitempty"`
	Temperature  *float64               `json:"temperature,omitempty"`
	TopP         *float64               `json:"top_p,omitempty"`
	TopK         *int                   `json:"top_k,omitempty"`
	Stream       bool                   `json:"stream,omitempty"`
	Stop         interface{}            `json:"stop,omitempty"`
	Tools        []core.Tool            `json:"tools,omitempty"`
	ToolChoice   interface{}            `json:"tool_choice,omitempty"`
	Functions    []core.Function        `json:"functions,omitempty"`
	FunctionCall interface{}            `json:"function_call,omitempty"`
	User         string                 `json:"user,omitempty"`
	Seed         *int                   `json:"seed,omitempty"`
	RequestID    string                 `json:"request_id"`
	IPAddress    string                 `json:"ip_address"`
	UserAgent    string                 `json:"user_agent"`
}

// ProxyResponse represents a response from a provider
type ProxyResponse struct {
	Provider     string             `json:"provider"`
	Model        string             `json:"model"`
	Response     *core.OpenAIResponse `json:"response"`
	Duration     time.Duration      `json:"duration"`
	TokensUsed   int                `json:"tokens_used"`
	RequestID    string             `json:"request_id"`
	Error        error              `json:"error,omitempty"`
}

// GetSupportedProviders returns a list of all supported providers
func (s *Service) GetSupportedProviders() []ProviderInfo {
	supportedProviders := s.factory.GetSupportedProviders()
	providers := make([]ProviderInfo, 0, len(supportedProviders))

	providerDetails := map[string]ProviderInfo{
		"openai": {
			Name:         "openai",
			DisplayName:  "OpenAI",
			Description:  "OpenAI GPT models including GPT-4, GPT-3.5, and embeddings",
			Capabilities: []string{"chat", "completion", "embedding", "image", "audio"},
			AuthType:     "bearer_token",
			BaseURL:      "https://api.openai.com/v1",
		},
		"gemini": {
			Name:         "gemini",
			DisplayName:  "Google Gemini",
			Description:  "Google's Gemini models with advanced reasoning capabilities",
			Capabilities: []string{"chat", "completion", "embedding", "vision"},
			AuthType:     "api_key",
			BaseURL:      "https://generativelanguage.googleapis.com/v1beta",
		},
		"claude": {
			Name:         "claude",
			DisplayName:  "Anthropic Claude",
			Description:  "Anthropic's Claude models with strong reasoning and safety",
			Capabilities: []string{"chat", "completion", "vision"},
			AuthType:     "api_key",
			BaseURL:      "https://api.anthropic.com/v1",
		},
		"perplexity": {
			Name:         "perplexity",
			DisplayName:  "Perplexity AI",
			Description:  "Perplexity's online search-enabled language models",
			Capabilities: []string{"chat", "completion", "search"},
			AuthType:     "bearer_token",
			BaseURL:      "https://api.perplexity.ai",
		},
		"mistral": {
			Name:         "mistral",
			DisplayName:  "Mistral AI",
			Description:  "Mistral's efficient and powerful language models",
			Capabilities: []string{"chat", "completion", "embedding", "function_calling"},
			AuthType:     "bearer_token",
			BaseURL:      "https://api.mistral.ai/v1",
		},
		"deepseek": {
			Name:         "deepseek",
			DisplayName:  "Deepseek",
			Description:  "Deepseek's reasoning and coding specialized models",
			Capabilities: []string{"chat", "completion", "reasoning", "coding"},
			AuthType:     "bearer_token",
			BaseURL:      "https://api.deepseek.com",
		},
		"moonshot": {
			Name:         "moonshot",
			DisplayName:  "Moonshot AI",
			Description:  "Moonshot's long-context language models",
			Capabilities: []string{"chat", "completion", "long_context"},
			AuthType:     "bearer_token",
			BaseURL:      "https://api.moonshot.cn/v1",
		},
		"ollama": {
			Name:         "ollama",
			DisplayName:  "Ollama",
			Description:  "Local deployment of open-source language models",
			Capabilities: []string{"chat", "completion", "embedding", "local"},
			AuthType:     "none",
			BaseURL:      "http://localhost:11434",
		},
		"aws": {
			Name:         "aws",
			DisplayName:  "AWS Bedrock",
			Description:  "Amazon Bedrock with multiple model families",
			Capabilities: []string{"chat", "completion", "embedding", "enterprise"},
			AuthType:     "aws_signature",
			BaseURL:      "https://bedrock-runtime.us-east-1.amazonaws.com",
		},
		"azure": {
			Name:         "azure",
			DisplayName:  "Azure OpenAI",
			Description:  "Microsoft Azure OpenAI Service with enterprise features",
			Capabilities: []string{"chat", "completion", "embedding", "enterprise"},
			AuthType:     "api_key",
			BaseURL:      "https://your-resource.openai.azure.com",
		},
		"ali": {
			Name:         "ali",
			DisplayName:  "Alibaba Cloud",
			Description:  "Alibaba's Qwen models optimized for Chinese language",
			Capabilities: []string{"chat", "completion", "embedding", "chinese"},
			AuthType:     "bearer_token",
			BaseURL:      "https://dashscope.aliyuncs.com/api/v1",
		},
	}

	for _, providerName := range supportedProviders {
		if info, exists := providerDetails[providerName]; exists {
			// Get models from adapter
			adapter, err := s.factory.CreateAdapter(providerName, &core.AdapterConfig{})
			if err == nil {
				info.Models = adapter.GetSupportedModels()
			}
			info.IsActive = true
			providers = append(providers, info)
		}
	}

	return providers
}

// GetProviderByModel determines which provider should handle a specific model
func (s *Service) GetProviderByModel(model string) (string, error) {
	// Try to route the request using the adapter manager
	request := &core.OpenAIRequest{Model: model}
	
	manager := providers.NewAdapterManager()
	adapter, err := manager.RouteRequest(request)
	if err != nil {
		return "", fmt.Errorf("failed to route model %s: %w", model, err)
	}
	
	return adapter.GetProviderName(), nil
}

// ValidateProviderKey validates an API key for a specific provider
func (s *Service) ValidateProviderKey(ctx context.Context, provider, apiKey string) error {
	// Create adapter configuration
	config := &core.AdapterConfig{
		APIKey:  apiKey,
		BaseURL: "",
	}
	
	// Create adapter for the provider
	adapter, err := s.factory.CreateAdapter(provider, config)
	if err != nil {
		return fmt.Errorf("failed to create adapter for provider %s: %w", provider, err)
	}
	
	// Create a simple test request
	testRequest := &core.OpenAIRequest{
		Model: s.getTestModel(provider),
		Messages: []core.Message{
			{
				Role:    "user",
				Content: []byte(`"test"`),
			},
		},
		MaxTokens: intPtr(1),
	}
	
	// Convert request
	_, err = adapter.ConvertRequest(ctx, testRequest)
	if err != nil {
		return fmt.Errorf("provider key validation failed: %w", err)
	}
	
	logger.Info("Provider key validated successfully", "provider", provider)
	return nil
}

// CreateProviderAdapter creates an adapter for a specific provider with user's API key
func (s *Service) CreateProviderAdapter(ctx context.Context, userID uint, provider string) (core.Adapter, error) {
	// Get user's API key for this provider
	keys, err := s.keyRepo.GetByUserIDAndProvider(userID, provider)
	if err != nil {
		return nil, fmt.Errorf("failed to get API keys for provider %s: %w", provider, err)
	}
	
	// Find an active key
	var activeKey *database.UserAPIKey
	for _, key := range keys {
		if key.Status == database.StatusActive {
			activeKey = &key
			break
		}
	}
	
	if activeKey == nil {
		return nil, fmt.Errorf("no active API key found for provider %s", provider)
	}
	
	// Decrypt the API key
	decryptedKey, err := s.crypto.Decrypt(activeKey.EncryptedAPIKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt API key: %w", err)
	}
	
	// Create adapter configuration
	config := &core.AdapterConfig{
		APIKey:     decryptedKey,
		BaseURL:    "",
		HTTPClient: &http.Client{Timeout: time.Duration(s.config.Provider.TimeoutSeconds) * time.Second},
	}
	
	// Create and return adapter
	adapter, err := s.factory.CreateAdapter(provider, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create adapter for provider %s: %w", provider, err)
	}
	
	return adapter, nil
}

// Helper functions

func (s *Service) getTestModel(provider string) string {
	testModels := map[string]string{
		"openai":     "gpt-3.5-turbo",
		"gemini":     "gemini-1.5-flash",
		"claude":     "claude-3-haiku-20240307",
		"perplexity": "llama-3.1-sonar-small-128k-online",
		"mistral":    "mistral-small-latest",
		"deepseek":   "deepseek-chat",
		"moonshot":   "moonshot-v1-8k",
		"ollama":     "llama3.2",
		"aws":        "claude-3-haiku-20240307",
		"azure":      "gpt-35-turbo",
		"ali":        "qwen-turbo",
	}
	
	if model, exists := testModels[provider]; exists {
		return model
	}
	return "gpt-3.5-turbo" // Default fallback
}

func intPtr(i int) *int {
	return &i
}
