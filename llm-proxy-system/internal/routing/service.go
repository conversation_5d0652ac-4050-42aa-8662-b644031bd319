package routing

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"llm-proxy-system/internal/adapters"
	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/database"
	"llm-proxy-system/internal/logger"
	"llm-proxy-system/pkg/adapters/core"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// Service handles routing logic and provider selection
type Service struct {
	db             *gorm.DB
	redis          *redis.Client
	config         *config.Config
	adapterService *adapters.Service
	ruleRepo       *database.AdminGlobalRoutingRuleRepository
	keyRepo        *database.UserAPIKeyRepository
}

// NewService creates a new routing service
func NewService(db *gorm.DB, redis *redis.Client, cfg *config.Config, adapterService *adapters.Service) *Service {
	return &Service{
		db:             db,
		redis:          redis,
		config:         cfg,
		adapterService: adapterService,
		ruleRepo:       database.NewAdminGlobalRoutingRuleRepository(db),
		keyRepo:        database.NewUserAPIKeyRepository(db),
	}
}

// RoutingRequest represents a request for provider routing
type RoutingRequest struct {
	UserID       uint   `json:"user_id"`
	Model        string `json:"model"`
	Provider     string `json:"provider,omitempty"`     // Optional: force specific provider
	RequiredCaps []string `json:"required_caps,omitempty"` // Optional: required capabilities
}

// RoutingResult represents the result of provider routing
type RoutingResult struct {
	Provider     string        `json:"provider"`
	UserAPIKeyID uint          `json:"user_api_key_id"`
	Adapter      core.Adapter  `json:"-"` // Not serialized
	Priority     int           `json:"priority"`
	Reason       string        `json:"reason"`
	Fallbacks    []string      `json:"fallbacks,omitempty"`
	Duration     time.Duration `json:"duration"`
}

// RouteRequest routes a request to the best available provider
func (s *Service) RouteRequest(ctx context.Context, req *RoutingRequest) (*RoutingResult, error) {
	start := time.Now()
	
	logger.Info("Starting request routing", "user_id", req.UserID, "model", req.Model, "forced_provider", req.Provider)
	
	// If provider is explicitly specified, use it directly
	if req.Provider != "" {
		result, err := s.routeToSpecificProvider(ctx, req)
		if err != nil {
			return nil, fmt.Errorf("failed to route to specified provider %s: %w", req.Provider, err)
		}
		result.Duration = time.Since(start)
		return result, nil
	}
	
	// Get routing rules
	rules, err := s.ruleRepo.GetEnabled()
	if err != nil {
		return nil, fmt.Errorf("failed to get routing rules: %w", err)
	}
	
	// Filter rules by model if specified
	filteredRules := s.filterRulesByModel(rules, req.Model)
	
	// Sort by priority
	sort.Slice(filteredRules, func(i, j int) bool {
		return filteredRules[i].Priority < filteredRules[j].Priority
	})
	
	// Try each provider in priority order
	var fallbacks []string
	for _, rule := range filteredRules {
		result, err := s.tryProvider(ctx, req, rule.Provider)
		if err != nil {
			logger.Warn("Provider failed, trying next", "provider", rule.Provider, "error", err)
			fallbacks = append(fallbacks, rule.Provider)
			continue
		}
		
		result.Priority = rule.Priority
		result.Reason = fmt.Sprintf("Matched routing rule (priority %d)", rule.Priority)
		result.Fallbacks = fallbacks
		result.Duration = time.Since(start)
		
		logger.Info("Successfully routed request", "provider", result.Provider, "priority", result.Priority, "fallbacks", len(fallbacks))
		return result, nil
	}
	
	// If no rules matched, try automatic detection
	autoProvider, err := s.adapterService.GetProviderByModel(req.Model)
	if err == nil {
		result, err := s.tryProvider(ctx, req, autoProvider)
		if err == nil {
			result.Priority = 999 // Low priority for auto-detection
			result.Reason = "Automatic model-based detection"
			result.Fallbacks = fallbacks
			result.Duration = time.Since(start)
			
			logger.Info("Successfully routed via auto-detection", "provider", result.Provider, "model", req.Model)
			return result, nil
		}
		fallbacks = append(fallbacks, autoProvider)
	}
	
	return nil, fmt.Errorf("no available provider found for model %s after trying %d options", req.Model, len(fallbacks))
}

// routeToSpecificProvider routes to a specific provider
func (s *Service) routeToSpecificProvider(ctx context.Context, req *RoutingRequest) (*RoutingResult, error) {
	result, err := s.tryProvider(ctx, req, req.Provider)
	if err != nil {
		return nil, err
	}
	
	result.Priority = 0 // Highest priority for explicit selection
	result.Reason = "Explicitly specified provider"
	return result, nil
}

// tryProvider attempts to route to a specific provider
func (s *Service) tryProvider(ctx context.Context, req *RoutingRequest, provider string) (*RoutingResult, error) {
	// Check if user has active API keys for this provider
	keys, err := s.keyRepo.GetByUserIDAndProvider(req.UserID, provider)
	if err != nil {
		return nil, fmt.Errorf("failed to get API keys for provider %s: %w", provider, err)
	}
	
	// Find an active key
	var activeKey *database.UserAPIKey
	for _, key := range keys {
		if key.Status == database.StatusActive {
			activeKey = &key
			break
		}
	}
	
	if activeKey == nil {
		return nil, fmt.Errorf("no active API key found for provider %s", provider)
	}
	
	// Check if provider is healthy
	if !s.isProviderHealthy(ctx, provider) {
		return nil, fmt.Errorf("provider %s is not healthy", provider)
	}
	
	// Create adapter
	adapter, err := s.adapterService.CreateProviderAdapter(ctx, req.UserID, provider)
	if err != nil {
		return nil, fmt.Errorf("failed to create adapter for provider %s: %w", provider, err)
	}
	
	// Verify model is supported
	if !s.isModelSupported(adapter, req.Model) {
		return nil, fmt.Errorf("model %s not supported by provider %s", req.Model, provider)
	}
	
	// Check required capabilities
	if len(req.RequiredCaps) > 0 && !s.hasRequiredCapabilities(provider, req.RequiredCaps) {
		return nil, fmt.Errorf("provider %s does not support required capabilities: %v", provider, req.RequiredCaps)
	}
	
	return &RoutingResult{
		Provider:     provider,
		UserAPIKeyID: activeKey.ID,
		Adapter:      adapter,
	}, nil
}

// filterRulesByModel filters routing rules by model pattern
func (s *Service) filterRulesByModel(rules []database.AdminGlobalRoutingRule, model string) []database.AdminGlobalRoutingRule {
	var filtered []database.AdminGlobalRoutingRule
	
	for _, rule := range rules {
		if rule.ModelFilter == "" {
			// No filter means it applies to all models
			filtered = append(filtered, rule)
			continue
		}
		
		// Simple pattern matching (can be enhanced with regex)
		if s.matchesModelFilter(model, rule.ModelFilter) {
			filtered = append(filtered, rule)
		}
	}
	
	return filtered
}

// matchesModelFilter checks if a model matches a filter pattern
func (s *Service) matchesModelFilter(model, filter string) bool {
	// Simple wildcard matching
	if filter == "*" {
		return true
	}
	
	// Exact match
	if strings.EqualFold(model, filter) {
		return true
	}
	
	// Prefix match with wildcard
	if strings.HasSuffix(filter, "*") {
		prefix := strings.TrimSuffix(filter, "*")
		return strings.HasPrefix(strings.ToLower(model), strings.ToLower(prefix))
	}
	
	// Contains match
	return strings.Contains(strings.ToLower(model), strings.ToLower(filter))
}

// isProviderHealthy checks if a provider is healthy
func (s *Service) isProviderHealthy(ctx context.Context, provider string) bool {
	// Check Redis cache for provider health status
	cacheKey := fmt.Sprintf("provider_health:%s", provider)
	
	status, err := s.redis.Get(ctx, cacheKey).Result()
	if err == nil {
		return status == "healthy"
	}
	
	// If not cached, assume healthy (health checks would be implemented separately)
	// In production, this would check actual provider health
	return true
}

// isModelSupported checks if a model is supported by the adapter
func (s *Service) isModelSupported(adapter core.Adapter, model string) bool {
	supportedModels := adapter.GetSupportedModels()
	
	for _, supportedModel := range supportedModels {
		if strings.EqualFold(model, supportedModel) {
			return true
		}
	}
	
	// For flexibility, also check if the model name contains the provider name
	providerName := adapter.GetProviderName()
	modelLower := strings.ToLower(model)
	
	switch providerName {
	case "openai":
		return strings.Contains(modelLower, "gpt") || strings.Contains(modelLower, "text-embedding") || 
		       strings.Contains(modelLower, "whisper") || strings.Contains(modelLower, "dall-e")
	case "gemini":
		return strings.Contains(modelLower, "gemini")
	case "claude":
		return strings.Contains(modelLower, "claude")
	case "perplexity":
		return strings.Contains(modelLower, "sonar") || strings.Contains(modelLower, "perplexity")
	case "mistral":
		return strings.Contains(modelLower, "mistral") || strings.Contains(modelLower, "mixtral")
	case "deepseek":
		return strings.Contains(modelLower, "deepseek")
	case "moonshot":
		return strings.Contains(modelLower, "moonshot")
	case "ollama":
		return strings.Contains(modelLower, "llama") || strings.Contains(modelLower, "phi") || 
		       strings.Contains(modelLower, "gemma") || strings.Contains(modelLower, "qwen")
	}
	
	return false
}

// hasRequiredCapabilities checks if a provider has required capabilities
func (s *Service) hasRequiredCapabilities(provider string, requiredCaps []string) bool {
	providers := s.adapterService.GetSupportedProviders()
	
	for _, p := range providers {
		if p.Name == provider {
			providerCaps := make(map[string]bool)
			for _, cap := range p.Capabilities {
				providerCaps[cap] = true
			}
			
			for _, required := range requiredCaps {
				if !providerCaps[required] {
					return false
				}
			}
			return true
		}
	}
	
	return false
}

// UpdateProviderHealth updates the health status of a provider
func (s *Service) UpdateProviderHealth(ctx context.Context, provider string, healthy bool) error {
	cacheKey := fmt.Sprintf("provider_health:%s", provider)
	status := "unhealthy"
	if healthy {
		status = "healthy"
	}
	
	// Cache for 5 minutes
	err := s.redis.Set(ctx, cacheKey, status, 5*time.Minute).Err()
	if err != nil {
		logger.Error("Failed to update provider health cache", "provider", provider, "error", err)
		return err
	}
	
	logger.Info("Updated provider health", "provider", provider, "healthy", healthy)
	return nil
}

// GetProviderHealth gets the health status of a provider
func (s *Service) GetProviderHealth(ctx context.Context, provider string) (bool, error) {
	cacheKey := fmt.Sprintf("provider_health:%s", provider)
	
	status, err := s.redis.Get(ctx, cacheKey).Result()
	if err != nil {
		// If not cached, assume healthy
		return true, nil
	}
	
	return status == "healthy", nil
}

// GetRoutingStats returns routing statistics
func (s *Service) GetRoutingStats(ctx context.Context, hours int) (map[string]interface{}, error) {
	// This would typically query usage logs for actual statistics
	// For now, return mock data
	
	stats := map[string]interface{}{
		"time_range_hours": hours,
		"total_requests":   1000,
		"successful_routes": 950,
		"failed_routes":    50,
		"success_rate":     95.0,
		"providers": map[string]interface{}{
			"openai":     map[string]interface{}{"requests": 400, "success_rate": 98.0, "avg_priority": 1.2},
			"gemini":     map[string]interface{}{"requests": 300, "success_rate": 96.0, "avg_priority": 2.1},
			"claude":     map[string]interface{}{"requests": 200, "success_rate": 94.0, "avg_priority": 3.0},
			"perplexity": map[string]interface{}{"requests": 50, "success_rate": 92.0, "avg_priority": 4.5},
		},
		"fallback_rate": 15.0,
		"avg_routing_time_ms": 25.5,
	}
	
	return stats, nil
}
