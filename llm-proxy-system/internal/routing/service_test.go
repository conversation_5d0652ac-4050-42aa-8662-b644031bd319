package routing

import (
	"context"
	"testing"
	"time"

	"llm-proxy-system/internal/adapters"
	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/database"

	"github.com/redis/go-redis/v9"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestRoutingService() (*Service, error) {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	
	// Run migrations
	err = db.AutoMigrate(
		&database.User{},
		&database.UserAPIKey{},
		&database.AdminGlobalRoutingRule{},
		&database.UsageLog{},
		&database.WebhookConfig{},
	)
	if err != nil {
		return nil, err
	}
	
	// Setup test Redis (mock)
	redisClient := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   1, // Use test database
	})
	
	// Setup test config
	cfg := &config.Config{
		EncryptionKey: "12345678901234567890123456789012", // 32 chars
		Provider: struct {
			TimeoutSeconds int `json:"timeout_seconds"`
			MaxRetries     int `json:"max_retries"`
		}{
			TimeoutSeconds: 30,
			MaxRetries:     3,
		},
	}
	
	// Setup adapter service
	adapterService, err := adapters.NewService(db, cfg)
	if err != nil {
		return nil, err
	}
	
	// Create routing service
	return NewService(db, redisClient, cfg, adapterService), nil
}

func setupTestData(db *gorm.DB, service *Service) error {
	// Create test user
	user := &database.User{
		Email:        "<EMAIL>",
		PasswordHash: "hashed_password",
		SystemAPIKey: "sk-sys_test_key",
		Role:         database.RoleUser,
		IsActive:     true,
	}
	
	if err := db.Create(user).Error; err != nil {
		return err
	}
	
	// For testing, we'll use plain text keys (in production these would be encrypted)
	encryptedOpenAIKey := "test-openai-key"
	encryptedGeminiKey := "test-gemini-key"
	
	// Create test API keys
	apiKeys := []*database.UserAPIKey{
		{
			UserID:          user.ID,
			Provider:        "openai",
			Name:            "Test OpenAI Key",
			EncryptedAPIKey: encryptedOpenAIKey,
			Status:          database.StatusActive,
		},
		{
			UserID:          user.ID,
			Provider:        "gemini",
			Name:            "Test Gemini Key",
			EncryptedAPIKey: encryptedGeminiKey,
			Status:          database.StatusActive,
		},
	}
	
	for _, key := range apiKeys {
		if err := db.Create(key).Error; err != nil {
			return err
		}
	}
	
	// Create test routing rules
	rules := []*database.AdminGlobalRoutingRule{
		{
			Provider:    "openai",
			Priority:    1,
			IsEnabled:   true,
			ModelFilter: "gpt*",
			Description: "OpenAI GPT models",
		},
		{
			Provider:    "gemini",
			Priority:    2,
			IsEnabled:   true,
			ModelFilter: "gemini*",
			Description: "Google Gemini models",
		},
		{
			Provider:    "claude",
			Priority:    3,
			IsEnabled:   true,
			ModelFilter: "claude*",
			Description: "Anthropic Claude models",
		},
	}
	
	for _, rule := range rules {
		if err := db.Create(rule).Error; err != nil {
			return err
		}
	}
	
	return nil
}

func TestNewService(t *testing.T) {
	service, err := setupTestRoutingService()
	if err != nil {
		t.Fatalf("Failed to create routing service: %v", err)
	}
	
	if service == nil {
		t.Fatal("Service is nil")
	}
	
	if service.ruleRepo == nil {
		t.Error("Rule repository is nil")
	}
	
	if service.keyRepo == nil {
		t.Error("Key repository is nil")
	}
	
	if service.adapterService == nil {
		t.Error("Adapter service is nil")
	}
}

func TestRouteRequestWithSpecificProvider(t *testing.T) {
	service, err := setupTestRoutingService()
	if err != nil {
		t.Fatalf("Failed to create routing service: %v", err)
	}
	
	if err := setupTestData(service.db, service); err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Test routing to specific provider
	req := &RoutingRequest{
		UserID:   1,
		Model:    "gpt-4",
		Provider: "openai",
	}
	
	result, err := service.RouteRequest(ctx, req)
	if err != nil {
		t.Errorf("Failed to route request: %v", err)
	}
	
	if result == nil {
		t.Fatal("Result is nil")
	}
	
	if result.Provider != "openai" {
		t.Errorf("Expected provider 'openai', got '%s'", result.Provider)
	}
	
	if result.Priority != 0 {
		t.Errorf("Expected priority 0 for explicit provider, got %d", result.Priority)
	}
	
	if result.Reason != "Explicitly specified provider" {
		t.Errorf("Expected explicit provider reason, got '%s'", result.Reason)
	}
}

func TestRouteRequestWithRules(t *testing.T) {
	service, err := setupTestRoutingService()
	if err != nil {
		t.Fatalf("Failed to create routing service: %v", err)
	}
	
	if err := setupTestData(service.db, service); err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	testCases := []struct {
		model            string
		expectedProvider string
		expectedPriority int
	}{
		{"gpt-4", "openai", 1},
		{"gpt-3.5-turbo", "openai", 1},
		{"gemini-1.5-pro", "gemini", 2},
	}
	
	for _, tc := range testCases {
		req := &RoutingRequest{
			UserID: 1,
			Model:  tc.model,
		}
		
		result, err := service.RouteRequest(ctx, req)
		if err != nil {
			t.Errorf("Failed to route request for model %s: %v", tc.model, err)
			continue
		}
		
		if result.Provider != tc.expectedProvider {
			t.Errorf("Expected provider %s for model %s, got %s", tc.expectedProvider, tc.model, result.Provider)
		}
		
		if result.Priority != tc.expectedPriority {
			t.Errorf("Expected priority %d for model %s, got %d", tc.expectedPriority, tc.model, result.Priority)
		}
	}
}

func TestFilterRulesByModel(t *testing.T) {
	service, err := setupTestRoutingService()
	if err != nil {
		t.Fatalf("Failed to create routing service: %v", err)
	}
	
	rules := []database.AdminGlobalRoutingRule{
		{Provider: "openai", ModelFilter: "gpt*"},
		{Provider: "gemini", ModelFilter: "gemini*"},
		{Provider: "claude", ModelFilter: "claude*"},
		{Provider: "universal", ModelFilter: ""},
	}
	
	testCases := []struct {
		model         string
		expectedCount int
		expectedProviders []string
	}{
		{"gpt-4", 2, []string{"openai", "universal"}},
		{"gemini-1.5-pro", 2, []string{"gemini", "universal"}},
		{"claude-3-sonnet", 2, []string{"claude", "universal"}},
		{"unknown-model", 1, []string{"universal"}},
	}
	
	for _, tc := range testCases {
		filtered := service.filterRulesByModel(rules, tc.model)
		
		if len(filtered) != tc.expectedCount {
			t.Errorf("Expected %d rules for model %s, got %d", tc.expectedCount, tc.model, len(filtered))
			continue
		}
		
		providerMap := make(map[string]bool)
		for _, rule := range filtered {
			providerMap[rule.Provider] = true
		}
		
		for _, expectedProvider := range tc.expectedProviders {
			if !providerMap[expectedProvider] {
				t.Errorf("Expected provider %s in filtered rules for model %s", expectedProvider, tc.model)
			}
		}
	}
}

func TestMatchesModelFilter(t *testing.T) {
	service, err := setupTestRoutingService()
	if err != nil {
		t.Fatalf("Failed to create routing service: %v", err)
	}
	
	testCases := []struct {
		model    string
		filter   string
		expected bool
	}{
		{"gpt-4", "gpt*", true},
		{"gpt-3.5-turbo", "gpt*", true},
		{"gemini-1.5-pro", "gpt*", false},
		{"claude-3-sonnet", "claude*", true},
		{"any-model", "*", true},
		{"gpt-4", "gpt-4", true},
		{"gpt-3.5", "gpt-4", false},
		{"model-with-gpt", "gpt", true}, // Contains match
	}
	
	for _, tc := range testCases {
		result := service.matchesModelFilter(tc.model, tc.filter)
		if result != tc.expected {
			t.Errorf("Expected %v for model %s with filter %s, got %v", tc.expected, tc.model, tc.filter, result)
		}
	}
}

func TestProviderHealth(t *testing.T) {
	service, err := setupTestRoutingService()
	if err != nil {
		t.Fatalf("Failed to create routing service: %v", err)
	}
	
	ctx := context.Background()
	provider := "openai"
	
	// Test updating provider health
	err = service.UpdateProviderHealth(ctx, provider, false)
	if err != nil {
		t.Errorf("Failed to update provider health: %v", err)
	}
	
	// Test getting provider health
	healthy, err := service.GetProviderHealth(ctx, provider)
	if err != nil {
		t.Errorf("Failed to get provider health: %v", err)
	}
	
	if healthy {
		t.Error("Expected provider to be unhealthy")
	}
	
	// Test updating to healthy
	err = service.UpdateProviderHealth(ctx, provider, true)
	if err != nil {
		t.Errorf("Failed to update provider health to healthy: %v", err)
	}
	
	healthy, err = service.GetProviderHealth(ctx, provider)
	if err != nil {
		t.Errorf("Failed to get provider health: %v", err)
	}
	
	if !healthy {
		t.Error("Expected provider to be healthy")
	}
}
