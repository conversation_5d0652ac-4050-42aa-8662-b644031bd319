package utils

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// APIResponse represents a standard API response
type APIResponse struct {
	Success   bool        `json:"success"`
	Data      interface{} `json:"data,omitempty"`
	Error     *APIError   `json:"error,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
	RequestID string      `json:"request_id,omitempty"`
}

// APIError represents an API error
type APIError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// PaginatedResponse represents a paginated API response
type PaginatedResponse struct {
	Success    bool        `json:"success"`
	Data       interface{} `json:"data"`
	Pagination Pagination `json:"pagination"`
	Timestamp  time.Time   `json:"timestamp"`
	RequestID  string      `json:"request_id,omitempty"`
}

// Pagination represents pagination metadata
type Pagination struct {
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	Total      int `json:"total"`
	TotalPages int `json:"total_pages"`
	HasNext    bool `json:"has_next"`
	HasPrev    bool `json:"has_prev"`
}

// SuccessResponse sends a successful response
func SuccessResponse(c *gin.Context, data interface{}) {
	response := APIResponse{
		Success:   true,
		Data:      data,
		Timestamp: time.Now().UTC(),
		RequestID: GetRequestID(c),
	}
	
	c.JSON(http.StatusOK, response)
}

// CreatedResponse sends a created response
func CreatedResponse(c *gin.Context, data interface{}) {
	response := APIResponse{
		Success:   true,
		Data:      data,
		Timestamp: time.Now().UTC(),
		RequestID: GetRequestID(c),
	}
	
	c.JSON(http.StatusCreated, response)
}

// ErrorResponse sends an error response
func ErrorResponse(c *gin.Context, statusCode int, code, message string, details ...string) {
	apiError := &APIError{
		Code:    code,
		Message: message,
	}
	
	if len(details) > 0 {
		apiError.Details = details[0]
	}
	
	response := APIResponse{
		Success:   false,
		Error:     apiError,
		Timestamp: time.Now().UTC(),
		RequestID: GetRequestID(c),
	}
	
	c.JSON(statusCode, response)
}

// ValidationErrorResponse sends a validation error response
func ValidationErrorResponse(c *gin.Context, message string, details ...string) {
	ErrorResponse(c, http.StatusBadRequest, "VALIDATION_ERROR", message, details...)
}

// UnauthorizedResponse sends an unauthorized response
func UnauthorizedResponse(c *gin.Context, message ...string) {
	msg := "Unauthorized"
	if len(message) > 0 {
		msg = message[0]
	}
	ErrorResponse(c, http.StatusUnauthorized, "UNAUTHORIZED", msg)
}

// ForbiddenResponse sends a forbidden response
func ForbiddenResponse(c *gin.Context, message ...string) {
	msg := "Forbidden"
	if len(message) > 0 {
		msg = message[0]
	}
	ErrorResponse(c, http.StatusForbidden, "FORBIDDEN", msg)
}

// NotFoundResponse sends a not found response
func NotFoundResponse(c *gin.Context, message ...string) {
	msg := "Resource not found"
	if len(message) > 0 {
		msg = message[0]
	}
	ErrorResponse(c, http.StatusNotFound, "NOT_FOUND", msg)
}

// ConflictResponse sends a conflict response
func ConflictResponse(c *gin.Context, message string, details ...string) {
	ErrorResponse(c, http.StatusConflict, "CONFLICT", message, details...)
}

// InternalErrorResponse sends an internal server error response
func InternalErrorResponse(c *gin.Context, message ...string) {
	msg := "Internal server error"
	if len(message) > 0 {
		msg = message[0]
	}
	ErrorResponse(c, http.StatusInternalServerError, "INTERNAL_ERROR", msg)
}

// RateLimitResponse sends a rate limit exceeded response
func RateLimitResponse(c *gin.Context, message ...string) {
	msg := "Rate limit exceeded"
	if len(message) > 0 {
		msg = message[0]
	}
	ErrorResponse(c, http.StatusTooManyRequests, "RATE_LIMIT_EXCEEDED", msg)
}

// PaginatedSuccessResponse sends a paginated successful response
func PaginatedSuccessResponse(c *gin.Context, data interface{}, pagination Pagination) {
	response := PaginatedResponse{
		Success:    true,
		Data:       data,
		Pagination: pagination,
		Timestamp:  time.Now().UTC(),
		RequestID:  GetRequestID(c),
	}
	
	c.JSON(http.StatusOK, response)
}

// GetRequestID extracts request ID from context
func GetRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

// CalculatePagination calculates pagination metadata
func CalculatePagination(page, limit, total int) Pagination {
	if page < 1 {
		page = 1
	}
	if limit < 1 {
		limit = 10
	}
	
	totalPages := (total + limit - 1) / limit
	if totalPages < 1 {
		totalPages = 1
	}
	
	return Pagination{
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
}

// StreamingResponse sets up headers for streaming response
func StreamingResponse(c *gin.Context) {
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")
}

// WriteSSEEvent writes a Server-Sent Event
func WriteSSEEvent(c *gin.Context, event, data string) {
	if event != "" {
		c.Writer.WriteString("event: " + event + "\n")
	}
	c.Writer.WriteString("data: " + data + "\n\n")
	c.Writer.Flush()
}
