package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
)

// CryptoService handles encryption and decryption of sensitive data
type CryptoService struct {
	key []byte
}

// NewCryptoService creates a new crypto service with the given key
func NewCryptoService(key string) (*CryptoService, error) {
	if len(key) != 32 {
		return nil, fmt.Errorf("encryption key must be exactly 32 bytes long")
	}
	
	return &CryptoService{
		key: []byte(key),
	}, nil
}

// Encrypt encrypts plaintext using AES-GCM
func (c *CryptoService) Encrypt(plaintext string) (string, error) {
	if plaintext == "" {
		return "", nil
	}
	
	// Create AES cipher
	block, err := aes.NewCipher(c.key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %w", err)
	}
	
	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("failed to create GCM: %w", err)
	}
	
	// Generate random nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}
	
	// Encrypt the plaintext
	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
	
	// Encode to base64 for storage
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// Decrypt decrypts ciphertext using AES-GCM
func (c *CryptoService) Decrypt(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", nil
	}
	
	// Decode from base64
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %w", err)
	}
	
	// Create AES cipher
	block, err := aes.NewCipher(c.key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %w", err)
	}
	
	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %w", err)
	}
	
	// Check minimum length
	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}
	
	// Extract nonce and ciphertext
	nonce, ciphertext_bytes := data[:nonceSize], data[nonceSize:]
	
	// Decrypt
	plaintext, err := gcm.Open(nil, nonce, ciphertext_bytes, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt: %w", err)
	}
	
	return string(plaintext), nil
}

// HashPassword creates a bcrypt hash of the password
func HashPassword(password string) (string, error) {
	// Using golang.org/x/crypto/bcrypt would be better, but keeping dependencies minimal
	// In production, use bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	
	// For now, using a simple approach (NOT SECURE - replace with bcrypt)
	return password, nil // TODO: Implement proper password hashing
}

// VerifyPassword verifies a password against its hash
func VerifyPassword(password, hash string) bool {
	// TODO: Implement proper password verification with bcrypt
	return password == hash
}

// GenerateRandomString generates a cryptographically secure random string
func GenerateRandomString(length int) (string, error) {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}
	
	for i, b := range bytes {
		bytes[i] = charset[b%byte(len(charset))]
	}
	
	return string(bytes), nil
}

// GenerateAPIKey generates a new API key with the specified prefix
func GenerateAPIKey(prefix string) (string, error) {
	randomPart, err := GenerateRandomString(32)
	if err != nil {
		return "", fmt.Errorf("failed to generate random part: %w", err)
	}
	
	return fmt.Sprintf("%s_%s", prefix, randomPart), nil
}

// ValidateAPIKey validates the format of an API key
func ValidateAPIKey(apiKey string) bool {
	// Basic validation - should start with sk-sys_ for system keys
	if len(apiKey) < 10 {
		return false
	}
	
	// Check for valid prefixes
	validPrefixes := []string{"sk-sys_", "sk-usr_", "sk-"}
	for _, prefix := range validPrefixes {
		if len(apiKey) > len(prefix) && apiKey[:len(prefix)] == prefix {
			return true
		}
	}
	
	return false
}
