package webhooks

import (
	"context"
	"testing"
	"time"

	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/database"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestWebhookService() (*Service, error) {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	
	// Run migrations
	err = db.AutoMigrate(
		&database.User{},
		&database.UserAPIKey{},
		&database.AdminGlobalRoutingRule{},
		&database.UsageLog{},
		&database.WebhookConfig{},
	)
	if err != nil {
		return nil, err
	}
	
	// Setup test config
	cfg := &config.Config{
		Environment: "test",
		EncryptionKey: "12345678901234567890123456789012", // 32 chars
	}
	
	// Create webhook service
	return NewService(db, cfg), nil
}

func setupTestWebhookData(db *gorm.DB) (*database.User, error) {
	// Create test user
	user := &database.User{
		Email:        "<EMAIL>",
		PasswordHash: "hashed_password",
		SystemAPIKey: "sk-sys_test_key",
		Role:         database.RoleUser,
		IsActive:     true,
	}
	
	if err := db.Create(user).Error; err != nil {
		return nil, err
	}
	
	return user, nil
}

func TestNewService(t *testing.T) {
	service, err := setupTestWebhookService()
	if err != nil {
		t.Fatalf("Failed to create webhook service: %v", err)
	}
	
	if service == nil {
		t.Fatal("Service is nil")
	}
	
	if service.webhookRepo == nil {
		t.Error("Webhook repository is nil")
	}
	
	if service.httpClient == nil {
		t.Error("HTTP client is nil")
	}
}

func TestGetSupportedEvents(t *testing.T) {
	service, err := setupTestWebhookService()
	if err != nil {
		t.Fatalf("Failed to create webhook service: %v", err)
	}
	
	events := service.GetSupportedEvents()
	
	if len(events) == 0 {
		t.Error("No supported events returned")
	}
	
	expectedEvents := []string{
		EventUserRegistered,
		EventUserLogin,
		EventAPIKeyCreated,
		EventAPIKeyDeleted,
		EventAPIKeyInvalid,
		EventLLMRequestSuccess,
		EventLLMRequestFailure,
		EventSystemAlert,
		EventQuotaExceeded,
	}
	
	if len(events) != len(expectedEvents) {
		t.Errorf("Expected %d events, got %d", len(expectedEvents), len(events))
	}
	
	for _, expected := range expectedEvents {
		found := false
		for _, event := range events {
			if event == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected event %s not found", expected)
		}
	}
}

func TestCreateWebhook(t *testing.T) {
	service, err := setupTestWebhookService()
	if err != nil {
		t.Fatalf("Failed to create webhook service: %v", err)
	}
	
	user, err := setupTestWebhookData(service.db)
	if err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Test successful webhook creation
	req := &CreateWebhookRequest{
		Name:        "Test Webhook",
		URL:         "https://example.com/webhook",
		Events:      []string{EventUserRegistered, EventAPIKeyCreated},
		IsActive:    true,
		Secret:      "test-secret",
		Description: "Test webhook description",
	}
	
	webhookResponse, err := service.CreateWebhook(ctx, user.ID, req)
	if err != nil {
		t.Errorf("Failed to create webhook: %v", err)
	}
	
	if webhookResponse == nil {
		t.Fatal("Webhook response is nil")
	}
	
	if webhookResponse.Name != req.Name {
		t.Errorf("Expected name %s, got %s", req.Name, webhookResponse.Name)
	}
	
	if webhookResponse.URL != req.URL {
		t.Errorf("Expected URL %s, got %s", req.URL, webhookResponse.URL)
	}
	
	if len(webhookResponse.Events) != len(req.Events) {
		t.Errorf("Expected %d events, got %d", len(req.Events), len(webhookResponse.Events))
	}
	
	// Test invalid event
	invalidReq := &CreateWebhookRequest{
		Name:   "Invalid Webhook",
		URL:    "https://example.com/webhook",
		Events: []string{"invalid.event"},
	}
	
	_, err = service.CreateWebhook(ctx, user.ID, invalidReq)
	if err == nil {
		t.Error("Expected error for invalid event")
	}
	
	// Test invalid URL
	invalidURLReq := &CreateWebhookRequest{
		Name:   "Invalid URL Webhook",
		URL:    "invalid-url",
		Events: []string{EventUserRegistered},
	}
	
	_, err = service.CreateWebhook(ctx, user.ID, invalidURLReq)
	if err == nil {
		t.Error("Expected error for invalid URL")
	}
}

func TestGetWebhooks(t *testing.T) {
	service, err := setupTestWebhookService()
	if err != nil {
		t.Fatalf("Failed to create webhook service: %v", err)
	}
	
	user, err := setupTestWebhookData(service.db)
	if err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Create test webhooks
	webhooks := []*CreateWebhookRequest{
		{
			Name:   "Webhook 1",
			URL:    "https://example.com/webhook1",
			Events: []string{EventUserRegistered},
		},
		{
			Name:   "Webhook 2",
			URL:    "https://example.com/webhook2",
			Events: []string{EventAPIKeyCreated, EventAPIKeyDeleted},
		},
	}
	
	for _, webhookReq := range webhooks {
		_, err := service.CreateWebhook(ctx, user.ID, webhookReq)
		if err != nil {
			t.Fatalf("Failed to create test webhook: %v", err)
		}
	}
	
	// Test getting all webhooks
	webhookResponses, err := service.GetWebhooks(ctx, user.ID)
	if err != nil {
		t.Errorf("Failed to get webhooks: %v", err)
	}
	
	if len(webhookResponses) != len(webhooks) {
		t.Errorf("Expected %d webhooks, got %d", len(webhooks), len(webhookResponses))
	}
}

func TestUpdateWebhook(t *testing.T) {
	service, err := setupTestWebhookService()
	if err != nil {
		t.Fatalf("Failed to create webhook service: %v", err)
	}
	
	user, err := setupTestWebhookData(service.db)
	if err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Create a test webhook
	createReq := &CreateWebhookRequest{
		Name:   "Original Webhook",
		URL:    "https://example.com/original",
		Events: []string{EventUserRegistered},
	}
	
	webhookResponse, err := service.CreateWebhook(ctx, user.ID, createReq)
	if err != nil {
		t.Fatalf("Failed to create test webhook: %v", err)
	}
	
	// Test updating name
	updateReq := &UpdateWebhookRequest{
		Name: "Updated Webhook",
	}
	
	updatedWebhook, err := service.UpdateWebhook(ctx, user.ID, webhookResponse.ID, updateReq)
	if err != nil {
		t.Errorf("Failed to update webhook: %v", err)
	}
	
	if updatedWebhook.Name != updateReq.Name {
		t.Errorf("Expected name %s, got %s", updateReq.Name, updatedWebhook.Name)
	}
	
	// Test updating URL
	updateReq = &UpdateWebhookRequest{
		URL: "https://example.com/updated",
	}
	
	updatedWebhook, err = service.UpdateWebhook(ctx, user.ID, webhookResponse.ID, updateReq)
	if err != nil {
		t.Errorf("Failed to update webhook URL: %v", err)
	}
	
	if updatedWebhook.URL != updateReq.URL {
		t.Errorf("Expected URL %s, got %s", updateReq.URL, updatedWebhook.URL)
	}
	
	// Test updating events
	updateReq = &UpdateWebhookRequest{
		Events: []string{EventAPIKeyCreated, EventAPIKeyDeleted},
	}
	
	updatedWebhook, err = service.UpdateWebhook(ctx, user.ID, webhookResponse.ID, updateReq)
	if err != nil {
		t.Errorf("Failed to update webhook events: %v", err)
	}
	
	if len(updatedWebhook.Events) != len(updateReq.Events) {
		t.Errorf("Expected %d events, got %d", len(updateReq.Events), len(updatedWebhook.Events))
	}
}

func TestDeleteWebhook(t *testing.T) {
	service, err := setupTestWebhookService()
	if err != nil {
		t.Fatalf("Failed to create webhook service: %v", err)
	}
	
	user, err := setupTestWebhookData(service.db)
	if err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Create a test webhook
	createReq := &CreateWebhookRequest{
		Name:   "Test Webhook",
		URL:    "https://example.com/webhook",
		Events: []string{EventUserRegistered},
	}
	
	webhookResponse, err := service.CreateWebhook(ctx, user.ID, createReq)
	if err != nil {
		t.Fatalf("Failed to create test webhook: %v", err)
	}
	
	// Delete the webhook
	err = service.DeleteWebhook(ctx, user.ID, webhookResponse.ID)
	if err != nil {
		t.Errorf("Failed to delete webhook: %v", err)
	}
	
	// Try to get the deleted webhook
	_, err = service.GetWebhook(ctx, user.ID, webhookResponse.ID)
	if err == nil {
		t.Error("Expected error when getting deleted webhook")
	}
}

func TestTriggerEvent(t *testing.T) {
	service, err := setupTestWebhookService()
	if err != nil {
		t.Fatalf("Failed to create webhook service: %v", err)
	}
	
	user, err := setupTestWebhookData(service.db)
	if err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Create a test webhook
	createReq := &CreateWebhookRequest{
		Name:     "Test Webhook",
		URL:      "https://httpbin.org/post", // This will fail but that's ok for testing
		Events:   []string{EventUserRegistered},
		IsActive: true,
	}
	
	_, err = service.CreateWebhook(ctx, user.ID, createReq)
	if err != nil {
		t.Fatalf("Failed to create test webhook: %v", err)
	}
	
	// Create test event
	event := &WebhookEvent{
		Type:      EventUserRegistered,
		Timestamp: time.Now().UTC(),
		UserID:    user.ID,
		Data: map[string]interface{}{
			"user_email": user.Email,
		},
	}
	
	// Trigger the event (this will attempt to send HTTP request)
	err = service.TriggerEvent(ctx, event)
	if err != nil {
		// This is expected to fail since we're using a test URL
		t.Logf("Event trigger failed as expected: %v", err)
	}
}

func TestUtilityFunctions(t *testing.T) {
	// Test contains function
	slice := []string{"a", "b", "c"}
	if !contains(slice, "b") {
		t.Error("Expected contains to return true for 'b'")
	}
	
	if contains(slice, "d") {
		t.Error("Expected contains to return false for 'd'")
	}
	
	// Test isValidURL function
	validURLs := []string{
		"http://example.com",
		"https://example.com",
		"https://example.com/webhook",
	}
	
	for _, url := range validURLs {
		if !isValidURL(url) {
			t.Errorf("Expected URL %s to be valid", url)
		}
	}
	
	invalidURLs := []string{
		"",
		"ftp://example.com",
		"example.com",
		"http",
	}
	
	for _, url := range invalidURLs {
		if isValidURL(url) {
			t.Errorf("Expected URL %s to be invalid", url)
		}
	}
}
