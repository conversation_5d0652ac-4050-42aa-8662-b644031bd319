package webhooks

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/database"
	"llm-proxy-system/internal/logger"

	"gorm.io/gorm"
)

// Service handles webhook operations
type Service struct {
	db         *gorm.DB
	config     *config.Config
	webhookRepo *database.WebhookConfigRepository
	httpClient *http.Client
}

// NewService creates a new webhook service
func NewService(db *gorm.DB, cfg *config.Config) *Service {
	return &Service{
		db:          db,
		config:      cfg,
		webhookRepo: database.NewWebhookConfigRepository(db),
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// WebhookEvent represents an event that can trigger webhooks
type WebhookEvent struct {
	Type      string                 `json:"type"`
	Timestamp time.Time              `json:"timestamp"`
	UserID    uint                   `json:"user_id,omitempty"`
	Data      map[string]interface{} `json:"data"`
}

// CreateWebhookRequest represents a request to create a webhook
type CreateWebhookRequest struct {
	Name        string   `json:"name" binding:"required"`
	URL         string   `json:"url" binding:"required"`
	Events      []string `json:"events" binding:"required"`
	IsActive    bool     `json:"is_active"`
	Secret      string   `json:"secret,omitempty"`
	Description string   `json:"description,omitempty"`
}

// UpdateWebhookRequest represents a request to update a webhook
type UpdateWebhookRequest struct {
	Name        string   `json:"name,omitempty"`
	URL         string   `json:"url,omitempty"`
	Events      []string `json:"events,omitempty"`
	IsActive    *bool    `json:"is_active,omitempty"`
	Secret      string   `json:"secret,omitempty"`
	Description string   `json:"description,omitempty"`
}

// WebhookResponse represents a webhook in responses
type WebhookResponse struct {
	ID               uint      `json:"id"`
	UserID           uint      `json:"user_id"`
	Name             string    `json:"name"`
	URL              string    `json:"url"`
	Events           []string  `json:"events"`
	IsActive         bool      `json:"is_active"`
	Description      string    `json:"description"`
	LastTriggeredAt  *time.Time `json:"last_triggered_at"`
	SuccessCount     int64     `json:"success_count"`
	FailureCount     int64     `json:"failure_count"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// Supported webhook events
const (
	EventUserRegistered    = "user.registered"
	EventUserLogin         = "user.login"
	EventAPIKeyCreated     = "api_key.created"
	EventAPIKeyDeleted     = "api_key.deleted"
	EventAPIKeyInvalid     = "api_key.invalid"
	EventLLMRequestSuccess = "llm_request.success"
	EventLLMRequestFailure = "llm_request.failure"
	EventSystemAlert       = "system.alert"
	EventQuotaExceeded     = "quota.exceeded"
)

// GetSupportedEvents returns all supported webhook events
func (s *Service) GetSupportedEvents() []string {
	return []string{
		EventUserRegistered,
		EventUserLogin,
		EventAPIKeyCreated,
		EventAPIKeyDeleted,
		EventAPIKeyInvalid,
		EventLLMRequestSuccess,
		EventLLMRequestFailure,
		EventSystemAlert,
		EventQuotaExceeded,
	}
}

// CreateWebhook creates a new webhook for a user
func (s *Service) CreateWebhook(ctx context.Context, userID uint, req *CreateWebhookRequest) (*WebhookResponse, error) {
	logger.Info("Creating webhook", "user_id", userID, "name", req.Name, "url", req.URL)

	// Validate events
	supportedEvents := s.GetSupportedEvents()
	for _, event := range req.Events {
		if !contains(supportedEvents, event) {
			return nil, fmt.Errorf("unsupported event: %s", event)
		}
	}

	// Validate URL format
	if !isValidURL(req.URL) {
		return nil, fmt.Errorf("invalid URL format")
	}

	// Convert events to JSON
	eventsJSON, err := json.Marshal(req.Events)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal events: %w", err)
	}

	// Create webhook record
	webhook := &database.WebhookConfig{
		UserID:      userID,
		Name:        req.Name,
		URL:         req.URL,
		Events:      string(eventsJSON),
		IsActive:    req.IsActive,
		Secret:      req.Secret,
		Description: req.Description,
	}

	if err := s.webhookRepo.Create(webhook); err != nil {
		return nil, fmt.Errorf("failed to create webhook: %w", err)
	}

	logger.Info("Webhook created successfully", "webhook_id", webhook.ID, "user_id", userID)
	return s.webhookToResponse(webhook), nil
}

// GetWebhooks returns all webhooks for a user
func (s *Service) GetWebhooks(ctx context.Context, userID uint) ([]WebhookResponse, error) {
	webhooks, err := s.webhookRepo.GetByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get webhooks: %w", err)
	}

	responses := make([]WebhookResponse, len(webhooks))
	for i, webhook := range webhooks {
		responses[i] = *s.webhookToResponse(&webhook)
	}

	logger.Info("Retrieved webhooks", "user_id", userID, "count", len(responses))
	return responses, nil
}

// GetWebhook returns a specific webhook for a user
func (s *Service) GetWebhook(ctx context.Context, userID, webhookID uint) (*WebhookResponse, error) {
	webhook, err := s.webhookRepo.GetByID(webhookID)
	if err != nil {
		return nil, fmt.Errorf("failed to get webhook: %w", err)
	}

	// Verify ownership
	if webhook.UserID != userID {
		return nil, fmt.Errorf("webhook not found")
	}

	return s.webhookToResponse(webhook), nil
}

// UpdateWebhook updates an existing webhook
func (s *Service) UpdateWebhook(ctx context.Context, userID, webhookID uint, req *UpdateWebhookRequest) (*WebhookResponse, error) {
	logger.Info("Updating webhook", "user_id", userID, "webhook_id", webhookID)

	webhook, err := s.webhookRepo.GetByID(webhookID)
	if err != nil {
		return nil, fmt.Errorf("failed to get webhook: %w", err)
	}

	// Verify ownership
	if webhook.UserID != userID {
		return nil, fmt.Errorf("webhook not found")
	}

	// Update fields
	updated := false
	if req.Name != "" && req.Name != webhook.Name {
		webhook.Name = req.Name
		updated = true
	}

	if req.URL != "" && req.URL != webhook.URL {
		if !isValidURL(req.URL) {
			return nil, fmt.Errorf("invalid URL format")
		}
		webhook.URL = req.URL
		updated = true
	}

	if len(req.Events) > 0 {
		// Validate events
		supportedEvents := s.GetSupportedEvents()
		for _, event := range req.Events {
			if !contains(supportedEvents, event) {
				return nil, fmt.Errorf("unsupported event: %s", event)
			}
		}

		eventsJSON, err := json.Marshal(req.Events)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal events: %w", err)
		}
		webhook.Events = string(eventsJSON)
		updated = true
	}

	if req.IsActive != nil && *req.IsActive != webhook.IsActive {
		webhook.IsActive = *req.IsActive
		updated = true
	}

	if req.Secret != webhook.Secret {
		webhook.Secret = req.Secret
		updated = true
	}

	if req.Description != webhook.Description {
		webhook.Description = req.Description
		updated = true
	}

	if updated {
		if err := s.webhookRepo.Update(webhook); err != nil {
			return nil, fmt.Errorf("failed to update webhook: %w", err)
		}
	}

	logger.Info("Webhook updated successfully", "webhook_id", webhookID, "user_id", userID)
	return s.webhookToResponse(webhook), nil
}

// DeleteWebhook deletes a webhook
func (s *Service) DeleteWebhook(ctx context.Context, userID, webhookID uint) error {
	logger.Info("Deleting webhook", "user_id", userID, "webhook_id", webhookID)

	webhook, err := s.webhookRepo.GetByID(webhookID)
	if err != nil {
		return fmt.Errorf("failed to get webhook: %w", err)
	}

	// Verify ownership
	if webhook.UserID != userID {
		return fmt.Errorf("webhook not found")
	}

	if err := s.webhookRepo.Delete(webhookID); err != nil {
		return fmt.Errorf("failed to delete webhook: %w", err)
	}

	logger.Info("Webhook deleted successfully", "webhook_id", webhookID, "user_id", userID)
	return nil
}

// TriggerEvent triggers webhooks for a specific event
func (s *Service) TriggerEvent(ctx context.Context, event *WebhookEvent) error {
	logger.Info("Triggering webhook event", "type", event.Type, "user_id", event.UserID)

	// Get all active webhooks that listen for this event
	webhooks, err := s.getWebhooksForEvent(event.Type, event.UserID)
	if err != nil {
		logger.Error("Failed to get webhooks for event", "error", err, "event_type", event.Type)
		return err
	}

	if len(webhooks) == 0 {
		logger.Debug("No webhooks found for event", "event_type", event.Type, "user_id", event.UserID)
		return nil
	}

	// Trigger webhooks asynchronously
	for _, webhook := range webhooks {
		go s.deliverWebhook(ctx, &webhook, event)
	}

	logger.Info("Webhook event triggered", "type", event.Type, "webhook_count", len(webhooks))
	return nil
}

// TestWebhook tests a webhook by sending a test event
func (s *Service) TestWebhook(ctx context.Context, userID, webhookID uint) error {
	logger.Info("Testing webhook", "user_id", userID, "webhook_id", webhookID)

	webhook, err := s.webhookRepo.GetByID(webhookID)
	if err != nil {
		return fmt.Errorf("failed to get webhook: %w", err)
	}

	// Verify ownership
	if webhook.UserID != userID {
		return fmt.Errorf("webhook not found")
	}

	// Create test event
	testEvent := &WebhookEvent{
		Type:      "webhook.test",
		Timestamp: time.Now().UTC(),
		UserID:    userID,
		Data: map[string]interface{}{
			"message":    "This is a test webhook event",
			"webhook_id": webhookID,
		},
	}

	// Deliver test webhook
	return s.deliverWebhook(ctx, webhook, testEvent)
}

// Helper methods

func (s *Service) getWebhooksForEvent(eventType string, userID uint) ([]database.WebhookConfig, error) {
	// Get all active webhooks for the user
	webhooks, err := s.webhookRepo.GetActiveByUserID(userID)
	if err != nil {
		return nil, err
	}

	// Filter webhooks that listen for this event
	var matchingWebhooks []database.WebhookConfig
	for _, webhook := range webhooks {
		var events []string
		if err := json.Unmarshal([]byte(webhook.Events), &events); err != nil {
			logger.Error("Failed to unmarshal webhook events", "error", err, "webhook_id", webhook.ID)
			continue
		}

		if contains(events, eventType) {
			matchingWebhooks = append(matchingWebhooks, webhook)
		}
	}

	return matchingWebhooks, nil
}

func (s *Service) deliverWebhook(ctx context.Context, webhook *database.WebhookConfig, event *WebhookEvent) error {
	logger.Info("Delivering webhook", "webhook_id", webhook.ID, "url", webhook.URL, "event_type", event.Type)

	// Prepare payload
	payload := map[string]interface{}{
		"event":     event,
		"webhook":   map[string]interface{}{
			"id":   webhook.ID,
			"name": webhook.Name,
		},
		"timestamp": time.Now().UTC(),
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		logger.Error("Failed to marshal webhook payload", "error", err, "webhook_id", webhook.ID)
		s.updateWebhookStats(webhook.ID, false)
		return err
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", webhook.URL, bytes.NewBuffer(payloadBytes))
	if err != nil {
		logger.Error("Failed to create webhook request", "error", err, "webhook_id", webhook.ID)
		s.updateWebhookStats(webhook.ID, false)
		return err
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "LLM-Proxy-Webhook/1.0")
	
	// Add signature if secret is provided
	if webhook.Secret != "" {
		signature := generateSignature(payloadBytes, webhook.Secret)
		req.Header.Set("X-Webhook-Signature", signature)
	}

	// Send request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		logger.Error("Failed to send webhook", "error", err, "webhook_id", webhook.ID, "url", webhook.URL)
		s.updateWebhookStats(webhook.ID, false)
		return err
	}
	defer resp.Body.Close()

	// Check response status
	success := resp.StatusCode >= 200 && resp.StatusCode < 300
	if success {
		logger.Info("Webhook delivered successfully", "webhook_id", webhook.ID, "status_code", resp.StatusCode)
	} else {
		logger.Warn("Webhook delivery failed", "webhook_id", webhook.ID, "status_code", resp.StatusCode)
	}

	// Update webhook statistics
	s.updateWebhookStats(webhook.ID, success)
	
	// Update last triggered timestamp
	s.webhookRepo.UpdateLastTriggered(webhook.ID, time.Now().UTC())

	return nil
}

func (s *Service) updateWebhookStats(webhookID uint, success bool) {
	if success {
		s.webhookRepo.IncrementSuccessCount(webhookID)
	} else {
		s.webhookRepo.IncrementFailureCount(webhookID)
	}
}

func (s *Service) webhookToResponse(webhook *database.WebhookConfig) *WebhookResponse {
	var events []string
	json.Unmarshal([]byte(webhook.Events), &events)

	return &WebhookResponse{
		ID:              webhook.ID,
		UserID:          webhook.UserID,
		Name:            webhook.Name,
		URL:             webhook.URL,
		Events:          events,
		IsActive:        webhook.IsActive,
		Description:     webhook.Description,
		LastTriggeredAt: webhook.LastTriggeredAt,
		SuccessCount:    webhook.SuccessCount,
		FailureCount:    webhook.FailureCount,
		CreatedAt:       webhook.CreatedAt,
		UpdatedAt:       webhook.UpdatedAt,
	}
}

// Utility functions
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func isValidURL(url string) bool {
	// Basic URL validation
	return len(url) > 0 && (len(url) >= 7) && (url[:7] == "http://" || url[:8] == "https://")
}

func generateSignature(payload []byte, secret string) string {
	// Simple signature generation (in production, use HMAC-SHA256)
	return fmt.Sprintf("sha256=%x", payload[:min(len(payload), 32)])
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
