package admin

import (
	"context"
	"testing"

	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/database"

	"github.com/redis/go-redis/v9"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestAdminService() (*Service, error) {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	
	// Run migrations
	err = db.AutoMigrate(
		&database.User{},
		&database.UserAPIKey{},
		&database.AdminGlobalRoutingRule{},
		&database.UsageLog{},
		&database.WebhookConfig{},
	)
	if err != nil {
		return nil, err
	}
	
	// Setup test Redis (mock)
	redisClient := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   1, // Use test database
	})
	
	// Setup test config
	cfg := &config.Config{
		Environment: "test",
		EncryptionKey: "12345678901234567890123456789012", // 32 chars
	}
	
	// Create admin service
	return NewService(db, redisClient, cfg), nil
}

func setupTestAdminData(db *gorm.DB) error {
	// Create test users
	users := []*database.User{
		{
			Email:        "<EMAIL>",
			PasswordHash: "hashed_password1",
			SystemAPIKey: "sk-sys_user1_key",
			Role:         database.RoleUser,
			IsActive:     true,
		},
		{
			Email:        "<EMAIL>",
			PasswordHash: "hashed_password2",
			SystemAPIKey: "sk-sys_user2_key",
			Role:         database.RoleUser,
			IsActive:     false,
		},
		{
			Email:        "<EMAIL>",
			PasswordHash: "hashed_password_admin",
			SystemAPIKey: "sk-sys_admin_key",
			Role:         database.RoleAdmin,
			IsActive:     true,
		},
	}
	
	for _, user := range users {
		if err := db.Create(user).Error; err != nil {
			return err
		}
	}
	
	// Create test API keys
	apiKeys := []*database.UserAPIKey{
		{
			UserID:          1,
			Provider:        "openai",
			Name:            "User1 OpenAI Key",
			EncryptedAPIKey: "encrypted_key_1",
			Status:          database.StatusActive,
		},
		{
			UserID:          1,
			Provider:        "gemini",
			Name:            "User1 Gemini Key",
			EncryptedAPIKey: "encrypted_key_2",
			Status:          database.StatusInvalid,
		},
		{
			UserID:          2,
			Provider:        "openai",
			Name:            "User2 OpenAI Key",
			EncryptedAPIKey: "encrypted_key_3",
			Status:          database.StatusTesting,
		},
	}
	
	for _, key := range apiKeys {
		if err := db.Create(key).Error; err != nil {
			return err
		}
	}
	
	// Create test usage logs
	usageLogs := []*database.UsageLog{
		{
			UserID:           1,
			UserAPIKeyID:     &[]uint{1}[0],
			Provider:         "openai",
			Model:            "gpt-4",
			RequestID:        "req_1",
			Method:           "chat.completion",
			PromptTokens:     50,
			CompletionTokens: 100,
			TotalTokens:      150,
			Duration:         1200,
			Status:           "success",
			IPAddress:        "***********",
			UserAgent:        "test-agent",
		},
		{
			UserID:           1,
			UserAPIKeyID:     &[]uint{1}[0],
			Provider:         "openai",
			Model:            "gpt-3.5-turbo",
			RequestID:        "req_2",
			Method:           "chat.completion",
			PromptTokens:     30,
			CompletionTokens: 80,
			TotalTokens:      110,
			Duration:         800,
			Status:           "success",
			IPAddress:        "***********",
			UserAgent:        "test-agent",
		},
		{
			UserID:           2,
			UserAPIKeyID:     &[]uint{3}[0],
			Provider:         "gemini",
			Model:            "gemini-1.5-pro",
			RequestID:        "req_3",
			Method:           "chat.completion",
			PromptTokens:     40,
			CompletionTokens: 0,
			TotalTokens:      40,
			Duration:         500,
			Status:           "error",
			ErrorMessage:     "API key invalid",
			IPAddress:        "***********",
			UserAgent:        "test-agent",
		},
	}
	
	for _, log := range usageLogs {
		if err := db.Create(log).Error; err != nil {
			return err
		}
	}
	
	return nil
}

func TestNewService(t *testing.T) {
	service, err := setupTestAdminService()
	if err != nil {
		t.Fatalf("Failed to create admin service: %v", err)
	}
	
	if service == nil {
		t.Fatal("Service is nil")
	}
	
	if service.userRepo == nil {
		t.Error("User repository is nil")
	}
	
	if service.keyRepo == nil {
		t.Error("Key repository is nil")
	}
	
	if service.usageRepo == nil {
		t.Error("Usage repository is nil")
	}
}

func TestGetSystemStats(t *testing.T) {
	service, err := setupTestAdminService()
	if err != nil {
		t.Fatalf("Failed to create admin service: %v", err)
	}
	
	if err := setupTestAdminData(service.db); err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	stats, err := service.GetSystemStats(ctx)
	if err != nil {
		t.Errorf("Failed to get system stats: %v", err)
	}
	
	if stats == nil {
		t.Fatal("Stats is nil")
	}
	
	// Check user stats
	if stats.Users.Total != 3 {
		t.Errorf("Expected 3 total users, got %d", stats.Users.Total)
	}
	
	if stats.Users.Active != 2 {
		t.Errorf("Expected 2 active users, got %d", stats.Users.Active)
	}
	
	if stats.Users.Inactive != 1 {
		t.Errorf("Expected 1 inactive user, got %d", stats.Users.Inactive)
	}
	
	// Check API key stats
	if stats.APIKeys.Total != 3 {
		t.Errorf("Expected 3 total API keys, got %d", stats.APIKeys.Total)
	}
	
	if stats.APIKeys.Active != 1 {
		t.Errorf("Expected 1 active API key, got %d", stats.APIKeys.Active)
	}
	
	if stats.APIKeys.Invalid != 1 {
		t.Errorf("Expected 1 invalid API key, got %d", stats.APIKeys.Invalid)
	}
	
	if stats.APIKeys.Testing != 1 {
		t.Errorf("Expected 1 testing API key, got %d", stats.APIKeys.Testing)
	}
	
	// Check usage stats
	if stats.Usage.TotalRequests != 3 {
		t.Errorf("Expected 3 total requests, got %d", stats.Usage.TotalRequests)
	}
	
	if stats.Usage.SuccessfulReqs != 2 {
		t.Errorf("Expected 2 successful requests, got %d", stats.Usage.SuccessfulReqs)
	}
	
	if stats.Usage.FailedReqs != 1 {
		t.Errorf("Expected 1 failed request, got %d", stats.Usage.FailedReqs)
	}
	
	if stats.Usage.TotalTokens != 300 { // 150 + 110 + 40
		t.Errorf("Expected 300 total tokens, got %d", stats.Usage.TotalTokens)
	}
}

func TestGetUsers(t *testing.T) {
	service, err := setupTestAdminService()
	if err != nil {
		t.Fatalf("Failed to create admin service: %v", err)
	}
	
	if err := setupTestAdminData(service.db); err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Test getting all users
	users, total, err := service.GetUsers(ctx, 1, 10)
	if err != nil {
		t.Errorf("Failed to get users: %v", err)
	}
	
	if len(users) != 3 {
		t.Errorf("Expected 3 users, got %d", len(users))
	}
	
	if total != 3 {
		t.Errorf("Expected total 3, got %d", total)
	}
	
	// Check user details
	for _, user := range users {
		if user.Email == "" {
			t.Error("User email is empty")
		}
		if user.SystemAPIKey == "" {
			t.Error("User system API key is empty")
		}
		if user.Role != database.RoleUser && user.Role != database.RoleAdmin {
			t.Errorf("Invalid user role: %s", user.Role)
		}
	}
	
	// Test pagination
	users, total, err = service.GetUsers(ctx, 1, 2)
	if err != nil {
		t.Errorf("Failed to get users with pagination: %v", err)
	}
	
	if len(users) != 2 {
		t.Errorf("Expected 2 users with limit 2, got %d", len(users))
	}
	
	if total != 3 {
		t.Errorf("Expected total 3 with pagination, got %d", total)
	}
}

func TestGetUser(t *testing.T) {
	service, err := setupTestAdminService()
	if err != nil {
		t.Fatalf("Failed to create admin service: %v", err)
	}
	
	if err := setupTestAdminData(service.db); err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Test getting existing user
	user, err := service.GetUser(ctx, 1)
	if err != nil {
		t.Errorf("Failed to get user: %v", err)
	}
	
	if user == nil {
		t.Fatal("User is nil")
	}
	
	if user.Email != "<EMAIL>" {
		t.Errorf("<NAME_EMAIL>, got %s", user.Email)
	}
	
	if user.APIKeyCount != 2 {
		t.Errorf("Expected 2 API keys, got %d", user.APIKeyCount)
	}
	
	if user.TotalRequests != 2 {
		t.Errorf("Expected 2 total requests, got %d", user.TotalRequests)
	}
	
	// Test getting non-existent user
	_, err = service.GetUser(ctx, 999)
	if err == nil {
		t.Error("Expected error for non-existent user")
	}
}

func TestUpdateUser(t *testing.T) {
	service, err := setupTestAdminService()
	if err != nil {
		t.Fatalf("Failed to create admin service: %v", err)
	}
	
	if err := setupTestAdminData(service.db); err != nil {
		t.Fatalf("Failed to setup test data: %v", err)
	}
	
	ctx := context.Background()
	
	// Test updating user role
	req := &UpdateUserRequest{
		Role: database.RoleAdmin,
	}
	
	user, err := service.UpdateUser(ctx, 1, req)
	if err != nil {
		t.Errorf("Failed to update user: %v", err)
	}
	
	if user.Role != database.RoleAdmin {
		t.Errorf("Expected role admin, got %s", user.Role)
	}
	
	// Test updating user status
	isActive := false
	req = &UpdateUserRequest{
		IsActive: &isActive,
	}
	
	user, err = service.UpdateUser(ctx, 1, req)
	if err != nil {
		t.Errorf("Failed to update user status: %v", err)
	}
	
	if user.IsActive {
		t.Error("Expected user to be inactive")
	}
	
	// Test invalid role
	req = &UpdateUserRequest{
		Role: "invalid-role",
	}
	
	_, err = service.UpdateUser(ctx, 1, req)
	if err == nil {
		t.Error("Expected error for invalid role")
	}
}

func TestGetSystemHealth(t *testing.T) {
	service, err := setupTestAdminService()
	if err != nil {
		t.Fatalf("Failed to create admin service: %v", err)
	}
	
	ctx := context.Background()
	
	health, err := service.GetSystemHealth(ctx)
	if err != nil {
		t.Errorf("Failed to get system health: %v", err)
	}
	
	if health == nil {
		t.Fatal("Health is nil")
	}
	
	status, ok := health["status"].(string)
	if !ok {
		t.Error("Health status is not a string")
	}
	
	if status != "healthy" && status != "unhealthy" {
		t.Errorf("Invalid health status: %s", status)
	}
	
	checks, ok := health["checks"].(map[string]interface{})
	if !ok {
		t.Error("Health checks is not a map")
	}
	
	// Check that database and redis checks exist
	if _, exists := checks["database"]; !exists {
		t.Error("Database health check missing")
	}
	
	if _, exists := checks["redis"]; !exists {
		t.Error("Redis health check missing")
	}
}
