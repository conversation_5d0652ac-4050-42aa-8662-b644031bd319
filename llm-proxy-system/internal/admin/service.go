package admin

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/database"
	"llm-proxy-system/internal/logger"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// Service handles admin operations
type Service struct {
	db         *gorm.DB
	redis      *redis.Client
	config     *config.Config
	userRepo   *database.UserRepository
	keyRepo    *database.UserAPIKeyRepository
	usageRepo  *database.UsageLogRepository
	ruleRepo   *database.AdminGlobalRoutingRuleRepository
	webhookRepo *database.WebhookConfigRepository
}

// NewService creates a new admin service
func NewService(db *gorm.DB, redis *redis.Client, cfg *config.Config) *Service {
	return &Service{
		db:          db,
		redis:       redis,
		config:      cfg,
		userRepo:    database.NewUserRepository(db),
		keyRepo:     database.NewUserAPIKeyRepository(db),
		usageRepo:   database.NewUsageLogRepository(db),
		ruleRepo:    database.NewAdminGlobalRoutingRuleRepository(db),
		webhookRepo: database.NewWebhookConfigRepository(db),
	}
}

// SystemStats represents system-wide statistics
type SystemStats struct {
	Users struct {
		Total    int64 `json:"total"`
		Active   int64 `json:"active"`
		Inactive int64 `json:"inactive"`
		NewToday int64 `json:"new_today"`
	} `json:"users"`
	
	APIKeys struct {
		Total   int64 `json:"total"`
		Active  int64 `json:"active"`
		Invalid int64 `json:"invalid"`
		Testing int64 `json:"testing"`
	} `json:"api_keys"`
	
	Usage struct {
		TotalRequests    int64 `json:"total_requests"`
		SuccessfulReqs   int64 `json:"successful_requests"`
		FailedReqs       int64 `json:"failed_requests"`
		TotalTokens      int64 `json:"total_tokens"`
		RequestsToday    int64 `json:"requests_today"`
		TokensToday      int64 `json:"tokens_today"`
	} `json:"usage"`
	
	Providers map[string]struct {
		Requests     int64   `json:"requests"`
		SuccessRate  float64 `json:"success_rate"`
		AvgTokens    float64 `json:"avg_tokens"`
		LastUsed     *time.Time `json:"last_used"`
	} `json:"providers"`
	
	System struct {
		Uptime        time.Duration `json:"uptime"`
		Version       string        `json:"version"`
		Environment   string        `json:"environment"`
		DatabaseSize  int64         `json:"database_size_mb"`
		CacheHitRate  float64       `json:"cache_hit_rate"`
	} `json:"system"`
}

// UserManagement represents user management operations
type UserManagement struct {
	ID           uint      `json:"id"`
	Email        string    `json:"email"`
	Role         string    `json:"role"`
	IsActive     bool      `json:"is_active"`
	SystemAPIKey string    `json:"system_api_key"`
	APIKeyCount  int       `json:"api_key_count"`
	TotalRequests int64    `json:"total_requests"`
	TotalTokens   int64    `json:"total_tokens"`
	LastLoginAt   *time.Time `json:"last_login_at"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// UpdateUserRequest represents a request to update a user
type UpdateUserRequest struct {
	Role     string `json:"role,omitempty"`
	IsActive *bool  `json:"is_active,omitempty"`
}

// GetSystemStats returns comprehensive system statistics
func (s *Service) GetSystemStats(ctx context.Context) (*SystemStats, error) {
	logger.Info("Retrieving system statistics")
	
	stats := &SystemStats{
		Providers: make(map[string]struct {
			Requests     int64   `json:"requests"`
			SuccessRate  float64 `json:"success_rate"`
			AvgTokens    float64 `json:"avg_tokens"`
			LastUsed     *time.Time `json:"last_used"`
		}),
	}
	
	// User statistics
	if err := s.getUserStats(ctx, stats); err != nil {
		logger.Error("Failed to get user stats", "error", err)
		return nil, fmt.Errorf("failed to get user statistics: %w", err)
	}
	
	// API key statistics
	if err := s.getAPIKeyStats(ctx, stats); err != nil {
		logger.Error("Failed to get API key stats", "error", err)
		return nil, fmt.Errorf("failed to get API key statistics: %w", err)
	}
	
	// Usage statistics
	if err := s.getUsageStats(ctx, stats); err != nil {
		logger.Error("Failed to get usage stats", "error", err)
		return nil, fmt.Errorf("failed to get usage statistics: %w", err)
	}
	
	// Provider statistics
	if err := s.getProviderStats(ctx, stats); err != nil {
		logger.Error("Failed to get provider stats", "error", err)
		return nil, fmt.Errorf("failed to get provider statistics: %w", err)
	}
	
	// System statistics
	s.getSystemInfo(stats)
	
	logger.Info("System statistics retrieved successfully")
	return stats, nil
}

// GetUsers returns all users with pagination
func (s *Service) GetUsers(ctx context.Context, page, limit int) ([]UserManagement, int64, error) {
	logger.Info("Retrieving users", "page", page, "limit", limit)
	
	offset := (page - 1) * limit
	
	var users []database.User
	var total int64
	
	// Get total count
	if err := s.db.Model(&database.User{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}
	
	// Get users with pagination
	if err := s.db.Offset(offset).Limit(limit).Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get users: %w", err)
	}
	
	// Convert to management format with additional stats
	userMgmt := make([]UserManagement, len(users))
	for i, user := range users {
		userMgmt[i] = UserManagement{
			ID:           user.ID,
			Email:        user.Email,
			Role:         user.Role,
			IsActive:     user.IsActive,
			SystemAPIKey: user.SystemAPIKey,
			CreatedAt:    user.CreatedAt,
			UpdatedAt:    user.UpdatedAt,
		}
		
		// Get additional stats for each user
		s.enrichUserStats(&userMgmt[i])
	}
	
	logger.Info("Users retrieved successfully", "count", len(userMgmt), "total", total)
	return userMgmt, total, nil
}

// GetUser returns a specific user with detailed information
func (s *Service) GetUser(ctx context.Context, userID uint) (*UserManagement, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	
	userMgmt := &UserManagement{
		ID:           user.ID,
		Email:        user.Email,
		Role:         user.Role,
		IsActive:     user.IsActive,
		SystemAPIKey: user.SystemAPIKey,
		CreatedAt:    user.CreatedAt,
		UpdatedAt:    user.UpdatedAt,
	}
	
	// Enrich with additional stats
	s.enrichUserStats(userMgmt)
	
	return userMgmt, nil
}

// UpdateUser updates a user's information
func (s *Service) UpdateUser(ctx context.Context, userID uint, req *UpdateUserRequest) (*UserManagement, error) {
	logger.Info("Updating user", "user_id", userID)
	
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	
	// Update fields
	updated := false
	if req.Role != "" && req.Role != user.Role {
		if req.Role != database.RoleUser && req.Role != database.RoleAdmin {
			return nil, fmt.Errorf("invalid role: %s", req.Role)
		}
		user.Role = req.Role
		updated = true
	}
	
	if req.IsActive != nil && *req.IsActive != user.IsActive {
		user.IsActive = *req.IsActive
		updated = true
	}
	
	if updated {
		if err := s.userRepo.Update(user); err != nil {
			return nil, fmt.Errorf("failed to update user: %w", err)
		}
	}
	
	logger.Info("User updated successfully", "user_id", userID)
	return s.GetUser(ctx, userID)
}

// DeleteUser deletes a user (soft delete)
func (s *Service) DeleteUser(ctx context.Context, userID uint) error {
	logger.Info("Deleting user", "user_id", userID)
	
	// Check if user exists
	_, err := s.userRepo.GetByID(userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	
	// Soft delete user
	if err := s.userRepo.Delete(userID); err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}
	
	logger.Info("User deleted successfully", "user_id", userID)
	return nil
}

// GetUserAPIKeys returns all API keys for a specific user
func (s *Service) GetUserAPIKeys(ctx context.Context, userID uint) ([]database.UserAPIKey, error) {
	keys, err := s.keyRepo.GetByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user API keys: %w", err)
	}
	
	return keys, nil
}

// GetSystemHealth returns system health information
func (s *Service) GetSystemHealth(ctx context.Context) (map[string]interface{}, error) {
	health := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now().UTC(),
		"checks":    make(map[string]interface{}),
	}
	
	checks := health["checks"].(map[string]interface{})
	
	// Database health check
	if err := s.db.Exec("SELECT 1").Error; err != nil {
		checks["database"] = map[string]interface{}{
			"status": "unhealthy",
			"error":  err.Error(),
		}
		health["status"] = "unhealthy"
	} else {
		checks["database"] = map[string]interface{}{
			"status": "healthy",
		}
	}
	
	// Redis health check
	if err := s.redis.Ping(ctx).Err(); err != nil {
		checks["redis"] = map[string]interface{}{
			"status": "unhealthy",
			"error":  err.Error(),
		}
		health["status"] = "unhealthy"
	} else {
		checks["redis"] = map[string]interface{}{
			"status": "healthy",
		}
	}
	
	// Memory usage (mock)
	checks["memory"] = map[string]interface{}{
		"status":      "healthy",
		"usage_mb":    256,
		"available_mb": 1024,
	}
	
	// Disk usage (mock)
	checks["disk"] = map[string]interface{}{
		"status":      "healthy",
		"usage_gb":    5.2,
		"available_gb": 50.0,
	}
	
	return health, nil
}

// Helper methods

func (s *Service) getUserStats(ctx context.Context, stats *SystemStats) error {
	// Total users
	if err := s.db.Model(&database.User{}).Count(&stats.Users.Total).Error; err != nil {
		return err
	}
	
	// Active users
	if err := s.db.Model(&database.User{}).Where("is_active = ?", true).Count(&stats.Users.Active).Error; err != nil {
		return err
	}
	
	stats.Users.Inactive = stats.Users.Total - stats.Users.Active
	
	// New users today
	today := time.Now().Truncate(24 * time.Hour)
	if err := s.db.Model(&database.User{}).Where("created_at >= ?", today).Count(&stats.Users.NewToday).Error; err != nil {
		return err
	}
	
	return nil
}

func (s *Service) getAPIKeyStats(ctx context.Context, stats *SystemStats) error {
	// Total API keys
	if err := s.db.Model(&database.UserAPIKey{}).Count(&stats.APIKeys.Total).Error; err != nil {
		return err
	}
	
	// Active API keys
	if err := s.db.Model(&database.UserAPIKey{}).Where("status = ?", database.StatusActive).Count(&stats.APIKeys.Active).Error; err != nil {
		return err
	}
	
	// Invalid API keys
	if err := s.db.Model(&database.UserAPIKey{}).Where("status = ?", database.StatusInvalid).Count(&stats.APIKeys.Invalid).Error; err != nil {
		return err
	}
	
	// Testing API keys
	if err := s.db.Model(&database.UserAPIKey{}).Where("status = ?", database.StatusTesting).Count(&stats.APIKeys.Testing).Error; err != nil {
		return err
	}
	
	return nil
}

func (s *Service) getUsageStats(ctx context.Context, stats *SystemStats) error {
	// Total requests
	if err := s.db.Model(&database.UsageLog{}).Count(&stats.Usage.TotalRequests).Error; err != nil {
		return err
	}
	
	// Successful requests
	if err := s.db.Model(&database.UsageLog{}).Where("status = ?", "success").Count(&stats.Usage.SuccessfulReqs).Error; err != nil {
		return err
	}
	
	stats.Usage.FailedReqs = stats.Usage.TotalRequests - stats.Usage.SuccessfulReqs
	
	// Total tokens
	var totalTokens sql.NullInt64
	if err := s.db.Model(&database.UsageLog{}).Select("SUM(total_tokens)").Scan(&totalTokens).Error; err != nil {
		return err
	}
	if totalTokens.Valid {
		stats.Usage.TotalTokens = totalTokens.Int64
	}
	
	// Today's stats
	today := time.Now().Truncate(24 * time.Hour)
	if err := s.db.Model(&database.UsageLog{}).Where("created_at >= ?", today).Count(&stats.Usage.RequestsToday).Error; err != nil {
		return err
	}
	
	var tokensToday sql.NullInt64
	if err := s.db.Model(&database.UsageLog{}).Where("created_at >= ?", today).Select("SUM(total_tokens)").Scan(&tokensToday).Error; err != nil {
		return err
	}
	if tokensToday.Valid {
		stats.Usage.TokensToday = tokensToday.Int64
	}
	
	return nil
}

func (s *Service) getProviderStats(ctx context.Context, stats *SystemStats) error {
	// Get provider statistics from usage logs
	providers := []string{"openai", "gemini", "claude", "perplexity", "mistral", "deepseek", "moonshot", "ollama", "aws", "azure", "ali"}
	
	for _, provider := range providers {
		var requests int64
		if err := s.db.Model(&database.UsageLog{}).Where("provider = ?", provider).Count(&requests).Error; err != nil {
			continue
		}
		
		if requests == 0 {
			continue
		}
		
		var successCount int64
		s.db.Model(&database.UsageLog{}).Where("provider = ? AND status = ?", provider, "success").Count(&successCount)
		
		successRate := float64(successCount) / float64(requests) * 100
		
		var avgTokens sql.NullFloat64
		s.db.Model(&database.UsageLog{}).Where("provider = ? AND total_tokens > 0", provider).Select("AVG(total_tokens)").Scan(&avgTokens)
		
		var lastUsed time.Time
		s.db.Model(&database.UsageLog{}).Where("provider = ?", provider).Select("MAX(created_at)").Scan(&lastUsed)
		
		stats.Providers[provider] = struct {
			Requests     int64   `json:"requests"`
			SuccessRate  float64 `json:"success_rate"`
			AvgTokens    float64 `json:"avg_tokens"`
			LastUsed     *time.Time `json:"last_used"`
		}{
			Requests:    requests,
			SuccessRate: successRate,
			AvgTokens:   avgTokens.Float64,
			LastUsed:    &lastUsed,
		}
	}
	
	return nil
}

func (s *Service) getSystemInfo(stats *SystemStats) {
	stats.System.Version = "1.0.0"
	stats.System.Environment = s.config.Environment
	stats.System.Uptime = time.Since(time.Now().Add(-24 * time.Hour)) // Mock uptime
	stats.System.DatabaseSize = 100 // Mock database size in MB
	stats.System.CacheHitRate = 95.5 // Mock cache hit rate
}

func (s *Service) enrichUserStats(user *UserManagement) {
	// Get API key count
	var keyCount int64
	s.db.Model(&database.UserAPIKey{}).Where("user_id = ?", user.ID).Count(&keyCount)
	user.APIKeyCount = int(keyCount)
	
	// Get usage stats
	var totalRequests int64
	s.db.Model(&database.UsageLog{}).Where("user_id = ?", user.ID).Count(&totalRequests)
	user.TotalRequests = totalRequests
	
	var totalTokens sql.NullInt64
	s.db.Model(&database.UsageLog{}).Where("user_id = ?", user.ID).Select("SUM(total_tokens)").Scan(&totalTokens)
	if totalTokens.Valid {
		user.TotalTokens = totalTokens.Int64
	}
}


