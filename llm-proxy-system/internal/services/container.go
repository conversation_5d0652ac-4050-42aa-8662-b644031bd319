package services

import (
	"llm-proxy-system/internal/adapters"
	"llm-proxy-system/internal/admin"
	"llm-proxy-system/internal/auth"
	"llm-proxy-system/internal/config"
	"llm-proxy-system/internal/keys"
	"llm-proxy-system/internal/monitoring"
	"llm-proxy-system/internal/proxy"
	"llm-proxy-system/internal/routing"
	"llm-proxy-system/internal/webhooks"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// Container holds all service dependencies
type Container struct {
	// Configuration
	Config *config.Config
	
	// Database connections
	DB    *gorm.DB
	Redis *redis.Client
	
	// Services (will be initialized in later modules)
	AuthService        *auth.Service
	AdapterService     *adapters.Service
	RoutingService     *routing.Service
	ProxyService       *proxy.Service
	KeyService         *keys.Service
	AdminService       *admin.Service
	WebhookService     *webhooks.Service
	MonitoringService  *monitoring.Service
	UserService        *UserService
	HealthService      *HealthService
}

// NewContainer creates a new service container
func NewContainer(db *gorm.DB, redis *redis.Client, cfg *config.Config) *Container {
	container := &Container{
		Config: cfg,
		DB:     db,
		Redis:  redis,
	}
	
	// Initialize services (placeholder implementations)
	container.initializeServices()
	
	return container
}

// initializeServices initializes all services
func (c *Container) initializeServices() {
	// Initialize auth service
	authService, err := auth.NewService(c.DB, c.Redis, c.Config)
	if err != nil {
		// Log error but don't fail - service will be nil and middleware will handle gracefully
		// In production, you might want to fail here
		c.AuthService = nil
	} else {
		c.AuthService = authService
	}

	// Initialize adapter service
	adapterService, err := adapters.NewService(c.DB, c.Config)
	if err != nil {
		// Log error but don't fail
		c.AdapterService = nil
	} else {
		c.AdapterService = adapterService
	}

	// Initialize routing service
	if c.AdapterService != nil {
		routingService := routing.NewService(c.DB, c.Redis, c.Config, c.AdapterService)
		c.RoutingService = routingService
	} else {
		c.RoutingService = nil
	}

	// Initialize proxy service
	if c.RoutingService != nil {
		proxyService := proxy.NewService(c.DB, c.Config, c.RoutingService)
		c.ProxyService = proxyService
	} else {
		c.ProxyService = nil
	}

	// Initialize key service
	if c.AdapterService != nil {
		keyService, err := keys.NewService(c.DB, c.Config, c.AdapterService)
		if err != nil {
			// Log error but don't fail
			c.KeyService = nil
		} else {
			c.KeyService = keyService
		}
	} else {
		c.KeyService = nil
	}

	// Initialize admin service
	adminService := admin.NewService(c.DB, c.Redis, c.Config)
	c.AdminService = adminService

	// Initialize webhook service
	webhookService := webhooks.NewService(c.DB, c.Config)
	c.WebhookService = webhookService

	// Initialize monitoring service
	monitoringService := monitoring.NewService(c.DB, c.Redis, c.Config)
	c.MonitoringService = monitoringService
	
	c.UserService = &UserService{
		db:     c.DB,
		config: c.Config,
	}
	

	

	

	

	

	
	c.HealthService = &HealthService{
		db:     c.DB,
		redis:  c.Redis,
		config: c.Config,
	}
}

// Placeholder service structs (will be implemented in their respective modules)

// UserService handles user management
type UserService struct {
	db     *gorm.DB
	config *config.Config
}











// HealthService handles health checks and monitoring
type HealthService struct {
	db     *gorm.DB
	redis  *redis.Client
	config *config.Config
}
