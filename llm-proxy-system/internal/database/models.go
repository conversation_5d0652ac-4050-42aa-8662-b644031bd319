package database

import (
	"time"

	"gorm.io/gorm"
)

// User represents a user in the system
type User struct {
	ID           uint      `gorm:"primaryKey" json:"id"`
	Email        string    `gorm:"uniqueIndex;not null" json:"email"`
	PasswordHash string    `gorm:"not null" json:"-"` // Don't expose in JSON
	SystemAPIKey string    `gorm:"uniqueIndex;not null" json:"system_api_key"`
	Role         string    `gorm:"not null;default:'user'" json:"role"` // 'user' or 'admin'
	IsActive     bool      `gorm:"not null;default:true" json:"is_active"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
	
	// Relationships
	APIKeys   []UserAPIKey `gorm:"foreignKey:UserID" json:"api_keys,omitempty"`
	UsageLogs []UsageLog   `gorm:"foreignKey:UserID" json:"usage_logs,omitempty"`
}

// UserAPIKey represents a provider API key for a user
type UserAPIKey struct {
	ID                uint      `gorm:"primaryKey" json:"id"`
	UserID            uint      `gorm:"not null;index" json:"user_id"`
	Provider          string    `gorm:"not null" json:"provider"` // 'openai', 'gemini', 'claude', etc.
	Name              string    `gorm:"not null" json:"name"`     // User-friendly name
	EncryptedAPIKey   string    `gorm:"not null" json:"-"`        // Encrypted API key
	Status            string    `gorm:"not null;default:'active'" json:"status"` // 'active', 'invalid', 'rate_limited', 'disabled'
	LastTestedAt      *time.Time `json:"last_tested_at"`
	LastUsedAt        *time.Time `json:"last_used_at"`
	ErrorMessage      string    `json:"error_message,omitempty"`
	RequestCount      int64     `gorm:"default:0" json:"request_count"`
	SuccessCount      int64     `gorm:"default:0" json:"success_count"`
	ErrorCount        int64     `gorm:"default:0" json:"error_count"`
	TotalTokensUsed   int64     `gorm:"default:0" json:"total_tokens_used"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `gorm:"index" json:"-"`
	
	// Relationships
	User      User       `gorm:"foreignKey:UserID" json:"user,omitempty"`
	UsageLogs []UsageLog `gorm:"foreignKey:UserAPIKeyID" json:"usage_logs,omitempty"`
}

// AdminGlobalRoutingRule represents global routing rules for provider selection
type AdminGlobalRoutingRule struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	Provider    string    `gorm:"not null" json:"provider"`
	Priority    int       `gorm:"not null" json:"priority"` // Lower number = higher priority
	IsEnabled   bool      `gorm:"not null;default:true" json:"is_enabled"`
	ModelFilter string    `json:"model_filter,omitempty"` // Optional model filter (regex or glob)
	Description string    `json:"description,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// UsageLog represents a log entry for API usage
type UsageLog struct {
	ID            uint      `gorm:"primaryKey" json:"id"`
	UserID        uint      `gorm:"not null;index" json:"user_id"`
	UserAPIKeyID  *uint     `gorm:"index" json:"user_api_key_id,omitempty"`
	Provider      string    `gorm:"not null;index" json:"provider"`
	Model         string    `gorm:"not null;index" json:"model"`
	RequestID     string    `gorm:"index" json:"request_id"`
	Method        string    `gorm:"not null" json:"method"` // 'chat', 'completion', 'embedding', etc.
	PromptTokens  int       `json:"prompt_tokens"`
	CompletionTokens int    `json:"completion_tokens"`
	TotalTokens   int       `json:"total_tokens"`
	Duration      int64     `json:"duration_ms"` // Duration in milliseconds
	Status        string    `gorm:"not null" json:"status"` // 'success', 'error', 'rate_limited'
	ErrorCode     string    `json:"error_code,omitempty"`
	ErrorMessage  string    `json:"error_message,omitempty"`
	IPAddress     string    `gorm:"index" json:"ip_address"`
	UserAgent     string    `json:"user_agent,omitempty"`
	CreatedAt     time.Time `gorm:"index" json:"created_at"`
	
	// Relationships
	User       User        `gorm:"foreignKey:UserID" json:"user,omitempty"`
	UserAPIKey *UserAPIKey `gorm:"foreignKey:UserAPIKeyID" json:"user_api_key,omitempty"`
}

// WebhookConfig represents webhook configuration for notifications
type WebhookConfig struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	UserID      *uint     `gorm:"index" json:"user_id,omitempty"` // Null for global webhooks
	Type        string    `gorm:"not null" json:"type"`           // 'discord', 'telegram', 'slack', etc.
	URL         string    `gorm:"not null" json:"url"`
	Secret      string    `json:"-"`                              // Webhook secret for verification
	Events      string    `gorm:"not null" json:"events"`         // JSON array of events to listen for
	IsEnabled   bool      `gorm:"not null;default:true" json:"is_enabled"`
	LastUsedAt  *time.Time `json:"last_used_at"`
	ErrorCount  int64     `gorm:"default:0" json:"error_count"`
	SuccessCount int64    `gorm:"default:0" json:"success_count"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
	
	// Relationships
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// Model constants for validation
const (
	// User roles
	RoleUser  = "user"
	RoleAdmin = "admin"
	
	// API key statuses
	StatusActive      = "active"
	StatusInvalid     = "invalid"
	StatusRateLimited = "rate_limited"
	StatusDisabled    = "disabled"
	StatusTesting     = "testing"
	
	// Usage log statuses
	LogStatusSuccess     = "success"
	LogStatusError       = "error"
	LogStatusRateLimited = "rate_limited"
	LogStatusTimeout     = "timeout"
	
	// Webhook types
	WebhookTypeDiscord  = "discord"
	WebhookTypeTelegram = "telegram"
	WebhookTypeSlack    = "slack"
	WebhookTypeGeneric  = "generic"
	
	// Supported providers
	ProviderOpenAI     = "openai"
	ProviderGemini     = "gemini"
	ProviderClaude     = "claude"
	ProviderPerplexity = "perplexity"
	ProviderMistral    = "mistral"
	ProviderDeepseek   = "deepseek"
	ProviderMoonshot   = "moonshot"
	ProviderOllama     = "ollama"
	ProviderAWS        = "aws"
	ProviderAzure      = "azure"
	ProviderAli        = "ali"
)

// TableName methods for custom table names (optional)
func (User) TableName() string {
	return "users"
}

func (UserAPIKey) TableName() string {
	return "user_api_keys"
}

func (AdminGlobalRoutingRule) TableName() string {
	return "admin_global_routing_rules"
}

func (UsageLog) TableName() string {
	return "usage_logs"
}

func (WebhookConfig) TableName() string {
	return "webhook_configs"
}

// Validation methods

// IsValidRole checks if the role is valid
func (u *User) IsValidRole() bool {
	return u.Role == RoleUser || u.Role == RoleAdmin
}

// IsAdmin checks if the user is an admin
func (u *User) IsAdmin() bool {
	return u.Role == RoleAdmin
}

// IsValidStatus checks if the API key status is valid
func (k *UserAPIKey) IsValidStatus() bool {
	validStatuses := []string{StatusActive, StatusInvalid, StatusRateLimited, StatusDisabled, StatusTesting}
	for _, status := range validStatuses {
		if k.Status == status {
			return true
		}
	}
	return false
}

// IsUsable checks if the API key can be used for requests
func (k *UserAPIKey) IsUsable() bool {
	return k.Status == StatusActive
}

// IsValidProvider checks if the provider is supported
func (k *UserAPIKey) IsValidProvider() bool {
	validProviders := []string{
		ProviderOpenAI, ProviderGemini, ProviderClaude, ProviderPerplexity,
		ProviderMistral, ProviderDeepseek, ProviderMoonshot, ProviderOllama,
		ProviderAWS, ProviderAzure, ProviderAli,
	}
	for _, provider := range validProviders {
		if k.Provider == provider {
			return true
		}
	}
	return false
}

// UpdateStats updates the API key statistics
func (k *UserAPIKey) UpdateStats(success bool, tokens int64) {
	k.RequestCount++
	k.TotalTokensUsed += tokens
	k.LastUsedAt = &time.Time{}
	*k.LastUsedAt = time.Now().UTC()
	
	if success {
		k.SuccessCount++
	} else {
		k.ErrorCount++
	}
}

// IsValidWebhookType checks if the webhook type is valid
func (w *WebhookConfig) IsValidWebhookType() bool {
	validTypes := []string{WebhookTypeDiscord, WebhookTypeTelegram, WebhookTypeSlack, WebhookTypeGeneric}
	for _, webhookType := range validTypes {
		if w.Type == webhookType {
			return true
		}
	}
	return false
}
