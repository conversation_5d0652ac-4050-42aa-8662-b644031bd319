package database

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// UserRepository handles user database operations
type UserRepository struct {
	db *gorm.DB
}

// NewUserRepository creates a new user repository
func NewUserRepository(db *gorm.DB) *UserRepository {
	return &UserRepository{db: db}
}

// Create creates a new user
func (r *UserRepository) Create(user *User) error {
	return r.db.Create(user).Error
}

// GetByID gets a user by ID
func (r *UserRepository) GetByID(id uint) (*User, error) {
	var user User
	err := r.db.First(&user, id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByEmail gets a user by email
func (r *UserRepository) GetByEmail(email string) (*User, error) {
	var user User
	err := r.db.Where("email = ?", email).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetBySystemAPIKey gets a user by system API key
func (r *UserRepository) GetBySystemAPIKey(apiKey string) (*User, error) {
	var user User
	err := r.db.Where("system_api_key = ?", apiKey).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// Update updates a user
func (r *UserRepository) Update(user *User) error {
	return r.db.Save(user).Error
}

// Delete soft deletes a user
func (r *UserRepository) Delete(id uint) error {
	return r.db.Delete(&User{}, id).Error
}

// List lists users with pagination
func (r *UserRepository) List(offset, limit int) ([]User, int64, error) {
	var users []User
	var total int64
	
	// Count total
	if err := r.db.Model(&User{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// Get paginated results
	err := r.db.Offset(offset).Limit(limit).Find(&users).Error
	return users, total, err
}

// UserAPIKeyRepository handles user API key database operations
type UserAPIKeyRepository struct {
	db *gorm.DB
}

// NewUserAPIKeyRepository creates a new user API key repository
func NewUserAPIKeyRepository(db *gorm.DB) *UserAPIKeyRepository {
	return &UserAPIKeyRepository{db: db}
}

// Create creates a new user API key
func (r *UserAPIKeyRepository) Create(key *UserAPIKey) error {
	return r.db.Create(key).Error
}

// GetByID gets a user API key by ID
func (r *UserAPIKeyRepository) GetByID(id uint) (*UserAPIKey, error) {
	var key UserAPIKey
	err := r.db.Preload("User").First(&key, id).Error
	if err != nil {
		return nil, err
	}
	return &key, nil
}

// GetByUserID gets all API keys for a user
func (r *UserAPIKeyRepository) GetByUserID(userID uint) ([]UserAPIKey, error) {
	var keys []UserAPIKey
	err := r.db.Where("user_id = ?", userID).Find(&keys).Error
	return keys, err
}

// GetByUserIDAndProvider gets API keys for a user and provider
func (r *UserAPIKeyRepository) GetByUserIDAndProvider(userID uint, provider string) ([]UserAPIKey, error) {
	var keys []UserAPIKey
	err := r.db.Where("user_id = ? AND provider = ?", userID, provider).Find(&keys).Error
	return keys, err
}

// GetActiveByProvider gets all active API keys for a provider
func (r *UserAPIKeyRepository) GetActiveByProvider(provider string) ([]UserAPIKey, error) {
	var keys []UserAPIKey
	err := r.db.Preload("User").Where("provider = ? AND status = ?", provider, StatusActive).Find(&keys).Error
	return keys, err
}

// Update updates a user API key
func (r *UserAPIKeyRepository) Update(key *UserAPIKey) error {
	return r.db.Save(key).Error
}

// UpdateStatus updates the status of an API key
func (r *UserAPIKeyRepository) UpdateStatus(id uint, status string, errorMessage string) error {
	updates := map[string]interface{}{
		"status":        status,
		"error_message": errorMessage,
		"updated_at":    time.Now().UTC(),
	}
	
	if status == StatusActive {
		updates["last_tested_at"] = time.Now().UTC()
	}
	
	return r.db.Model(&UserAPIKey{}).Where("id = ?", id).Updates(updates).Error
}

// Delete soft deletes a user API key
func (r *UserAPIKeyRepository) Delete(id uint) error {
	return r.db.Delete(&UserAPIKey{}, id).Error
}

// AdminGlobalRoutingRuleRepository handles routing rule database operations
type AdminGlobalRoutingRuleRepository struct {
	db *gorm.DB
}

// NewAdminGlobalRoutingRuleRepository creates a new routing rule repository
func NewAdminGlobalRoutingRuleRepository(db *gorm.DB) *AdminGlobalRoutingRuleRepository {
	return &AdminGlobalRoutingRuleRepository{db: db}
}

// Create creates a new routing rule
func (r *AdminGlobalRoutingRuleRepository) Create(rule *AdminGlobalRoutingRule) error {
	return r.db.Create(rule).Error
}

// GetByID gets a routing rule by ID
func (r *AdminGlobalRoutingRuleRepository) GetByID(id uint) (*AdminGlobalRoutingRule, error) {
	var rule AdminGlobalRoutingRule
	err := r.db.First(&rule, id).Error
	if err != nil {
		return nil, err
	}
	return &rule, nil
}

// GetAll gets all routing rules ordered by priority
func (r *AdminGlobalRoutingRuleRepository) GetAll() ([]AdminGlobalRoutingRule, error) {
	var rules []AdminGlobalRoutingRule
	err := r.db.Order("priority ASC").Find(&rules).Error
	return rules, err
}

// GetEnabled gets all enabled routing rules ordered by priority
func (r *AdminGlobalRoutingRuleRepository) GetEnabled() ([]AdminGlobalRoutingRule, error) {
	var rules []AdminGlobalRoutingRule
	err := r.db.Where("is_enabled = ?", true).Order("priority ASC").Find(&rules).Error
	return rules, err
}

// Update updates a routing rule
func (r *AdminGlobalRoutingRuleRepository) Update(rule *AdminGlobalRoutingRule) error {
	return r.db.Save(rule).Error
}

// UpdatePriorities updates the priorities of multiple rules
func (r *AdminGlobalRoutingRuleRepository) UpdatePriorities(updates []struct {
	ID       uint
	Priority int
}) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		for _, update := range updates {
			if err := tx.Model(&AdminGlobalRoutingRule{}).Where("id = ?", update.ID).Update("priority", update.Priority).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// Delete soft deletes a routing rule
func (r *AdminGlobalRoutingRuleRepository) Delete(id uint) error {
	return r.db.Delete(&AdminGlobalRoutingRule{}, id).Error
}

// UsageLogRepository handles usage log database operations
type UsageLogRepository struct {
	db *gorm.DB
}

// NewUsageLogRepository creates a new usage log repository
func NewUsageLogRepository(db *gorm.DB) *UsageLogRepository {
	return &UsageLogRepository{db: db}
}

// Create creates a new usage log entry
func (r *UsageLogRepository) Create(log *UsageLog) error {
	return r.db.Create(log).Error
}

// GetByUserID gets usage logs for a user with pagination
func (r *UsageLogRepository) GetByUserID(userID uint, offset, limit int) ([]UsageLog, int64, error) {
	var logs []UsageLog
	var total int64
	
	query := r.db.Where("user_id = ?", userID)
	
	// Count total
	if err := query.Model(&UsageLog{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// Get paginated results
	err := query.Preload("UserAPIKey").Order("created_at DESC").Offset(offset).Limit(limit).Find(&logs).Error
	return logs, total, err
}

// GetStats gets usage statistics for a user
func (r *UsageLogRepository) GetStats(userID uint, from, to time.Time) (map[string]interface{}, error) {
	var stats struct {
		TotalRequests    int64
		SuccessRequests  int64
		ErrorRequests    int64
		TotalTokens      int64
		AverageDuration  float64
	}
	
	query := r.db.Model(&UsageLog{}).Where("user_id = ? AND created_at BETWEEN ? AND ?", userID, from, to)
	
	// Total requests
	if err := query.Count(&stats.TotalRequests).Error; err != nil {
		return nil, err
	}
	
	// Success requests
	if err := query.Where("status = ?", LogStatusSuccess).Count(&stats.SuccessRequests).Error; err != nil {
		return nil, err
	}
	
	// Error requests
	stats.ErrorRequests = stats.TotalRequests - stats.SuccessRequests
	
	// Total tokens and average duration
	var result struct {
		TotalTokens     int64
		AverageDuration float64
	}
	
	err := query.Select("COALESCE(SUM(total_tokens), 0) as total_tokens, COALESCE(AVG(duration), 0) as average_duration").Scan(&result).Error
	if err != nil {
		return nil, err
	}
	
	stats.TotalTokens = result.TotalTokens
	stats.AverageDuration = result.AverageDuration
	
	return map[string]interface{}{
		"total_requests":    stats.TotalRequests,
		"success_requests":  stats.SuccessRequests,
		"error_requests":    stats.ErrorRequests,
		"success_rate":      float64(stats.SuccessRequests) / float64(stats.TotalRequests) * 100,
		"total_tokens":      stats.TotalTokens,
		"average_duration":  stats.AverageDuration,
	}, nil
}

// WebhookConfigRepository handles webhook configuration database operations
type WebhookConfigRepository struct {
	db *gorm.DB
}

// NewWebhookConfigRepository creates a new webhook config repository
func NewWebhookConfigRepository(db *gorm.DB) *WebhookConfigRepository {
	return &WebhookConfigRepository{db: db}
}

// Create creates a new webhook configuration
func (r *WebhookConfigRepository) Create(config *WebhookConfig) error {
	return r.db.Create(config).Error
}

// GetByID gets a webhook configuration by ID
func (r *WebhookConfigRepository) GetByID(id uint) (*WebhookConfig, error) {
	var config WebhookConfig
	err := r.db.Preload("User").First(&config, id).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// GetByUserID gets webhook configurations for a user
func (r *WebhookConfigRepository) GetByUserID(userID uint) ([]WebhookConfig, error) {
	var configs []WebhookConfig
	err := r.db.Where("user_id = ?", userID).Find(&configs).Error
	return configs, err
}

// GetGlobal gets global webhook configurations
func (r *WebhookConfigRepository) GetGlobal() ([]WebhookConfig, error) {
	var configs []WebhookConfig
	err := r.db.Where("user_id IS NULL AND is_enabled = ?", true).Find(&configs).Error
	return configs, err
}

// Update updates a webhook configuration
func (r *WebhookConfigRepository) Update(config *WebhookConfig) error {
	return r.db.Save(config).Error
}

// Delete soft deletes a webhook configuration
func (r *WebhookConfigRepository) Delete(id uint) error {
	return r.db.Delete(&WebhookConfig{}, id).Error
}
