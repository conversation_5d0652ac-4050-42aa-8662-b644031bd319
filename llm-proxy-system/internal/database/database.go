package database

import (
	"context"
	"fmt"
	"time"

	"llm-proxy-system/internal/logger"

	"github.com/redis/go-redis/v9"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

// Initialize initializes the database connection
func Initialize(databaseURL string) (*gorm.DB, error) {
	logger.Info("Initializing database connection")
	
	// Configure GORM logger
	var gormLogLevel gormLogger.LogLevel
	if logger.GetLogger().Level == logger.GetLogger().Level {
		gormLogLevel = gormLogger.Info
	} else {
		gormLogLevel = gormLogger.Error
	}
	
	config := &gorm.Config{
		Logger: gormLogger.Default.LogMode(gormLogLevel),
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	}
	
	// Open database connection
	db, err := gorm.Open(postgres.Open(databaseURL), config)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}
	
	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}
	
	// Set connection pool settings
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)
	
	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := sqlDB.PingContext(ctx); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}
	
	logger.Info("Database connection established successfully")
	return db, nil
}

// InitializeRedis initializes the Redis connection
func InitializeRedis(redisURL string) (*redis.Client, error) {
	logger.Info("Initializing Redis connection")
	
	// Parse Redis URL
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Redis URL: %w", err)
	}
	
	// Create Redis client
	client := redis.NewClient(opt)
	
	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to ping Redis: %w", err)
	}
	
	logger.Info("Redis connection established successfully")
	return client, nil
}

// Migrate runs database migrations
func Migrate(db *gorm.DB) error {
	logger.Info("Running database migrations")
	
	// Auto-migrate all models
	err := db.AutoMigrate(
		&User{},
		&UserAPIKey{},
		&AdminGlobalRoutingRule{},
		&UsageLog{},
		&WebhookConfig{},
	)
	
	if err != nil {
		return fmt.Errorf("failed to run migrations: %w", err)
	}
	
	logger.Info("Database migrations completed successfully")
	return nil
}

// HealthCheck checks the health of database connections
func HealthCheck(db *gorm.DB, redis *redis.Client) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	// Check PostgreSQL
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get database instance: %w", err)
	}
	
	if err := sqlDB.PingContext(ctx); err != nil {
		return fmt.Errorf("PostgreSQL health check failed: %w", err)
	}
	
	// Check Redis
	if err := redis.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("Redis health check failed: %w", err)
	}
	
	return nil
}

// GetDatabaseStats returns database connection statistics
func GetDatabaseStats(db *gorm.DB) (map[string]interface{}, error) {
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get database instance: %w", err)
	}
	
	stats := sqlDB.Stats()
	
	return map[string]interface{}{
		"max_open_connections":     stats.MaxOpenConnections,
		"open_connections":         stats.OpenConnections,
		"in_use":                  stats.InUse,
		"idle":                    stats.Idle,
		"wait_count":              stats.WaitCount,
		"wait_duration":           stats.WaitDuration.String(),
		"max_idle_closed":         stats.MaxIdleClosed,
		"max_idle_time_closed":    stats.MaxIdleTimeClosed,
		"max_lifetime_closed":     stats.MaxLifetimeClosed,
	}, nil
}

// Transaction executes a function within a database transaction
func Transaction(db *gorm.DB, fn func(*gorm.DB) error) error {
	return db.Transaction(fn)
}

// Close closes database connections
func Close(db *gorm.DB, redis *redis.Client) error {
	logger.Info("Closing database connections")
	
	// Close PostgreSQL
	if db != nil {
		sqlDB, err := db.DB()
		if err == nil {
			if err := sqlDB.Close(); err != nil {
				logger.Error("Failed to close PostgreSQL connection", "error", err)
			}
		}
	}
	
	// Close Redis
	if redis != nil {
		if err := redis.Close(); err != nil {
			logger.Error("Failed to close Redis connection", "error", err)
			return err
		}
	}
	
	logger.Info("Database connections closed successfully")
	return nil
}
