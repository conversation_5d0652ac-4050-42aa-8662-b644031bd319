package middleware

import (
	"fmt"
	"time"

	"llm-proxy-system/internal/logger"
	"llm-proxy-system/internal/services"
	"llm-proxy-system/internal/utils"

	"github.com/gin-gonic/gin"
)

// RequestID adds a unique request ID to each request
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.Get<PERSON>eader("X-Request-ID")
		if requestID == "" {
			// Generate a simple UUID-like string without external dependency
			requestID = generateSimpleUUID()
		}
		
		c.Set("request_id", requestID)
		c.<PERSON>er("X-Request-ID", requestID)
		c.Next()
	}
}

// SecurityHeaders adds security headers to responses
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// HSTS (HTTP Strict Transport Security)
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		
		// Content Security Policy
		c.Header("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'")
		
		// X-Frame-Options
		c.Header("X-Frame-Options", "DENY")
		
		// X-Content-Type-Options
		c.Header("X-Content-Type-Options", "nosniff")
		
		// X-XSS-Protection
		c.Header("X-XSS-Protection", "1; mode=block")
		
		// Referrer Policy
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		
		c.Next()
	}
}

// LoggingMiddleware logs HTTP requests
func LoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		
		// Process request
		c.Next()
		
		// Calculate duration
		duration := time.Since(start).Milliseconds()
		
		// Get user ID if available
		userID := ""
		if user, exists := c.Get("user_id"); exists {
			if id, ok := user.(string); ok {
				userID = id
			}
		}
		
		// Log request
		logger.RequestLogger(
			c.Request.Method,
			c.Request.URL.Path,
			userID,
			c.Writer.Status(),
			float64(duration),
		).Info("HTTP request processed")
	}
}

// AuthRequired middleware validates JWT tokens or API keys
func AuthRequired(services *services.Container) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			utils.UnauthorizedResponse(c, "Authorization header required")
			c.Abort()
			return
		}

		// Check if it's a Bearer token (JWT) or API key
		if len(authHeader) >= 7 && authHeader[:7] == "Bearer " {
			token := authHeader[7:]

			// Check if it's a system API key (starts with sk-)
			if len(token) > 3 && token[:3] == "sk-" {
				// Validate API key
				if services.AuthService != nil {
					user, err := services.AuthService.ValidateSystemAPIKey(c.Request.Context(), token)
					if err != nil {
						utils.UnauthorizedResponse(c, "Invalid API key")
						c.Abort()
						return
					}

					c.Set("user_id", user.ID)
					c.Set("user_email", user.Email)
					c.Set("user_role", user.Role)
					c.Set("auth_method", "api_key")
				} else {
					// Fallback for when auth service is not initialized
					c.Set("user_id", uint(1))
					c.Set("user_role", "user")
				}
			} else {
				// Validate JWT token
				if services.AuthService != nil {
					// TODO: Implement JWT validation through auth service
					// For now, just pass through
					c.Set("user_id", uint(1))
					c.Set("user_role", "user")
				} else {
					c.Set("user_id", uint(1))
					c.Set("user_role", "user")
				}
			}
		} else {
			utils.UnauthorizedResponse(c, "Invalid authorization header format")
			c.Abort()
			return
		}

		c.Next()
	}
}

// AdminRequired middleware ensures user has admin role
func AdminRequired(services *services.Container) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user role from context (set by AuthRequired middleware)
		role, exists := c.Get("user_role")
		if !exists {
			utils.ForbiddenResponse(c, "User role not found")
			c.Abort()
			return
		}
		
		userRole, ok := role.(string)
		if !ok || userRole != "admin" {
			utils.ForbiddenResponse(c, "Admin access required")
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// RateLimit middleware implements rate limiting
func RateLimit(services *services.Container) gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement Redis-based rate limiting
		// For now, just pass through
		
		// Get user ID from context
		userID, exists := c.Get("user_id")
		if !exists {
			utils.UnauthorizedResponse(c, "User not authenticated")
			c.Abort()
			return
		}
		
		userIDStr, ok := userID.(string)
		if !ok {
			utils.InternalErrorResponse(c, "Invalid user ID")
			c.Abort()
			return
		}
		
		// TODO: Check rate limit in Redis
		// Key format: rate_limit:user:{user_id}
		// Implement sliding window or token bucket algorithm
		
		logger.Debug("Rate limit check", "user_id", userIDStr)
		
		c.Next()
	}
}

// ValidateJSON middleware validates JSON request body
func ValidateJSON() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			contentType := c.GetHeader("Content-Type")
			if contentType != "application/json" {
				utils.ValidationErrorResponse(c, "Content-Type must be application/json")
				c.Abort()
				return
			}
		}
		
		c.Next()
	}
}

// CORS middleware (using gin-contrib/cors in router.go)
// This is a placeholder for custom CORS logic if needed
func CustomCORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Custom CORS logic can be implemented here if needed
		// Currently using gin-contrib/cors in router.go
		c.Next()
	}
}

// ErrorHandler middleware handles panics and errors
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				logger.Error("Panic recovered", "error", err, "path", c.Request.URL.Path)
				utils.InternalErrorResponse(c, "Internal server error")
				c.Abort()
			}
		}()

		c.Next()
	}
}

// generateSimpleUUID generates a simple UUID-like string without external dependencies
func generateSimpleUUID() string {
	// Simple implementation for request ID generation
	// In production, consider using a proper UUID library
	return fmt.Sprintf("%d-%d", time.Now().UnixNano(), time.Now().Unix())
}
