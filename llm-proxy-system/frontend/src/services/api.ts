import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import toast from 'react-hot-toast';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    } else if (error.response?.status >= 500) {
      // Server error
      toast.error('Server error. Please try again later.');
    }
    return Promise.reject(error);
  }
);

// Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: string;
  };
  timestamp: string;
  request_id: string;
}

export interface PaginatedResponse<T = any> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  timestamp: string;
  request_id: string;
}

export interface User {
  id: number;
  email: string;
  role: 'user' | 'admin';
  is_active: boolean;
  system_api_key: string;
  created_at: string;
  updated_at: string;
}

export interface APIKey {
  id: number;
  provider: string;
  name: string;
  status: 'active' | 'invalid' | 'testing';
  last_tested_at?: string;
  last_used_at?: string;
  error_message?: string;
  request_count: number;
  success_count: number;
  error_count: number;
  total_tokens_used: number;
  created_at: string;
  updated_at: string;
  masked_api_key: string;
}

export interface Webhook {
  id: number;
  user_id: number;
  name: string;
  url: string;
  events: string[];
  is_active: boolean;
  description: string;
  last_triggered_at?: string;
  success_count: number;
  failure_count: number;
  created_at: string;
  updated_at: string;
}

export interface SystemStats {
  users: {
    total: number;
    active: number;
    inactive: number;
    new_today: number;
  };
  api_keys: {
    total: number;
    active: number;
    invalid: number;
    testing: number;
  };
  usage: {
    total_requests: number;
    successful_requests: number;
    failed_requests: number;
    total_tokens: number;
    requests_today: number;
    tokens_today: number;
  };
  providers: Record<string, {
    requests: number;
    success_rate: number;
    avg_tokens: number;
    last_used?: string;
  }>;
  system: {
    uptime: string;
    version: string;
    environment: string;
    database_size_mb: number;
    cache_hit_rate: number;
  };
}

// API Service Class
class ApiService {
  // Authentication
  async login(email: string, password: string): Promise<{ token: string; user: User }> {
    const response = await api.post<ApiResponse<{ token: string; user: User }>>('/auth/login', {
      email,
      password,
    });
    return response.data.data!;
  }

  async register(email: string, password: string): Promise<{ token: string; user: User }> {
    const response = await api.post<ApiResponse<{ token: string; user: User }>>('/auth/register', {
      email,
      password,
    });
    return response.data.data!;
  }

  async logout(): Promise<void> {
    await api.post('/auth/logout');
  }

  async getCurrentUser(): Promise<User> {
    const response = await api.get<ApiResponse<User>>('/users/me');
    return response.data.data!;
  }

  // API Keys
  async getAPIKeys(provider?: string): Promise<{ keys: APIKey[]; count: number }> {
    const params = provider ? { provider } : {};
    const response = await api.get<ApiResponse<{ keys: APIKey[]; count: number }>>('/users/me/keys', { params });
    return response.data.data!;
  }

  async createAPIKey(data: { provider: string; name: string; api_key: string }): Promise<APIKey> {
    const response = await api.post<ApiResponse<APIKey>>('/users/me/keys', data);
    return response.data.data!;
  }

  async updateAPIKey(id: number, data: { name?: string; api_key?: string }): Promise<APIKey> {
    const response = await api.put<ApiResponse<APIKey>>(`/users/me/keys/${id}`, data);
    return response.data.data!;
  }

  async deleteAPIKey(id: number): Promise<void> {
    await api.delete(`/users/me/keys/${id}`);
  }

  async testAPIKey(id: number): Promise<APIKey> {
    const response = await api.post<ApiResponse<APIKey>>(`/users/me/keys/${id}/test`);
    return response.data.data!;
  }

  async getKeyStats(): Promise<any> {
    const response = await api.get<ApiResponse<any>>('/users/me/keys/stats');
    return response.data.data!;
  }

  // Webhooks
  async getWebhooks(): Promise<{ webhooks: Webhook[]; count: number }> {
    const response = await api.get<ApiResponse<{ webhooks: Webhook[]; count: number }>>('/users/me/webhooks');
    return response.data.data!;
  }

  async createWebhook(data: {
    name: string;
    url: string;
    events: string[];
    is_active?: boolean;
    secret?: string;
    description?: string;
  }): Promise<Webhook> {
    const response = await api.post<ApiResponse<Webhook>>('/users/me/webhooks', data);
    return response.data.data!;
  }

  async updateWebhook(id: number, data: {
    name?: string;
    url?: string;
    events?: string[];
    is_active?: boolean;
    secret?: string;
    description?: string;
  }): Promise<Webhook> {
    const response = await api.put<ApiResponse<Webhook>>(`/users/me/webhooks/${id}`, data);
    return response.data.data!;
  }

  async deleteWebhook(id: number): Promise<void> {
    await api.delete(`/users/me/webhooks/${id}`);
  }

  async testWebhook(id: number): Promise<void> {
    await api.post(`/users/me/webhooks/${id}/test`);
  }

  async getSupportedEvents(): Promise<{ events: Array<{ type: string; description: string }>; count: number }> {
    const response = await api.get<ApiResponse<{ events: Array<{ type: string; description: string }>; count: number }>>('/users/me/webhooks/events');
    return response.data.data!;
  }

  // LLM Proxy
  async proxyLLMRequest(data: {
    model: string;
    messages?: any[];
    prompt?: string;
    max_tokens?: number;
    temperature?: number;
    stream?: boolean;
    provider?: string;
  }): Promise<any> {
    const response = await api.post<any>('/llm/proxy', data);
    return response.data;
  }

  async getSupportedModels(): Promise<{ data: any[]; object: string }> {
    const response = await api.get<{ data: any[]; object: string }>('/models');
    return response.data;
  }

  async validateRequest(data: any): Promise<{ valid: boolean; message: string; model: string }> {
    const response = await api.post<ApiResponse<{ valid: boolean; message: string; model: string }>>('/llm/validate', data);
    return response.data.data!;
  }

  async getRequestHistory(page = 1, limit = 20): Promise<PaginatedResponse<any>> {
    const response = await api.get<PaginatedResponse<any>>('/llm/history', {
      params: { page, limit },
    });
    return response.data;
  }

  // Admin APIs (admin role required)
  async getSystemStats(): Promise<SystemStats> {
    const response = await api.get<ApiResponse<SystemStats>>('/admin/system/stats');
    return response.data.data!;
  }

  async getSystemHealth(): Promise<any> {
    const response = await api.get<any>('/admin/system/health');
    return response.data;
  }

  async getUsers(page = 1, limit = 20): Promise<PaginatedResponse<any>> {
    const response = await api.get<PaginatedResponse<any>>('/admin/users', {
      params: { page, limit },
    });
    return response.data;
  }

  async updateUser(id: number, data: { role?: string; is_active?: boolean }): Promise<any> {
    const response = await api.put<ApiResponse<any>>(`/admin/users/${id}`, data);
    return response.data.data!;
  }

  async deleteUser(id: number): Promise<void> {
    await api.delete(`/admin/users/${id}`);
  }

  // Routing
  async getRoutingRules(): Promise<any[]> {
    const response = await api.get<ApiResponse<any[]>>('/admin/routing-rules');
    return response.data.data!;
  }

  async createRoutingRule(data: {
    provider: string;
    priority: number;
    model_filter?: string;
    description?: string;
    is_enabled?: boolean;
  }): Promise<any> {
    const response = await api.post<ApiResponse<any>>('/admin/routing-rules', data);
    return response.data.data!;
  }

  async updateRoutingRule(id: number, data: any): Promise<any> {
    const response = await api.put<ApiResponse<any>>(`/admin/routing-rules/${id}`, data);
    return response.data.data!;
  }

  async deleteRoutingRule(id: number): Promise<void> {
    await api.delete(`/admin/routing-rules/${id}`);
  }

  // Providers
  async getProviders(): Promise<any[]> {
    const response = await api.get<ApiResponse<any[]>>('/providers');
    return response.data.data!;
  }

  async getProviderHealth(): Promise<any> {
    const response = await api.get<ApiResponse<any>>('/routing/health');
    return response.data.data!;
  }
}

export const apiService = new ApiService();
export default api;
