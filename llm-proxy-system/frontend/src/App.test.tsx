import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import App from './App';

// Mock the API service
jest.mock('./services/api', () => ({
  apiService: {
    getCurrentUser: jest.fn().mockRejectedValue(new Error('Not authenticated')),
  },
}));

// Mock react-hot-toast
jest.mock('react-hot-toast', () => ({
  __esModule: true,
  default: {
    success: jest.fn(),
    error: jest.fn(),
  },
  Toaster: () => <div data-testid="toaster" />,
}));

const renderApp = () => {
  return render(
    <BrowserRouter>
      <App />
    </BrowserRouter>
  );
};

describe('App Component', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  test('renders login page when not authenticated', async () => {
    renderApp();
    
    await waitFor(() => {
      expect(screen.getByText('Sign in to your account')).toBeInTheDocument();
    });
  });

  test('renders toaster component', () => {
    renderApp();
    expect(screen.getByTestId('toaster')).toBeInTheDocument();
  });

  test('shows loading spinner initially', () => {
    renderApp();
    // The loading spinner should be visible while checking authentication
    expect(document.querySelector('.loading-spinner-lg')).toBeInTheDocument();
  });
});

describe('Authentication Flow', () => {
  test('redirects to login when no token exists', async () => {
    renderApp();
    
    await waitFor(() => {
      expect(screen.getByText('Sign in to your account')).toBeInTheDocument();
    });
  });

  test('shows register link on login page', async () => {
    renderApp();
    
    await waitFor(() => {
      expect(screen.getByText('create a new account')).toBeInTheDocument();
    });
  });
});

describe('Responsive Design', () => {
  test('renders mobile-friendly layout', async () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });

    renderApp();
    
    await waitFor(() => {
      expect(screen.getByText('Sign in to your account')).toBeInTheDocument();
    });
  });
});

describe('Error Handling', () => {
  test('handles authentication errors gracefully', async () => {
    // Set invalid token
    localStorage.setItem('auth_token', 'invalid-token');
    localStorage.setItem('user', JSON.stringify({ email: '<EMAIL>' }));

    renderApp();
    
    await waitFor(() => {
      expect(screen.getByText('Sign in to your account')).toBeInTheDocument();
    });

    // Token should be cleared after failed authentication
    expect(localStorage.getItem('auth_token')).toBeNull();
    expect(localStorage.getItem('user')).toBeNull();
  });
});

describe('Accessibility', () => {
  test('has proper heading structure', async () => {
    renderApp();
    
    await waitFor(() => {
      const headings = screen.getAllByRole('heading');
      expect(headings.length).toBeGreaterThan(0);
    });
  });

  test('has proper form labels', async () => {
    renderApp();
    
    await waitFor(() => {
      expect(screen.getByLabelText('Email address')).toBeInTheDocument();
      expect(screen.getByLabelText('Password')).toBeInTheDocument();
    });
  });
});

describe('Navigation', () => {
  test('renders navigation links when authenticated', async () => {
    // Mock authenticated user
    const mockUser = {
      id: 1,
      email: '<EMAIL>',
      role: 'user',
      is_active: true,
      system_api_key: 'sk-test',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    };

    localStorage.setItem('auth_token', 'valid-token');
    localStorage.setItem('user', JSON.stringify(mockUser));

    // Mock successful API call
    const { apiService } = require('./services/api');
    apiService.getCurrentUser.mockResolvedValue(mockUser);

    renderApp();
    
    await waitFor(() => {
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
    });
  });
});

describe('Theme and Styling', () => {
  test('applies correct CSS classes', async () => {
    renderApp();
    
    await waitFor(() => {
      const appElement = document.querySelector('.App');
      expect(appElement).toBeInTheDocument();
    });
  });

  test('has responsive grid layout', async () => {
    renderApp();
    
    await waitFor(() => {
      // Check for responsive classes in the DOM
      const responsiveElements = document.querySelectorAll('[class*="sm:"], [class*="lg:"]');
      expect(responsiveElements.length).toBeGreaterThan(0);
    });
  });
});

describe('Performance', () => {
  test('lazy loads components efficiently', async () => {
    renderApp();
    
    // The app should render quickly without blocking
    await waitFor(() => {
      expect(screen.getByText('Sign in to your account')).toBeInTheDocument();
    }, { timeout: 1000 });
  });
});

describe('Security', () => {
  test('clears sensitive data on logout', async () => {
    localStorage.setItem('auth_token', 'test-token');
    localStorage.setItem('user', JSON.stringify({ email: '<EMAIL>' }));

    renderApp();
    
    await waitFor(() => {
      // After failed authentication, sensitive data should be cleared
      expect(localStorage.getItem('auth_token')).toBeNull();
      expect(localStorage.getItem('user')).toBeNull();
    });
  });

  test('validates user input properly', async () => {
    renderApp();
    
    await waitFor(() => {
      const emailInput = screen.getByLabelText('Email address');
      const passwordInput = screen.getByLabelText('Password');
      
      expect(emailInput).toHaveAttribute('type', 'email');
      expect(passwordInput).toHaveAttribute('type', 'password');
      expect(emailInput).toHaveAttribute('required');
      expect(passwordInput).toHaveAttribute('required');
    });
  });
});
