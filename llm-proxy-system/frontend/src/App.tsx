import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AuthProvider, useAuth } from './contexts/AuthContext';

// Layout Components
import DashboardLayout from './components/layout/DashboardLayout';
import AuthLayout from './components/layout/AuthLayout';

// Auth Pages
import LoginPage from './pages/auth/LoginPage';
import RegisterPage from './pages/auth/RegisterPage';

// User Pages
import DashboardPage from './pages/dashboard/DashboardPage';
import APIKeysPage from './pages/keys/APIKeysPage';
import WebhooksPage from './pages/webhooks/WebhooksPage';
import LLMPlaygroundPage from './pages/llm/LLMPlaygroundPage';
import UsageHistoryPage from './pages/usage/UsageHistoryPage';
import ProfilePage from './pages/profile/ProfilePage';

// Admin Pages
import AdminDashboardPage from './pages/admin/AdminDashboardPage';
import AdminUsersPage from './pages/admin/AdminUsersPage';
import AdminSystemPage from './pages/admin/AdminSystemPage';
import AdminRoutingPage from './pages/admin/AdminRoutingPage';

// Error Pages
import NotFoundPage from './pages/error/NotFoundPage';

// Protected Route Component
const ProtectedRoute: React.FC<{ children: React.ReactNode; requireAdmin?: boolean }> = ({ 
  children, 
  requireAdmin = false 
}) => {
  const { isAuthenticated, isAdmin, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading-spinner-lg"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requireAdmin && !isAdmin) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

// Public Route Component (redirect if authenticated)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading-spinner-lg"></div>
      </div>
    );
  }

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/login" element={
        <PublicRoute>
          <AuthLayout>
            <LoginPage />
          </AuthLayout>
        </PublicRoute>
      } />
      
      <Route path="/register" element={
        <PublicRoute>
          <AuthLayout>
            <RegisterPage />
          </AuthLayout>
        </PublicRoute>
      } />

      {/* Protected User Routes */}
      <Route path="/dashboard" element={
        <ProtectedRoute>
          <DashboardLayout>
            <DashboardPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/keys" element={
        <ProtectedRoute>
          <DashboardLayout>
            <APIKeysPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/webhooks" element={
        <ProtectedRoute>
          <DashboardLayout>
            <WebhooksPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/playground" element={
        <ProtectedRoute>
          <DashboardLayout>
            <LLMPlaygroundPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/usage" element={
        <ProtectedRoute>
          <DashboardLayout>
            <UsageHistoryPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/profile" element={
        <ProtectedRoute>
          <DashboardLayout>
            <ProfilePage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      {/* Protected Admin Routes */}
      <Route path="/admin" element={
        <ProtectedRoute requireAdmin>
          <DashboardLayout>
            <AdminDashboardPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/admin/users" element={
        <ProtectedRoute requireAdmin>
          <DashboardLayout>
            <AdminUsersPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/admin/system" element={
        <ProtectedRoute requireAdmin>
          <DashboardLayout>
            <AdminSystemPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/admin/routing" element={
        <ProtectedRoute requireAdmin>
          <DashboardLayout>
            <AdminRoutingPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      {/* Default Routes */}
      <Route path="/" element={<Navigate to="/dashboard" replace />} />
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};

const App: React.FC = () => {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <AppRoutes />
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                iconTheme: {
                  primary: '#10b981',
                  secondary: '#fff',
                },
              },
              error: {
                duration: 5000,
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#fff',
                },
              },
            }}
          />
        </div>
      </Router>
    </AuthProvider>
  );
};

export default App;
