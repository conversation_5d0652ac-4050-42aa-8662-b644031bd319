import React from 'react';
import { RouteIcon } from '@heroicons/react/24/outline';

const AdminRoutingPage: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Routing Rules</h1>
        <p className="mt-1 text-sm text-gray-500">
          Manage LLM provider routing and priority rules
        </p>
      </div>

      <div className="card">
        <div className="card-body text-center py-12">
          <RouteIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Routing Configuration</h3>
          <p className="mt-1 text-sm text-gray-500">
            Routing rules management will be implemented here.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AdminRoutingPage;
