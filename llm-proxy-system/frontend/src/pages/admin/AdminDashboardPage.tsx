import React, { useState, useEffect } from 'react';
import { apiService, SystemStats } from '../../services/api';
import { UsersIcon, KeyIcon, ServerIcon, ChartBarIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const AdminDashboardPage: React.FC = () => {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSystemStats();
  }, []);

  const loadSystemStats = async () => {
    try {
      setIsLoading(true);
      const data = await apiService.getSystemStats();
      setStats(data);
    } catch (error) {
      console.error('Failed to load system stats:', error);
      toast.error('Failed to load system statistics');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="skeleton h-8 w-64"></div>
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="card p-6">
              <div className="skeleton h-6 w-20 mb-2"></div>
              <div className="skeleton h-8 w-16"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500">
          System overview and administration
        </p>
      </div>

      {/* System Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="stat-card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UsersIcon className="h-6 w-6 text-primary-600" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="stat-label">Total Users</dt>
                <dd className="stat-value">{stats?.users.total || 0}</dd>
              </dl>
            </div>
          </div>
          <div className="mt-3 flex text-sm">
            <span className="text-success-600">{stats?.users.active || 0} active</span>
            <span className="ml-2 text-gray-600">{stats?.users.inactive || 0} inactive</span>
          </div>
        </div>

        <div className="stat-card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <KeyIcon className="h-6 w-6 text-success-600" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="stat-label">API Keys</dt>
                <dd className="stat-value">{stats?.api_keys.total || 0}</dd>
              </dl>
            </div>
          </div>
          <div className="mt-3 flex text-sm">
            <span className="text-success-600">{stats?.api_keys.active || 0} active</span>
            <span className="ml-2 text-error-600">{stats?.api_keys.invalid || 0} invalid</span>
          </div>
        </div>

        <div className="stat-card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-6 w-6 text-warning-600" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="stat-label">Total Requests</dt>
                <dd className="stat-value">{stats?.usage.total_requests.toLocaleString() || 0}</dd>
              </dl>
            </div>
          </div>
          <div className="mt-3 text-sm text-gray-600">
            {stats?.usage.requests_today || 0} today
          </div>
        </div>

        <div className="stat-card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ServerIcon className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="stat-label">System Health</dt>
                <dd className="stat-value text-success-600">Healthy</dd>
              </dl>
            </div>
          </div>
          <div className="mt-3 text-sm text-gray-600">
            Version {stats?.system.version || '1.0.0'}
          </div>
        </div>
      </div>

      {/* Provider Stats */}
      {stats?.providers && Object.keys(stats.providers).length > 0 && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">Provider Statistics</h3>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {Object.entries(stats.providers).map(([provider, data]) => (
                <div key={provider} className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 capitalize">{provider}</h4>
                  <div className="mt-2 space-y-1 text-sm text-gray-600">
                    <div>Requests: {data.requests.toLocaleString()}</div>
                    <div>Success Rate: {data.success_rate.toFixed(1)}%</div>
                    <div>Avg Tokens: {data.avg_tokens.toFixed(0)}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboardPage;
