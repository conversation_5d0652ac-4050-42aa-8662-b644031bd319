import React, { useState, useEffect } from 'react';
import { apiService } from '../../services/api';
import { ServerIcon, CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const AdminSystemPage: React.FC = () => {
  const [health, setHealth] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSystemHealth();
  }, []);

  const loadSystemHealth = async () => {
    try {
      setIsLoading(true);
      const data = await apiService.getSystemHealth();
      setHealth(data);
    } catch (error) {
      console.error('Failed to load system health:', error);
      toast.error('Failed to load system health');
    } finally {
      setIsLoading(false);
    }
  };

  const getHealthIcon = (status: string) => {
    return status === 'healthy' ? 
      <CheckCircleIcon className="h-5 w-5 text-success-500" /> :
      <ExclamationTriangleIcon className="h-5 w-5 text-error-500" />;
  };

  const getHealthBadge = (status: string) => {
    return status === 'healthy' ? 
      <span className="badge-success">Healthy</span> :
      <span className="badge-error">Unhealthy</span>;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="skeleton h-8 w-64"></div>
        <div className="card">
          <div className="card-body">
            <div className="skeleton h-32 w-full"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">System Health</h1>
          <p className="mt-1 text-sm text-gray-500">
            Monitor system components and performance
          </p>
        </div>
        <button
          onClick={loadSystemHealth}
          className="btn-outline"
        >
          Refresh
        </button>
      </div>

      {/* Overall Status */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center">
            <ServerIcon className="h-6 w-6 text-gray-400 mr-3" />
            <h3 className="text-lg font-medium">System Status</h3>
          </div>
        </div>
        <div className="card-body">
          <div className="flex items-center">
            {getHealthIcon(health?.status || 'unknown')}
            <span className="ml-2 text-lg font-medium">
              {getHealthBadge(health?.status || 'unknown')}
            </span>
            <span className="ml-4 text-sm text-gray-500">
              Last checked: {health?.timestamp ? new Date(health.timestamp).toLocaleString() : 'Unknown'}
            </span>
          </div>
        </div>
      </div>

      {/* Component Health */}
      {health?.checks && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">Component Health</h3>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              {Object.entries(health.checks).map(([component, check]: [string, any]) => (
                <div key={component} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900 capitalize">{component}</h4>
                    {getHealthIcon(check.status)}
                  </div>
                  <div className="mt-2">
                    {getHealthBadge(check.status)}
                  </div>
                  {check.error && (
                    <div className="mt-2 text-sm text-error-600">
                      {check.error}
                    </div>
                  )}
                  {check.usage_mb && (
                    <div className="mt-2 text-sm text-gray-600">
                      Usage: {check.usage_mb} MB
                    </div>
                  )}
                  {check.available_mb && (
                    <div className="text-sm text-gray-600">
                      Available: {check.available_mb} MB
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* System Information */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">System Information</h3>
          </div>
          <div className="card-body">
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">Version</dt>
                <dd className="text-sm text-gray-900">1.0.0</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Environment</dt>
                <dd className="text-sm text-gray-900">Production</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Uptime</dt>
                <dd className="text-sm text-gray-900">24h 15m</dd>
              </div>
            </dl>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">Performance Metrics</h3>
          </div>
          <div className="card-body">
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">CPU Usage</dt>
                <dd className="text-sm text-gray-900">45%</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Memory Usage</dt>
                <dd className="text-sm text-gray-900">2.1 GB / 8 GB</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Disk Usage</dt>
                <dd className="text-sm text-gray-900">15.5 GB / 100 GB</dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSystemPage;
