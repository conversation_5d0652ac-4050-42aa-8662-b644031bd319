import React, { useState, useEffect } from 'react';
import { apiService } from '../../services/api';
import { PlayIcon, StopIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const LLMPlaygroundPage: React.FC = () => {
  const [model, setModel] = useState('gpt-3.5-turbo');
  const [messages, setMessages] = useState([
    { role: 'user', content: 'Hello! How can you help me today?' }
  ]);
  const [newMessage, setNewMessage] = useState('');
  const [response, setResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [models, setModels] = useState<any[]>([]);

  useEffect(() => {
    loadModels();
  }, []);

  const loadModels = async () => {
    try {
      const data = await apiService.getSupportedModels();
      setModels(data.data || []);
    } catch (error) {
      console.error('Failed to load models:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    const updatedMessages = [...messages, { role: 'user', content: newMessage }];
    setMessages(updatedMessages);
    setNewMessage('');
    setIsLoading(true);
    setResponse('');

    try {
      const result = await apiService.proxyLLMRequest({
        model,
        messages: updatedMessages,
        max_tokens: 1000,
        temperature: 0.7,
      });

      const assistantMessage = result.choices?.[0]?.message?.content || 'No response received';
      setResponse(assistantMessage);
      setMessages([...updatedMessages, { role: 'assistant', content: assistantMessage }]);
      
      toast.success('Response received successfully');
    } catch (error: any) {
      const message = error.response?.data?.error?.message || 'Failed to get response';
      toast.error(message);
      setResponse('Error: ' + message);
    } finally {
      setIsLoading(false);
    }
  };

  const clearConversation = () => {
    setMessages([]);
    setResponse('');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">LLM Playground</h1>
        <p className="mt-1 text-sm text-gray-500">
          Test and experiment with different LLM models
        </p>
      </div>

      {/* Configuration */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium">Configuration</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div>
              <label className="label">Model</label>
              <select
                value={model}
                onChange={(e) => setModel(e.target.value)}
                className="input"
              >
                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                <option value="gpt-4">GPT-4</option>
                <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                {models.map((m) => (
                  <option key={m.id} value={m.id}>{m.id}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="label">Max Tokens</label>
              <input
                type="number"
                defaultValue={1000}
                className="input"
                min={1}
                max={4000}
              />
            </div>
            <div>
              <label className="label">Temperature</label>
              <input
                type="number"
                defaultValue={0.7}
                className="input"
                min={0}
                max={2}
                step={0.1}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Conversation */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input */}
        <div className="card">
          <div className="card-header flex justify-between items-center">
            <h3 className="text-lg font-medium">Input</h3>
            <button
              onClick={clearConversation}
              className="btn-outline btn-sm"
            >
              Clear
            </button>
          </div>
          <div className="card-body">
            <div className="space-y-4">
              {/* Message History */}
              <div className="max-h-60 overflow-y-auto space-y-2">
                {messages.map((msg, idx) => (
                  <div
                    key={idx}
                    className={`p-3 rounded-lg ${
                      msg.role === 'user' 
                        ? 'bg-primary-50 border-l-4 border-primary-500' 
                        : 'bg-gray-50 border-l-4 border-gray-500'
                    }`}
                  >
                    <div className="text-xs font-medium text-gray-500 mb-1 capitalize">
                      {msg.role}
                    </div>
                    <div className="text-sm text-gray-900">{msg.content}</div>
                  </div>
                ))}
              </div>

              {/* New Message Input */}
              <div className="space-y-3">
                <textarea
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type your message here..."
                  className="input min-h-[100px] resize-none"
                  rows={4}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={isLoading || !newMessage.trim()}
                  className="btn-primary w-full"
                >
                  {isLoading ? (
                    <>
                      <div className="loading-spinner mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <PlayIcon className="h-4 w-4 mr-2" />
                      Send Message
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Output */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">Response</h3>
          </div>
          <div className="card-body">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="loading-spinner-lg mx-auto mb-4"></div>
                  <p className="text-gray-500">Generating response...</p>
                </div>
              </div>
            ) : response ? (
              <div className="prose prose-sm max-w-none">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <pre className="whitespace-pre-wrap text-sm text-gray-900 font-sans">
                    {response}
                  </pre>
                </div>
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <PlayIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p>Send a message to see the response here</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Examples */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium">Quick Examples</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                title: 'Code Generation',
                prompt: 'Write a Python function to calculate the factorial of a number',
              },
              {
                title: 'Text Summarization',
                prompt: 'Summarize the key benefits of renewable energy in 3 bullet points',
              },
              {
                title: 'Creative Writing',
                prompt: 'Write a short story about a robot learning to paint',
              },
              {
                title: 'Data Analysis',
                prompt: 'Explain the difference between supervised and unsupervised learning',
              },
              {
                title: 'Translation',
                prompt: 'Translate "Hello, how are you?" to Spanish, French, and German',
              },
              {
                title: 'Problem Solving',
                prompt: 'How would you approach debugging a slow-loading web application?',
              },
            ].map((example, idx) => (
              <button
                key={idx}
                onClick={() => setNewMessage(example.prompt)}
                className="text-left p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
              >
                <h4 className="font-medium text-gray-900 mb-2">{example.title}</h4>
                <p className="text-sm text-gray-600">{example.prompt}</p>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LLMPlaygroundPage;
