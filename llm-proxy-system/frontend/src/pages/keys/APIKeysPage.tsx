import React, { useState, useEffect } from 'react';
import { apiService, APIKey } from '../../services/api';
import {
  PlusIcon,
  KeyIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  EyeIcon,
  EyeSlashIcon,
  TrashIcon,
  PencilIcon,
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const APIKeysPage: React.FC = () => {
  const [keys, setKeys] = useState<APIKey[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingKey, setEditingKey] = useState<APIKey | null>(null);
  const [testingKeys, setTestingKeys] = useState<Set<number>>(new Set());

  useEffect(() => {
    loadAPIKeys();
  }, []);

  const loadAPIKeys = async () => {
    try {
      setIsLoading(true);
      const data = await apiService.getAPIKeys();
      setKeys(data.keys);
    } catch (error) {
      console.error('Failed to load API keys:', error);
      toast.error('Failed to load API keys');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateKey = async (keyData: { provider: string; name: string; api_key: string }) => {
    try {
      const newKey = await apiService.createAPIKey(keyData);
      setKeys([...keys, newKey]);
      setShowCreateModal(false);
      toast.success('API key created successfully');
    } catch (error: any) {
      const message = error.response?.data?.error?.message || 'Failed to create API key';
      toast.error(message);
    }
  };

  const handleUpdateKey = async (id: number, keyData: { name?: string; api_key?: string }) => {
    try {
      const updatedKey = await apiService.updateAPIKey(id, keyData);
      setKeys(keys.map(key => key.id === id ? updatedKey : key));
      setShowEditModal(false);
      setEditingKey(null);
      toast.success('API key updated successfully');
    } catch (error: any) {
      const message = error.response?.data?.error?.message || 'Failed to update API key';
      toast.error(message);
    }
  };

  const handleDeleteKey = async (id: number) => {
    if (!window.confirm('Are you sure you want to delete this API key?')) {
      return;
    }

    try {
      await apiService.deleteAPIKey(id);
      setKeys(keys.filter(key => key.id !== id));
      toast.success('API key deleted successfully');
    } catch (error: any) {
      const message = error.response?.data?.error?.message || 'Failed to delete API key';
      toast.error(message);
    }
  };

  const handleTestKey = async (id: number) => {
    try {
      setTestingKeys(prev => new Set(prev).add(id));
      const updatedKey = await apiService.testAPIKey(id);
      setKeys(keys.map(key => key.id === id ? updatedKey : key));
      toast.success('API key tested successfully');
    } catch (error: any) {
      const message = error.response?.data?.error?.message || 'API key test failed';
      toast.error(message);
    } finally {
      setTestingKeys(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="h-5 w-5 text-success-500" />;
      case 'invalid':
        return <ExclamationTriangleIcon className="h-5 w-5 text-error-500" />;
      case 'testing':
        return <ClockIcon className="h-5 w-5 text-warning-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <span className="badge-success">Active</span>;
      case 'invalid':
        return <span className="badge-error">Invalid</span>;
      case 'testing':
        return <span className="badge-warning">Testing</span>;
      default:
        return <span className="badge-gray">Unknown</span>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="skeleton h-8 w-64"></div>
        <div className="card">
          <div className="card-body space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="skeleton h-10 w-10 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="skeleton h-4 w-1/4"></div>
                  <div className="skeleton h-3 w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">API Keys</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your provider API keys securely
          </p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="btn-primary"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add API Key
        </button>
      </div>

      {/* API Keys List */}
      {keys.length === 0 ? (
        <div className="card">
          <div className="card-body text-center py-12">
            <KeyIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No API keys</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by adding your first provider API key.
            </p>
            <div className="mt-6">
              <button
                onClick={() => setShowCreateModal(true)}
                className="btn-primary"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add API Key
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="card">
          <div className="card-body p-0">
            <div className="overflow-hidden">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th className="table-header-cell">Provider</th>
                    <th className="table-header-cell">Name</th>
                    <th className="table-header-cell">Status</th>
                    <th className="table-header-cell">Usage</th>
                    <th className="table-header-cell">Last Used</th>
                    <th className="table-header-cell">Actions</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {keys.map((key) => (
                    <tr key={key.id}>
                      <td className="table-cell">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8">
                            <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary-800 uppercase">
                                {key.provider.slice(0, 2)}
                              </span>
                            </div>
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900 capitalize">
                              {key.provider}
                            </div>
                            <div className="text-xs text-gray-500 font-mono">
                              {key.masked_api_key}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="text-sm text-gray-900">{key.name}</div>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center">
                          {getStatusIcon(key.status)}
                          <span className="ml-2">{getStatusBadge(key.status)}</span>
                        </div>
                        {key.error_message && (
                          <div className="text-xs text-error-600 mt-1">
                            {key.error_message}
                          </div>
                        )}
                      </td>
                      <td className="table-cell">
                        <div className="text-sm text-gray-900">
                          {key.request_count.toLocaleString()} requests
                        </div>
                        <div className="text-xs text-gray-500">
                          {key.total_tokens_used.toLocaleString()} tokens
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="text-sm text-gray-500">
                          {key.last_used_at ? formatDate(key.last_used_at) : 'Never'}
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleTestKey(key.id)}
                            disabled={testingKeys.has(key.id)}
                            className="btn-outline btn-sm"
                          >
                            {testingKeys.has(key.id) ? (
                              <div className="loading-spinner"></div>
                            ) : (
                              'Test'
                            )}
                          </button>
                          <button
                            onClick={() => {
                              setEditingKey(key);
                              setShowEditModal(true);
                            }}
                            className="btn-outline btn-sm"
                          >
                            <PencilIcon className="h-3 w-3" />
                          </button>
                          <button
                            onClick={() => handleDeleteKey(key.id)}
                            className="btn-danger btn-sm"
                          >
                            <TrashIcon className="h-3 w-3" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Create Modal */}
      {showCreateModal && (
        <CreateAPIKeyModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateKey}
        />
      )}

      {/* Edit Modal */}
      {showEditModal && editingKey && (
        <EditAPIKeyModal
          key={editingKey}
          apiKey={editingKey}
          onClose={() => {
            setShowEditModal(false);
            setEditingKey(null);
          }}
          onSubmit={(data) => handleUpdateKey(editingKey.id, data)}
        />
      )}
    </div>
  );
};

// Create API Key Modal Component
const CreateAPIKeyModal: React.FC<{
  onClose: () => void;
  onSubmit: (data: { provider: string; name: string; api_key: string }) => void;
}> = ({ onClose, onSubmit }) => {
  const [provider, setProvider] = useState('');
  const [name, setName] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [showApiKey, setShowApiKey] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const providers = [
    'openai', 'gemini', 'claude', 'perplexity', 'mistral', 
    'deepseek', 'moonshot', 'ollama', 'aws', 'azure', 'ali'
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!provider || !name || !apiKey) return;

    setIsSubmitting(true);
    try {
      await onSubmit({ provider, name, api_key: apiKey });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />
        
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Add API Key</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="label">Provider</label>
                  <select
                    value={provider}
                    onChange={(e) => setProvider(e.target.value)}
                    className="input"
                    required
                  >
                    <option value="">Select a provider</option>
                    {providers.map(p => (
                      <option key={p} value={p} className="capitalize">{p}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="label">Name</label>
                  <input
                    type="text"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="input"
                    placeholder="e.g., My OpenAI Key"
                    required
                  />
                </div>

                <div>
                  <label className="label">API Key</label>
                  <div className="relative">
                    <input
                      type={showApiKey ? 'text' : 'password'}
                      value={apiKey}
                      onChange={(e) => setApiKey(e.target.value)}
                      className="input pr-10"
                      placeholder="Enter your API key"
                      required
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      {showApiKey ? (
                        <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                      ) : (
                        <EyeIcon className="h-5 w-5 text-gray-400" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={isSubmitting}
                className="btn-primary sm:ml-3"
              >
                {isSubmitting ? <div className="loading-spinner"></div> : 'Add Key'}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="btn-outline mt-3 sm:mt-0"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

// Edit API Key Modal Component
const EditAPIKeyModal: React.FC<{
  apiKey: APIKey;
  onClose: () => void;
  onSubmit: (data: { name?: string; api_key?: string }) => void;
}> = ({ apiKey, onClose, onSubmit }) => {
  const [name, setName] = useState(apiKey.name);
  const [newApiKey, setNewApiKey] = useState('');
  const [showApiKey, setShowApiKey] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const updates: { name?: string; api_key?: string } = {};
    if (name !== apiKey.name) updates.name = name;
    if (newApiKey) updates.api_key = newApiKey;

    if (Object.keys(updates).length === 0) {
      onClose();
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(updates);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />
        
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Edit API Key</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="label">Provider</label>
                  <input
                    type="text"
                    value={apiKey.provider}
                    className="input bg-gray-50"
                    disabled
                  />
                </div>

                <div>
                  <label className="label">Name</label>
                  <input
                    type="text"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="input"
                    required
                  />
                </div>

                <div>
                  <label className="label">New API Key (optional)</label>
                  <div className="relative">
                    <input
                      type={showApiKey ? 'text' : 'password'}
                      value={newApiKey}
                      onChange={(e) => setNewApiKey(e.target.value)}
                      className="input pr-10"
                      placeholder="Enter new API key to update"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      {showApiKey ? (
                        <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                      ) : (
                        <EyeIcon className="h-5 w-5 text-gray-400" />
                      )}
                    </button>
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Leave empty to keep current API key
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={isSubmitting}
                className="btn-primary sm:ml-3"
              >
                {isSubmitting ? <div className="loading-spinner"></div> : 'Update'}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="btn-outline mt-3 sm:mt-0"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default APIKeysPage;
