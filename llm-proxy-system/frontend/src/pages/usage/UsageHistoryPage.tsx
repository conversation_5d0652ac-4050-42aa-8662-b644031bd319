import React from 'react';
import { ChartBarIcon } from '@heroicons/react/24/outline';

const UsageHistoryPage: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Usage History</h1>
        <p className="mt-1 text-sm text-gray-500">
          View your LLM usage analytics and history
        </p>
      </div>

      <div className="card">
        <div className="card-body text-center py-12">
          <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Usage Analytics</h3>
          <p className="mt-1 text-sm text-gray-500">
            Usage history and analytics will be displayed here.
          </p>
        </div>
      </div>
    </div>
  );
};

export default UsageHistoryPage;
