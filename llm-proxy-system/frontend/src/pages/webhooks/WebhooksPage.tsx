import React, { useState, useEffect } from 'react';
import { apiService, Webhook } from '../../services/api';
import { PlusIcon, BellIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const WebhooksPage: React.FC = () => {
  const [webhooks, setWebhooks] = useState<Webhook[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadWebhooks();
  }, []);

  const loadWebhooks = async () => {
    try {
      setIsLoading(true);
      const data = await apiService.getWebhooks();
      setWebhooks(data.webhooks);
    } catch (error) {
      console.error('Failed to load webhooks:', error);
      toast.error('Failed to load webhooks');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="skeleton h-8 w-64"></div>
        <div className="card">
          <div className="card-body">
            <div className="skeleton h-32 w-full"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Webhooks</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage event notifications and integrations
          </p>
        </div>
        <button className="btn-primary">
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Webhook
        </button>
      </div>

      {webhooks.length === 0 ? (
        <div className="card">
          <div className="card-body text-center py-12">
            <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No webhooks</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating your first webhook.
            </p>
            <div className="mt-6">
              <button className="btn-primary">
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Webhook
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {webhooks.map((webhook) => (
            <div key={webhook.id} className="card">
              <div className="card-body">
                <h3 className="text-lg font-medium text-gray-900">{webhook.name}</h3>
                <p className="text-sm text-gray-500 mt-1">{webhook.url}</p>
                <div className="mt-4">
                  <span className={`badge ${webhook.is_active ? 'badge-success' : 'badge-gray'}`}>
                    {webhook.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default WebhooksPage;
