import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/api';
import {
  KeyIcon,
  BellIcon,
  PlayIcon,
  ChartBarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface DashboardStats {
  apiKeys: {
    total: number;
    active: number;
    invalid: number;
    testing: number;
  };
  usage: {
    totalRequests: number;
    totalTokens: number;
    requestsToday: number;
    tokensToday: number;
  };
  webhooks: {
    total: number;
    active: number;
  };
  recentActivity: Array<{
    id: string;
    type: string;
    message: string;
    timestamp: string;
    status: 'success' | 'error' | 'warning';
  }>;
}

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // Load API keys stats
      const keyStats = await apiService.getKeyStats();
      
      // Load webhooks
      const webhooksData = await apiService.getWebhooks();
      
      // Mock usage data (would come from usage history API)
      const mockUsage = {
        totalRequests: 1250,
        totalTokens: 45000,
        requestsToday: 23,
        tokensToday: 890,
      };
      
      // Mock recent activity
      const mockActivity = [
        {
          id: '1',
          type: 'api_key',
          message: 'OpenAI API key tested successfully',
          timestamp: new Date().toISOString(),
          status: 'success' as const,
        },
        {
          id: '2',
          type: 'webhook',
          message: 'Webhook created for Discord notifications',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          status: 'success' as const,
        },
        {
          id: '3',
          type: 'llm_request',
          message: 'LLM request completed (GPT-4)',
          timestamp: new Date(Date.now() - 7200000).toISOString(),
          status: 'success' as const,
        },
      ];

      setStats({
        apiKeys: {
          total: keyStats.total_keys || 0,
          active: keyStats.active_keys || 0,
          invalid: keyStats.invalid_keys || 0,
          testing: keyStats.testing_keys || 0,
        },
        usage: mockUsage,
        webhooks: {
          total: webhooksData.count,
          active: webhooksData.webhooks.filter(w => w.is_active).length,
        },
        recentActivity: mockActivity,
      });
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const quickActions = [
    {
      name: 'Add API Key',
      description: 'Add a new provider API key',
      href: '/keys',
      icon: KeyIcon,
      color: 'bg-primary-500',
    },
    {
      name: 'Create Webhook',
      description: 'Set up event notifications',
      href: '/webhooks',
      icon: BellIcon,
      color: 'bg-success-500',
    },
    {
      name: 'LLM Playground',
      description: 'Test LLM requests',
      href: '/playground',
      icon: PlayIcon,
      color: 'bg-warning-500',
    },
    {
      name: 'View Usage',
      description: 'Check usage analytics',
      href: '/usage',
      icon: ChartBarIcon,
      color: 'bg-purple-500',
    },
  ];

  const getActivityIcon = (type: string, status: string) => {
    if (status === 'success') return CheckCircleIcon;
    if (status === 'error') return ExclamationTriangleIcon;
    return ClockIcon;
  };

  const getActivityColor = (status: string) => {
    if (status === 'success') return 'text-success-600';
    if (status === 'error') return 'text-error-600';
    return 'text-warning-600';
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="skeleton h-8 w-64"></div>
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="card p-6">
              <div className="skeleton h-6 w-20 mb-2"></div>
              <div className="skeleton h-8 w-16"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {user?.email}
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          Here's what's happening with your LLM proxy system today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="stat-card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <KeyIcon className="h-6 w-6 text-primary-600" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="stat-label">API Keys</dt>
                <dd className="stat-value">{stats?.apiKeys.total || 0}</dd>
              </dl>
            </div>
          </div>
          <div className="mt-3 flex text-sm">
            <span className="text-success-600">{stats?.apiKeys.active || 0} active</span>
            {stats?.apiKeys.invalid ? (
              <span className="ml-2 text-error-600">{stats.apiKeys.invalid} invalid</span>
            ) : null}
          </div>
        </div>

        <div className="stat-card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-6 w-6 text-success-600" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="stat-label">Total Requests</dt>
                <dd className="stat-value">{stats?.usage.totalRequests.toLocaleString() || 0}</dd>
              </dl>
            </div>
          </div>
          <div className="mt-3 text-sm text-gray-600">
            {stats?.usage.requestsToday || 0} today
          </div>
        </div>

        <div className="stat-card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <BellIcon className="h-6 w-6 text-warning-600" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="stat-label">Webhooks</dt>
                <dd className="stat-value">{stats?.webhooks.total || 0}</dd>
              </dl>
            </div>
          </div>
          <div className="mt-3 text-sm text-gray-600">
            {stats?.webhooks.active || 0} active
          </div>
        </div>

        <div className="stat-card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <PlayIcon className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="stat-label">Tokens Used</dt>
                <dd className="stat-value">{stats?.usage.totalTokens.toLocaleString() || 0}</dd>
              </dl>
            </div>
          </div>
          <div className="mt-3 text-sm text-gray-600">
            {stats?.usage.tokensToday.toLocaleString() || 0} today
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {quickActions.map((action) => (
            <Link
              key={action.name}
              to={action.href}
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg border border-gray-200 hover:shadow-md transition-shadow"
            >
              <div>
                <span className={`rounded-lg inline-flex p-3 ${action.color} text-white`}>
                  <action.icon className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium text-gray-900">
                  <span className="absolute inset-0" />
                  {action.name}
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  {action.description}
                </p>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h2>
        <div className="card">
          <div className="card-body">
            {stats?.recentActivity.length ? (
              <div className="flow-root">
                <ul className="-mb-8">
                  {stats.recentActivity.map((activity, activityIdx) => {
                    const Icon = getActivityIcon(activity.type, activity.status);
                    return (
                      <li key={activity.id}>
                        <div className="relative pb-8">
                          {activityIdx !== stats.recentActivity.length - 1 ? (
                            <span
                              className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                              aria-hidden="true"
                            />
                          ) : null}
                          <div className="relative flex space-x-3">
                            <div>
                              <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${getActivityColor(activity.status)}`}>
                                <Icon className="h-5 w-5" />
                              </span>
                            </div>
                            <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                              <div>
                                <p className="text-sm text-gray-900">{activity.message}</p>
                              </div>
                              <div className="text-right text-sm whitespace-nowrap text-gray-500">
                                {formatTimeAgo(activity.timestamp)}
                              </div>
                            </div>
                          </div>
                        </div>
                      </li>
                    );
                  })}
                </ul>
              </div>
            ) : (
              <div className="text-center py-6">
                <p className="text-gray-500">No recent activity</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
