import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { UserIcon } from '@heroicons/react/24/outline';

const ProfilePage: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Profile</h1>
        <p className="mt-1 text-sm text-gray-500">
          Manage your account settings and preferences
        </p>
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium">Account Information</h3>
        </div>
        <div className="card-body">
          <div className="space-y-4">
            <div>
              <label className="label">Email</label>
              <input
                type="email"
                value={user?.email || ''}
                className="input bg-gray-50"
                disabled
              />
            </div>
            <div>
              <label className="label">Role</label>
              <input
                type="text"
                value={user?.role || ''}
                className="input bg-gray-50 capitalize"
                disabled
              />
            </div>
            <div>
              <label className="label">System API Key</label>
              <input
                type="text"
                value={user?.system_api_key || ''}
                className="input bg-gray-50 font-mono"
                disabled
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
