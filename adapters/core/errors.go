package core

import (
	"fmt"
	"net/http"
)

// ErrorType represents different types of errors
type ErrorType string

const (
	ErrorTypeInvalidRequest     ErrorType = "invalid_request_error"
	ErrorTypeAuthentication     ErrorType = "authentication_error"
	ErrorTypePermission         ErrorType = "permission_error"
	ErrorTypeNotFound           ErrorType = "not_found_error"
	ErrorTypeRateLimit          ErrorType = "rate_limit_error"
	ErrorTypeQuotaExceeded      ErrorType = "quota_exceeded_error"
	ErrorTypeServerError        ErrorType = "server_error"
	ErrorTypeServiceUnavailable ErrorType = "service_unavailable_error"
	ErrorTypeTimeout            ErrorType = "timeout_error"
	ErrorTypeUnknown            ErrorType = "unknown_error"
)

// AdapterError represents an error from an adapter
type AdapterError struct {
	Type       ErrorType
	Message    string
	StatusCode int
	Provider   string
	Retryable  bool
	Cause      error
}

// Error implements the error interface
func (e *AdapterError) Error() string {
	if e.Provider != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Provider, e.Type, e.Message)
	}
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// Unwrap returns the underlying error
func (e *AdapterError) Unwrap() error {
	return e.Cause
}

// ToOpenAIError converts the adapter error to an OpenAI-compatible error
func (e *AdapterError) ToOpenAIError() *OpenAIError {
	return &OpenAIError{
		Message: e.Message,
		Type:    string(e.Type),
		Code:    e.StatusCode,
	}
}

// NewAdapterError creates a new adapter error
func NewAdapterError(errorType ErrorType, message string, statusCode int, provider string) *AdapterError {
	return &AdapterError{
		Type:       errorType,
		Message:    message,
		StatusCode: statusCode,
		Provider:   provider,
		Retryable:  isRetryableErrorType(errorType),
	}
}

// WrapError wraps an existing error as an adapter error
func WrapError(err error, errorType ErrorType, message string, statusCode int, provider string) *AdapterError {
	return &AdapterError{
		Type:       errorType,
		Message:    message,
		StatusCode: statusCode,
		Provider:   provider,
		Retryable:  isRetryableErrorType(errorType),
		Cause:      err,
	}
}

// isRetryableErrorType determines if an error type is retryable
func isRetryableErrorType(errorType ErrorType) bool {
	switch errorType {
	case ErrorTypeRateLimit, ErrorTypeServerError, ErrorTypeServiceUnavailable, ErrorTypeTimeout:
		return true
	default:
		return false
	}
}

// MapHTTPStatusToErrorType maps HTTP status codes to error types
func MapHTTPStatusToErrorType(statusCode int) ErrorType {
	switch statusCode {
	case http.StatusBadRequest:
		return ErrorTypeInvalidRequest
	case http.StatusUnauthorized:
		return ErrorTypeAuthentication
	case http.StatusForbidden:
		return ErrorTypePermission
	case http.StatusNotFound:
		return ErrorTypeNotFound
	case http.StatusTooManyRequests:
		return ErrorTypeRateLimit
	case http.StatusPaymentRequired:
		return ErrorTypeQuotaExceeded
	case http.StatusInternalServerError, http.StatusBadGateway:
		return ErrorTypeServerError
	case http.StatusServiceUnavailable:
		return ErrorTypeServiceUnavailable
	case http.StatusGatewayTimeout, http.StatusRequestTimeout:
		return ErrorTypeTimeout
	default:
		if statusCode >= 500 {
			return ErrorTypeServerError
		}
		return ErrorTypeUnknown
	}
}

// Common error messages
const (
	ErrMsgInvalidModel       = "invalid model specified"
	ErrMsgMissingAPIKey      = "API key is required"
	ErrMsgInvalidAPIKey      = "invalid API key"
	ErrMsgRateLimitExceeded  = "rate limit exceeded"
	ErrMsgQuotaExceeded      = "quota exceeded"
	ErrMsgServerError        = "internal server error"
	ErrMsgServiceUnavailable = "service temporarily unavailable"
	ErrMsgTimeout            = "request timeout"
	ErrMsgInvalidRequest     = "invalid request format"
	ErrMsgModelNotSupported  = "model not supported by provider"
)

// Predefined errors
var (
	ErrInvalidModel       = NewAdapterError(ErrorTypeInvalidRequest, ErrMsgInvalidModel, http.StatusBadRequest, "")
	ErrMissingAPIKey      = NewAdapterError(ErrorTypeAuthentication, ErrMsgMissingAPIKey, http.StatusUnauthorized, "")
	ErrInvalidAPIKey      = NewAdapterError(ErrorTypeAuthentication, ErrMsgInvalidAPIKey, http.StatusUnauthorized, "")
	ErrRateLimitExceeded  = NewAdapterError(ErrorTypeRateLimit, ErrMsgRateLimitExceeded, http.StatusTooManyRequests, "")
	ErrQuotaExceeded      = NewAdapterError(ErrorTypeQuotaExceeded, ErrMsgQuotaExceeded, http.StatusPaymentRequired, "")
	ErrServerError        = NewAdapterError(ErrorTypeServerError, ErrMsgServerError, http.StatusInternalServerError, "")
	ErrServiceUnavailable = NewAdapterError(ErrorTypeServiceUnavailable, ErrMsgServiceUnavailable, http.StatusServiceUnavailable, "")
	ErrTimeout            = NewAdapterError(ErrorTypeTimeout, ErrMsgTimeout, http.StatusGatewayTimeout, "")
	ErrInvalidRequest     = NewAdapterError(ErrorTypeInvalidRequest, ErrMsgInvalidRequest, http.StatusBadRequest, "")
	ErrModelNotSupported  = NewAdapterError(ErrorTypeInvalidRequest, ErrMsgModelNotSupported, http.StatusBadRequest, "")
)
