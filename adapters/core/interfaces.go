package core

import (
	"context"
	"net/http"
)

// Adapter defines the interface that all provider adapters must implement
type Adapter interface {
	// Provider identification
	GetProviderName() string
	GetSupportedModels() []string
	
	// Request handling
	ConvertRequest(ctx context.Context, request *OpenAIRequest) (interface{}, error)
	BuildRequestURL(baseURL string, request *OpenAIRequest) (string, error)
	SetupHeaders(headers map[string]string, apiKey string) error
	
	// Response handling
	ExecuteRequest(ctx context.Context, httpClient *http.Client, url string, headers map[string]string, body []byte) (*http.Response, error)
	ConvertResponse(ctx context.Context, response *http.Response, isStream bool) (*OpenAIResponse, error)
	
	// Error handling
	HandleError(response *http.Response) error
}

// AdapterConfig holds configuration for an adapter
type AdapterConfig struct {
	APIKey      string
	BaseURL     string
	HTTPClient  *http.Client
	ExtraParams map[string]interface{}
}

// AdapterManager manages multiple adapters and provides routing
type AdapterManager interface {
	RegisterAdapter(providerName string, adapter Adapter) error
	GetAdapter(providerName string) (Adapter, error)
	ListProviders() []string
	RouteRequest(request *OpenAIRequest) (Adapter, error)
}

// StreamHandler handles streaming responses
type StreamHandler interface {
	HandleStream(ctx context.Context, response *http.Response, callback func(chunk *StreamChunk) error) error
}

// StreamChunk represents a chunk of streaming data
type StreamChunk struct {
	Data     []byte
	IsLast   bool
	Error    error
	Metadata map[string]interface{}
}

// RequestValidator validates OpenAI requests
type RequestValidator interface {
	ValidateRequest(request *OpenAIRequest) error
	ValidateModel(model string, provider string) error
}

// ErrorHandler handles and converts provider-specific errors
type ErrorHandler interface {
	ConvertError(providerError error) *OpenAIError
	IsRetryableError(err error) bool
	GetRetryDelay(attempt int) int
}
