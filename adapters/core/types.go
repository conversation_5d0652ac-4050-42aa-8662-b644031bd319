package core

import (
	"encoding/json"
	"time"
)

// OpenAIRequest represents a standardized OpenAI-compatible request
type OpenAIRequest struct {
	Model            string                 `json:"model"`
	Messages         []Message              `json:"messages,omitempty"`
	Prompt           interface{}            `json:"prompt,omitempty"`
	Stream           bool                   `json:"stream,omitempty"`
	StreamOptions    *StreamOptions         `json:"stream_options,omitempty"`
	MaxTokens        *int                   `json:"max_tokens,omitempty"`
	Temperature      *float64               `json:"temperature,omitempty"`
	TopP             *float64               `json:"top_p,omitempty"`
	TopK             *int                   `json:"top_k,omitempty"`
	FrequencyPenalty *float64               `json:"frequency_penalty,omitempty"`
	PresencePenalty  *float64               `json:"presence_penalty,omitempty"`
	Stop             interface{}            `json:"stop,omitempty"`
	N                *int                   `json:"n,omitempty"`
	Tools            []Tool                 `json:"tools,omitempty"`
	ToolChoice       interface{}            `json:"tool_choice,omitempty"`
	Functions        []Function             `json:"functions,omitempty"`
	FunctionCall     interface{}            `json:"function_call,omitempty"`
	User             string                 `json:"user,omitempty"`
	Seed             *int                   `json:"seed,omitempty"`
	ExtraParams      map[string]interface{} `json:"-"`
}

// Message represents a chat message
type Message struct {
	Role         string          `json:"role"`
	Content      json.RawMessage `json:"content"`
	Name         *string         `json:"name,omitempty"`
	ToolCalls    json.RawMessage `json:"tool_calls,omitempty"`
	ToolCallID   string          `json:"tool_call_id,omitempty"`
	FunctionCall *FunctionCall   `json:"function_call,omitempty"`
}

// ParseContent parses the content field into MediaContent array
func (m *Message) ParseContent() ([]MediaContent, error) {
	var content []MediaContent
	
	// Try to parse as string first
	var stringContent string
	if err := json.Unmarshal(m.Content, &stringContent); err == nil {
		return []MediaContent{{Type: "text", Text: stringContent}}, nil
	}
	
	// Try to parse as array of MediaContent
	if err := json.Unmarshal(m.Content, &content); err != nil {
		return nil, err
	}
	
	return content, nil
}

// GetStringContent returns content as a string
func (m *Message) GetStringContent() string {
	var stringContent string
	if err := json.Unmarshal(m.Content, &stringContent); err == nil {
		return stringContent
	}
	return ""
}

// MediaContent represents different types of content in a message
type MediaContent struct {
	Type     string    `json:"type"`
	Text     string    `json:"text,omitempty"`
	ImageURL *ImageURL `json:"image_url,omitempty"`
}

// ImageURL represents an image URL with optional detail level
type ImageURL struct {
	URL    string `json:"url"`
	Detail string `json:"detail,omitempty"`
}

// Tool represents a function tool
type Tool struct {
	Type     string   `json:"type"`
	Function Function `json:"function"`
}

// Function represents a function definition
type Function struct {
	Name        string      `json:"name"`
	Description string      `json:"description,omitempty"`
	Parameters  interface{} `json:"parameters,omitempty"`
}

// FunctionCall represents a function call
type FunctionCall struct {
	Name      string `json:"name"`
	Arguments string `json:"arguments"`
}

// ToolCall represents a tool call
type ToolCall struct {
	ID       string       `json:"id"`
	Type     string       `json:"type"`
	Function FunctionCall `json:"function"`
}

// StreamOptions configures streaming behavior
type StreamOptions struct {
	IncludeUsage bool `json:"include_usage,omitempty"`
}

// OpenAIResponse represents a standardized OpenAI-compatible response
type OpenAIResponse struct {
	ID                string    `json:"id"`
	Object            string    `json:"object"`
	Created           int64     `json:"created"`
	Model             string    `json:"model"`
	Choices           []Choice  `json:"choices"`
	Usage             *Usage    `json:"usage,omitempty"`
	SystemFingerprint string    `json:"system_fingerprint,omitempty"`
	Error             *OpenAIError `json:"error,omitempty"`
}

// Choice represents a response choice
type Choice struct {
	Index        int      `json:"index"`
	Message      *Message `json:"message,omitempty"`
	Delta        *Message `json:"delta,omitempty"`
	FinishReason *string  `json:"finish_reason,omitempty"`
	Logprobs     interface{} `json:"logprobs,omitempty"`
}

// Usage represents token usage information
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// OpenAIError represents an OpenAI-compatible error
type OpenAIError struct {
	Message string      `json:"message"`
	Type    string      `json:"type"`
	Param   *string     `json:"param,omitempty"`
	Code    interface{} `json:"code,omitempty"`
}

// Error implements the error interface
func (e *OpenAIError) Error() string {
	return e.Message
}

// RequestContext holds context information for a request
type RequestContext struct {
	RequestID   string
	UserID      string
	StartTime   time.Time
	Provider    string
	Model       string
	IsStream    bool
	Metadata    map[string]interface{}
}

// ResponseMetadata holds metadata about the response
type ResponseMetadata struct {
	Provider      string
	Model         string
	Duration      time.Duration
	TokensUsed    *Usage
	StatusCode    int
	Headers       map[string]string
	Cached        bool
}
