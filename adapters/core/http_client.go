package core

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// HTTPClient provides HTTP client functionality for adapters
type HTTPClient struct {
	client  *http.Client
	timeout time.Duration
}

// NewHTTPClient creates a new HTTP client with default settings
func NewHTTPClient(timeout time.Duration) *HTTPClient {
	if timeout == 0 {
		timeout = 30 * time.Second
	}
	
	return &HTTPClient{
		client: &http.Client{
			Timeout: timeout,
		},
		timeout: timeout,
	}
}

// NewHTTPClientWithClient creates an HTTP client wrapper with a custom http.Client
func NewHTTPClientWithClient(client *http.Client) *HTTPClient {
	return &HTTPClient{
		client:  client,
		timeout: client.Timeout,
	}
}

// DoRequest executes an HTTP request with the given parameters
func (h *HTTPClient) DoRequest(ctx context.Context, method, url string, headers map[string]string, body []byte) (*http.Response, error) {
	var bodyReader io.Reader
	if body != nil {
		bodyReader = bytes.NewReader(body)
	}
	
	req, err := http.NewRequestWithContext(ctx, method, url, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	
	// Set headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}
	
	// Set default content type if not specified
	if req.Header.Get("Content-Type") == "" && body != nil {
		req.Header.Set("Content-Type", "application/json")
	}
	
	resp, err := h.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	
	return resp, nil
}

// DoJSONRequest executes a JSON request and returns the response
func (h *HTTPClient) DoJSONRequest(ctx context.Context, method, url string, headers map[string]string, requestBody interface{}) (*http.Response, error) {
	var body []byte
	var err error
	
	if requestBody != nil {
		body, err = json.Marshal(requestBody)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
	}
	
	if headers == nil {
		headers = make(map[string]string)
	}
	headers["Content-Type"] = "application/json"
	
	return h.DoRequest(ctx, method, url, headers, body)
}

// ReadResponseBody reads and returns the response body
func ReadResponseBody(resp *http.Response) ([]byte, error) {
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	
	return body, nil
}

// ParseJSONResponse reads and parses a JSON response
func ParseJSONResponse(resp *http.Response, target interface{}) error {
	body, err := ReadResponseBody(resp)
	if err != nil {
		return err
	}
	
	if err := json.Unmarshal(body, target); err != nil {
		return fmt.Errorf("failed to parse JSON response: %w", err)
	}
	
	return nil
}

// IsSuccessStatusCode checks if the status code indicates success
func IsSuccessStatusCode(statusCode int) bool {
	return statusCode >= 200 && statusCode < 300
}

// BuildURL constructs a URL with path parameters
func BuildURL(baseURL, path string) string {
	if baseURL == "" {
		return path
	}
	
	// Remove trailing slash from baseURL
	if len(baseURL) > 0 && baseURL[len(baseURL)-1] == '/' {
		baseURL = baseURL[:len(baseURL)-1]
	}
	
	// Ensure path starts with slash
	if len(path) > 0 && path[0] != '/' {
		path = "/" + path
	}
	
	return baseURL + path
}

// SetupCommonHeaders sets up common headers for API requests
func SetupCommonHeaders(headers map[string]string, apiKey string) {
	if headers == nil {
		headers = make(map[string]string)
	}
	
	headers["User-Agent"] = "llm-adapter/1.0"
	headers["Accept"] = "application/json"
	
	if apiKey != "" {
		headers["Authorization"] = "Bearer " + apiKey
	}
}

// StreamReader provides utilities for reading streaming responses
type StreamReader struct {
	resp   *http.Response
	reader io.Reader
}

// NewStreamReader creates a new stream reader
func NewStreamReader(resp *http.Response) *StreamReader {
	return &StreamReader{
		resp:   resp,
		reader: resp.Body,
	}
}

// ReadLine reads a line from the stream
func (s *StreamReader) ReadLine() ([]byte, error) {
	var line []byte
	buf := make([]byte, 1)
	
	for {
		n, err := s.reader.Read(buf)
		if err != nil {
			if err == io.EOF && len(line) > 0 {
				return line, nil
			}
			return nil, err
		}
		
		if n > 0 {
			if buf[0] == '\n' {
				return line, nil
			}
			if buf[0] != '\r' {
				line = append(line, buf[0])
			}
		}
	}
}

// Close closes the stream reader
func (s *StreamReader) Close() error {
	return s.resp.Body.Close()
}

// RetryConfig configures retry behavior
type RetryConfig struct {
	MaxRetries int
	BaseDelay  time.Duration
	MaxDelay   time.Duration
}

// DefaultRetryConfig returns a default retry configuration
func DefaultRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxRetries: 3,
		BaseDelay:  1 * time.Second,
		MaxDelay:   10 * time.Second,
	}
}

// CalculateRetryDelay calculates the delay for a retry attempt
func (r *RetryConfig) CalculateRetryDelay(attempt int) time.Duration {
	delay := r.BaseDelay * time.Duration(1<<uint(attempt)) // Exponential backoff
	if delay > r.MaxDelay {
		delay = r.MaxDelay
	}
	return delay
}
