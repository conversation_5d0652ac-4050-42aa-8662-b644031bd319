package mistral

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"../../core"
)

// Adapter implements the Mistral adapter
type Adapter struct {
	config *core.AdapterConfig
}

// NewAdapter creates a new Mistral adapter
func NewAdapter(config *core.AdapterConfig) *Adapter {
	if config.BaseURL == "" {
		config.BaseURL = "https://api.mistral.ai/v1"
	}
	
	return &Adapter{
		config: config,
	}
}

// GetProviderName returns the provider name
func (a *Adapter) GetProviderName() string {
	return "mistral"
}

// GetSupportedModels returns the list of supported models
func (a *Adapter) GetSupportedModels() []string {
	return []string{
		"open-mistral-7b",
		"open-mixtral-8x7b",
		"open-mixtral-8x22b",
		"mistral-small-latest",
		"mistral-medium-latest",
		"mistral-large-latest",
		"mistral-embed",
		"codestral-latest",
		"codestral-mamba-latest",
		"mistral-moderation-latest",
		"pixtral-12b-2409",
		"pixtral-large-latest",
	}
}

// ConvertRequest converts an OpenAI request to Mistral format
func (a *Adapter) ConvertRequest(ctx context.Context, request *core.OpenAIRequest) (interface{}, error) {
	if request == nil {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, "request cannot be nil", http.StatusBadRequest, a.GetProviderName())
	}
	
	// Validate the model
	if !a.isModelSupported(request.Model) {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, fmt.Sprintf("model %s is not supported", request.Model), http.StatusBadRequest, a.GetProviderName())
	}
	
	// Handle embedding requests
	if strings.Contains(request.Model, "embed") {
		return a.convertEmbeddingRequest(request)
	}
	
	// Mistral uses OpenAI format with some modifications
	mistralRequest := &MistralRequest{
		Model:            request.Model,
		Messages:         request.Messages,
		MaxTokens:        request.MaxTokens,
		Temperature:      request.Temperature,
		TopP:             request.TopP,
		Stream:           request.Stream,
		RandomSeed:       request.Seed,
	}

	// Handle stop sequences
	if request.Stop != nil {
		switch stop := request.Stop.(type) {
		case string:
			mistralRequest.Stop = []string{stop}
		case []string:
			mistralRequest.Stop = stop
		case []interface{}:
			stopSeqs := make([]string, 0, len(stop))
			for _, s := range stop {
				if str, ok := s.(string); ok {
					stopSeqs = append(stopSeqs, str)
				}
			}
			mistralRequest.Stop = stopSeqs
		}
	}

	// Handle tools/functions
	if len(request.Tools) > 0 {
		tools := make([]MistralTool, len(request.Tools))
		for i, tool := range request.Tools {
			tools[i] = MistralTool{
				Type:     tool.Type,
				Function: tool.Function,
			}
		}
		mistralRequest.Tools = tools
	}

	// Handle tool choice
	if request.ToolChoice != nil {
		mistralRequest.ToolChoice = request.ToolChoice
	}

	// Mistral-specific parameters
	mistralRequest.SafePrompt = false // Can be configured

	return mistralRequest, nil
}

// convertEmbeddingRequest converts an embedding request
func (a *Adapter) convertEmbeddingRequest(request *core.OpenAIRequest) (*MistralEmbeddingRequest, error) {
	embeddingRequest := &MistralEmbeddingRequest{
		Model: request.Model,
	}

	// Extract input from messages or prompt
	if len(request.Messages) > 0 {
		inputs := make([]string, len(request.Messages))
		for i, message := range request.Messages {
			inputs[i] = message.GetStringContent()
		}
		embeddingRequest.Input = inputs
	} else if request.Prompt != nil {
		switch prompt := request.Prompt.(type) {
		case string:
			embeddingRequest.Input = []string{prompt}
		case []string:
			embeddingRequest.Input = prompt
		case []interface{}:
			inputs := make([]string, len(prompt))
			for i, p := range prompt {
				if str, ok := p.(string); ok {
					inputs[i] = str
				}
			}
			embeddingRequest.Input = inputs
		}
	}

	embeddingRequest.EncodingFormat = "float"

	return embeddingRequest, nil
}

// BuildRequestURL builds the request URL for the given request
func (a *Adapter) BuildRequestURL(baseURL string, request *core.OpenAIRequest) (string, error) {
	if baseURL == "" {
		baseURL = a.config.BaseURL
	}
	
	var endpoint string
	if strings.Contains(request.Model, "embed") {
		endpoint = "/embeddings"
	} else {
		endpoint = "/chat/completions"
	}
	
	return core.BuildURL(baseURL, endpoint), nil
}

// SetupHeaders sets up the request headers
func (a *Adapter) SetupHeaders(headers map[string]string, apiKey string) error {
	if apiKey == "" {
		apiKey = a.config.APIKey
	}
	
	if apiKey == "" {
		return core.NewAdapterError(core.ErrorTypeAuthentication, "API key is required", http.StatusUnauthorized, a.GetProviderName())
	}
	
	core.SetupCommonHeaders(headers, apiKey)
	
	return nil
}

// ExecuteRequest executes the HTTP request
func (a *Adapter) ExecuteRequest(ctx context.Context, httpClient *http.Client, url string, headers map[string]string, body []byte) (*http.Response, error) {
	if httpClient == nil {
		httpClient = a.config.HTTPClient
	}
	
	if httpClient == nil {
		httpClient = &http.Client{}
	}
	
	client := core.NewHTTPClientWithClient(httpClient)
	return client.DoRequest(ctx, "POST", url, headers, body)
}

// ConvertResponse converts the Mistral response to OpenAI format
func (a *Adapter) ConvertResponse(ctx context.Context, response *http.Response, isStream bool) (*core.OpenAIResponse, error) {
	if !core.IsSuccessStatusCode(response.StatusCode) {
		return nil, a.HandleError(response)
	}
	
	if isStream {
		// For streaming responses, we need to handle them differently
		// This is a simplified implementation - in practice, you'd want to handle SSE properly
		return nil, core.NewAdapterError(core.ErrorTypeServerError, "streaming not implemented in this example", http.StatusNotImplemented, a.GetProviderName())
	}
	
	var mistralResponse MistralResponse
	if err := core.ParseJSONResponse(response, &mistralResponse); err != nil {
		return nil, core.WrapError(err, core.ErrorTypeServerError, "failed to parse Mistral response", http.StatusInternalServerError, a.GetProviderName())
	}
	
	// Convert Mistral response to OpenAI format
	openaiResponse := &core.OpenAIResponse{
		ID:      mistralResponse.ID,
		Object:  mistralResponse.Object,
		Created: mistralResponse.Created,
		Model:   mistralResponse.Model,
		Choices: make([]core.Choice, len(mistralResponse.Choices)),
		Usage:   mistralResponse.Usage,
	}

	for i, choice := range mistralResponse.Choices {
		openaiResponse.Choices[i] = core.Choice{
			Index:        choice.Index,
			Message:      choice.Message,
			FinishReason: choice.FinishReason,
		}
	}

	return openaiResponse, nil
}

// HandleError handles Mistral-specific errors
func (a *Adapter) HandleError(response *http.Response) error {
	body, err := core.ReadResponseBody(response)
	if err != nil {
		return core.WrapError(err, core.ErrorTypeServerError, "failed to read error response", response.StatusCode, a.GetProviderName())
	}
	
	var errorResponse struct {
		Error *core.OpenAIError `json:"error"`
	}
	
	if err := json.Unmarshal(body, &errorResponse); err != nil {
		// If we can't parse the error, return a generic error
		return core.NewAdapterError(
			core.MapHTTPStatusToErrorType(response.StatusCode),
			string(body),
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	if errorResponse.Error != nil {
		return core.NewAdapterError(
			core.ErrorType(errorResponse.Error.Type),
			errorResponse.Error.Message,
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	return core.NewAdapterError(
		core.MapHTTPStatusToErrorType(response.StatusCode),
		"unknown error",
		response.StatusCode,
		a.GetProviderName(),
	)
}

// isModelSupported checks if the model is supported by this adapter
func (a *Adapter) isModelSupported(model string) bool {
	supportedModels := a.GetSupportedModels()
	for _, supportedModel := range supportedModels {
		if model == supportedModel {
			return true
		}
		// Also check for model prefixes
		if strings.HasPrefix(model, supportedModel) {
			return true
		}
	}
	return false
}
