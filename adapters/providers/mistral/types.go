package mistral

import (
	"../../core"
)

// MistralRequest represents a Mistral API request
type MistralRequest struct {
	Model       string           `json:"model"`
	Messages    []core.Message   `json:"messages"`
	MaxTokens   *int             `json:"max_tokens,omitempty"`
	Temperature *float64         `json:"temperature,omitempty"`
	TopP        *float64         `json:"top_p,omitempty"`
	Stream      bool             `json:"stream,omitempty"`
	Stop        []string         `json:"stop,omitempty"`
	RandomSeed  *int             `json:"random_seed,omitempty"`
	Tools       []MistralTool    `json:"tools,omitempty"`
	ToolChoice  interface{}      `json:"tool_choice,omitempty"`
	SafePrompt  bool             `json:"safe_prompt,omitempty"`
}

// MistralTool represents a tool definition
type MistralTool struct {
	Type     string        `json:"type"`
	Function core.Function `json:"function"`
}

// MistralResponse represents a Mistral API response
type MistralResponse struct {
	ID      string           `json:"id"`
	Object  string           `json:"object"`
	Created int64            `json:"created"`
	Model   string           `json:"model"`
	Choices []MistralChoice  `json:"choices"`
	Usage   *core.Usage      `json:"usage,omitempty"`
}

// MistralChoice represents a choice in the response
type MistralChoice struct {
	Index        int           `json:"index"`
	Message      *core.Message `json:"message,omitempty"`
	Delta        *core.Message `json:"delta,omitempty"`
	FinishReason *string       `json:"finish_reason,omitempty"`
}

// MistralStreamResponse represents a streaming response chunk
type MistralStreamResponse struct {
	ID      string           `json:"id"`
	Object  string           `json:"object"`
	Created int64            `json:"created"`
	Model   string           `json:"model"`
	Choices []MistralChoice  `json:"choices"`
}

// MistralEmbeddingRequest represents an embedding request
type MistralEmbeddingRequest struct {
	Model          string   `json:"model"`
	Input          []string `json:"input"`
	EncodingFormat string   `json:"encoding_format,omitempty"`
}

// MistralEmbeddingResponse represents an embedding response
type MistralEmbeddingResponse struct {
	ID     string                  `json:"id"`
	Object string                  `json:"object"`
	Data   []MistralEmbeddingData  `json:"data"`
	Model  string                  `json:"model"`
	Usage  *core.Usage             `json:"usage,omitempty"`
}

// MistralEmbeddingData represents embedding data
type MistralEmbeddingData struct {
	Object    string    `json:"object"`
	Embedding []float64 `json:"embedding"`
	Index     int       `json:"index"`
}

// MistralError represents a Mistral API error
type MistralError struct {
	Error struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    string `json:"code"`
	} `json:"error"`
}

// Model categories
const (
	ModelCategoryOpen        = "open"
	ModelCategoryCommercial  = "commercial"
	ModelCategoryEmbedding   = "embedding"
	ModelCategoryCode        = "code"
	ModelCategoryModeration  = "moderation"
	ModelCategoryVision      = "vision"
)

// Encoding formats for embeddings
const (
	EncodingFormatFloat = "float"
	EncodingFormatBase64 = "base64"
)

// Tool choice options
const (
	ToolChoiceAuto = "auto"
	ToolChoiceNone = "none"
)

// IsEmbeddingModel checks if the model is an embedding model
func IsEmbeddingModel(model string) bool {
	return strings.Contains(model, "embed")
}

// IsCodeModel checks if the model is a code model
func IsCodeModel(model string) bool {
	return strings.Contains(model, "codestral")
}

// IsModerationModel checks if the model is a moderation model
func IsModerationModel(model string) bool {
	return strings.Contains(model, "moderation")
}

// IsVisionModel checks if the model supports vision
func IsVisionModel(model string) bool {
	return strings.Contains(model, "pixtral")
}
