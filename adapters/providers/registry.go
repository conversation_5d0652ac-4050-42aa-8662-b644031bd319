package providers

import (
	"fmt"
	"strings"
	"sync"

	"../core"
	"./ali"
	"./aws"
	"./azure"
	"./claude"
	"./deepseek"
	"./gemini"
	"./mistral"
	"./moonshot"
	"./ollama"
	"./openai"
	"./perplexity"
)

// Registry manages all available adapters
type Registry struct {
	adapters map[string]core.Adapter
	mutex    sync.RWMutex
}

// NewRegistry creates a new adapter registry
func NewRegistry() *Registry {
	return &Registry{
		adapters: make(map[string]core.Adapter),
	}
}

// RegisterAdapter registers an adapter with the given provider name
func (r *Registry) RegisterAdapter(providerName string, adapter core.Adapter) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	if adapter == nil {
		return fmt.Errorf("adapter cannot be nil")
	}
	
	providerName = strings.ToLower(providerName)
	r.adapters[providerName] = adapter
	
	return nil
}

// GetAdapter retrieves an adapter by provider name
func (r *Registry) GetAdapter(providerName string) (core.Adapter, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	providerName = strings.ToLower(providerName)
	adapter, exists := r.adapters[providerName]
	if !exists {
		return nil, fmt.Errorf("adapter for provider '%s' not found", providerName)
	}
	
	return adapter, nil
}

// ListProviders returns a list of all registered provider names
func (r *Registry) ListProviders() []string {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	providers := make([]string, 0, len(r.adapters))
	for providerName := range r.adapters {
		providers = append(providers, providerName)
	}
	
	return providers
}

// RouteRequest determines which adapter to use for a given request
func (r *Registry) RouteRequest(request *core.OpenAIRequest) (core.Adapter, error) {
	if request == nil {
		return nil, fmt.Errorf("request cannot be nil")
	}
	
	// Simple routing based on model name
	providerName := r.detectProviderFromModel(request.Model)
	if providerName == "" {
		return nil, fmt.Errorf("unable to determine provider for model: %s", request.Model)
	}
	
	return r.GetAdapter(providerName)
}

// detectProviderFromModel attempts to detect the provider based on the model name
func (r *Registry) detectProviderFromModel(model string) string {
	model = strings.ToLower(model)
	
	// Check each registered adapter to see if it supports the model
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	for providerName, adapter := range r.adapters {
		supportedModels := adapter.GetSupportedModels()
		for _, supportedModel := range supportedModels {
			if model == strings.ToLower(supportedModel) {
				return providerName
			}
			// Check for prefix matches
			if strings.HasPrefix(model, strings.ToLower(supportedModel)) {
				return providerName
			}
		}
	}
	
	// Fallback to pattern matching
	if strings.Contains(model, "gpt") || strings.Contains(model, "text-embedding") || strings.Contains(model, "whisper") || strings.Contains(model, "dall-e") || strings.Contains(model, "tts") {
		return "openai"
	}
	
	if strings.Contains(model, "gemini") || strings.Contains(model, "embedding-001") {
		return "gemini"
	}
	
	if strings.Contains(model, "claude") {
		return "claude"
	}
	
	return ""
}

// DefaultRegistry creates a registry with all built-in adapters
func DefaultRegistry() *Registry {
	registry := NewRegistry()

	// Register built-in adapters with default configurations
	registry.RegisterAdapter("openai", openai.NewAdapter(&core.AdapterConfig{}))
	registry.RegisterAdapter("gemini", gemini.NewAdapter(&core.AdapterConfig{}))
	registry.RegisterAdapter("claude", claude.NewAdapter(&core.AdapterConfig{}))
	registry.RegisterAdapter("perplexity", perplexity.NewAdapter(&core.AdapterConfig{}))
	registry.RegisterAdapter("mistral", mistral.NewAdapter(&core.AdapterConfig{}))
	registry.RegisterAdapter("deepseek", deepseek.NewAdapter(&core.AdapterConfig{}))
	registry.RegisterAdapter("moonshot", moonshot.NewAdapter(&core.AdapterConfig{}))
	registry.RegisterAdapter("ollama", ollama.NewAdapter(&core.AdapterConfig{}))
	registry.RegisterAdapter("aws", aws.NewAdapter(&core.AdapterConfig{}))
	registry.RegisterAdapter("azure", azure.NewAdapter(&core.AdapterConfig{}))
	registry.RegisterAdapter("ali", ali.NewAdapter(&core.AdapterConfig{}))

	return registry
}

// AdapterManager implements the core.AdapterManager interface
type AdapterManager struct {
	registry *Registry
}

// NewAdapterManager creates a new adapter manager
func NewAdapterManager() *AdapterManager {
	return &AdapterManager{
		registry: DefaultRegistry(),
	}
}

// NewAdapterManagerWithRegistry creates a new adapter manager with a custom registry
func NewAdapterManagerWithRegistry(registry *Registry) *AdapterManager {
	return &AdapterManager{
		registry: registry,
	}
}

// RegisterAdapter registers an adapter
func (am *AdapterManager) RegisterAdapter(providerName string, adapter core.Adapter) error {
	return am.registry.RegisterAdapter(providerName, adapter)
}

// GetAdapter retrieves an adapter
func (am *AdapterManager) GetAdapter(providerName string) (core.Adapter, error) {
	return am.registry.GetAdapter(providerName)
}

// ListProviders lists all providers
func (am *AdapterManager) ListProviders() []string {
	return am.registry.ListProviders()
}

// RouteRequest routes a request to the appropriate adapter
func (am *AdapterManager) RouteRequest(request *core.OpenAIRequest) (core.Adapter, error) {
	return am.registry.RouteRequest(request)
}

// AdapterFactory provides factory methods for creating adapters
type AdapterFactory struct{}

// NewAdapterFactory creates a new adapter factory
func NewAdapterFactory() *AdapterFactory {
	return &AdapterFactory{}
}

// CreateOpenAIAdapter creates an OpenAI adapter with the given configuration
func (f *AdapterFactory) CreateOpenAIAdapter(config *core.AdapterConfig) core.Adapter {
	return openai.NewAdapter(config)
}

// CreateGeminiAdapter creates a Gemini adapter with the given configuration
func (f *AdapterFactory) CreateGeminiAdapter(config *core.AdapterConfig) core.Adapter {
	return gemini.NewAdapter(config)
}

// CreateClaudeAdapter creates a Claude adapter with the given configuration
func (f *AdapterFactory) CreateClaudeAdapter(config *core.AdapterConfig) core.Adapter {
	return claude.NewAdapter(config)
}

// CreatePerplexityAdapter creates a Perplexity adapter with the given configuration
func (f *AdapterFactory) CreatePerplexityAdapter(config *core.AdapterConfig) core.Adapter {
	return perplexity.NewAdapter(config)
}

// CreateMistralAdapter creates a Mistral adapter with the given configuration
func (f *AdapterFactory) CreateMistralAdapter(config *core.AdapterConfig) core.Adapter {
	return mistral.NewAdapter(config)
}

// CreateDeepseekAdapter creates a Deepseek adapter with the given configuration
func (f *AdapterFactory) CreateDeepseekAdapter(config *core.AdapterConfig) core.Adapter {
	return deepseek.NewAdapter(config)
}

// CreateMoonshotAdapter creates a Moonshot adapter with the given configuration
func (f *AdapterFactory) CreateMoonshotAdapter(config *core.AdapterConfig) core.Adapter {
	return moonshot.NewAdapter(config)
}

// CreateOllamaAdapter creates an Ollama adapter with the given configuration
func (f *AdapterFactory) CreateOllamaAdapter(config *core.AdapterConfig) core.Adapter {
	return ollama.NewAdapter(config)
}

// CreateAWSAdapter creates an AWS Bedrock adapter with the given configuration
func (f *AdapterFactory) CreateAWSAdapter(config *core.AdapterConfig) core.Adapter {
	return aws.NewAdapter(config)
}

// CreateAzureAdapter creates an Azure OpenAI adapter with the given configuration
func (f *AdapterFactory) CreateAzureAdapter(config *core.AdapterConfig) core.Adapter {
	return azure.NewAdapter(config)
}

// CreateAliAdapter creates an Alibaba adapter with the given configuration
func (f *AdapterFactory) CreateAliAdapter(config *core.AdapterConfig) core.Adapter {
	return ali.NewAdapter(config)
}

// CreateAdapter creates an adapter for the specified provider
func (f *AdapterFactory) CreateAdapter(providerName string, config *core.AdapterConfig) (core.Adapter, error) {
	switch strings.ToLower(providerName) {
	case "openai":
		return f.CreateOpenAIAdapter(config), nil
	case "gemini":
		return f.CreateGeminiAdapter(config), nil
	case "claude":
		return f.CreateClaudeAdapter(config), nil
	case "perplexity":
		return f.CreatePerplexityAdapter(config), nil
	case "mistral":
		return f.CreateMistralAdapter(config), nil
	case "deepseek":
		return f.CreateDeepseekAdapter(config), nil
	case "moonshot":
		return f.CreateMoonshotAdapter(config), nil
	case "ollama":
		return f.CreateOllamaAdapter(config), nil
	case "aws", "bedrock":
		return f.CreateAWSAdapter(config), nil
	case "azure":
		return f.CreateAzureAdapter(config), nil
	case "ali", "alibaba":
		return f.CreateAliAdapter(config), nil
	default:
		return nil, fmt.Errorf("unsupported provider: %s", providerName)
	}
}

// GetSupportedProviders returns a list of all supported providers
func (f *AdapterFactory) GetSupportedProviders() []string {
	return []string{
		"openai", "gemini", "claude", "perplexity", "mistral",
		"deepseek", "moonshot", "ollama", "aws", "azure", "ali",
	}
}
