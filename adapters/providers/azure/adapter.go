package azure

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"../../core"
)

// Adapter implements the Azure OpenAI adapter
type Adapter struct {
	config *core.AdapterConfig
}

// NewAdapter creates a new Azure OpenAI adapter
func NewAdapter(config *core.AdapterConfig) *Adapter {
	// Azure OpenAI requires a custom base URL with resource name
	// Format: https://{resource-name}.openai.azure.com
	if config.BaseURL == "" {
		config.BaseURL = "https://your-resource.openai.azure.com"
	}
	
	return &Adapter{
		config: config,
	}
}

// GetProviderName returns the provider name
func (a *Adapter) GetProviderName() string {
	return "azure"
}

// GetSupportedModels returns the list of supported models
func (a *Adapter) GetSupportedModels() []string {
	return []string{
		"gpt-4o",
		"gpt-4o-mini",
		"gpt-4-turbo",
		"gpt-4",
		"gpt-4-32k",
		"gpt-35-turbo",
		"gpt-35-turbo-16k",
		"gpt-35-turbo-instruct",
		"text-embedding-ada-002",
		"text-embedding-3-large",
		"text-embedding-3-small",
		"whisper-1",
		"tts-1",
		"tts-1-hd",
		"dall-e-3",
		"dall-e-2",
	}
}

// ConvertRequest converts an OpenAI request to Azure OpenAI format
func (a *Adapter) ConvertRequest(ctx context.Context, request *core.OpenAIRequest) (interface{}, error) {
	if request == nil {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, "request cannot be nil", http.StatusBadRequest, a.GetProviderName())
	}
	
	// Validate the model
	if !a.isModelSupported(request.Model) {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, fmt.Sprintf("model %s is not supported", request.Model), http.StatusBadRequest, a.GetProviderName())
	}
	
	// Azure OpenAI uses the same format as OpenAI with some modifications
	azureRequest := &AzureRequest{
		Model:            request.Model,
		Messages:         request.Messages,
		MaxTokens:        request.MaxTokens,
		Temperature:      request.Temperature,
		TopP:             request.TopP,
		Stream:           request.Stream,
		Stop:             request.Stop,
		FrequencyPenalty: request.FrequencyPenalty,
		PresencePenalty:  request.PresencePenalty,
		N:                request.N,
		Tools:            request.Tools,
		ToolChoice:       request.ToolChoice,
		Functions:        request.Functions,
		FunctionCall:     request.FunctionCall,
		User:             request.User,
		Seed:             request.Seed,
	}
	
	// Azure-specific parameters
	if a.config.ExtraParams != nil {
		if dataSources, ok := a.config.ExtraParams["data_sources"]; ok {
			azureRequest.DataSources = dataSources
		}
		if enhancements, ok := a.config.ExtraParams["enhancements"]; ok {
			azureRequest.Enhancements = enhancements
		}
	}
	
	return azureRequest, nil
}

// BuildRequestURL builds the request URL for the given request
func (a *Adapter) BuildRequestURL(baseURL string, request *core.OpenAIRequest) (string, error) {
	if baseURL == "" {
		baseURL = a.config.BaseURL
	}
	
	// Get deployment name from config or use model name
	deploymentName := request.Model
	if a.config.ExtraParams != nil {
		if deployment, ok := a.config.ExtraParams["deployment_name"].(string); ok && deployment != "" {
			deploymentName = deployment
		}
	}
	
	// Get API version from config or use default
	apiVersion := "2024-02-15-preview"
	if a.config.ExtraParams != nil {
		if version, ok := a.config.ExtraParams["api_version"].(string); ok && version != "" {
			apiVersion = version
		}
	}
	
	var endpoint string
	
	// Determine endpoint based on request type
	if len(request.Messages) > 0 {
		endpoint = fmt.Sprintf("/openai/deployments/%s/chat/completions?api-version=%s", deploymentName, apiVersion)
	} else if request.Prompt != nil {
		endpoint = fmt.Sprintf("/openai/deployments/%s/completions?api-version=%s", deploymentName, apiVersion)
	} else if strings.Contains(request.Model, "embedding") {
		endpoint = fmt.Sprintf("/openai/deployments/%s/embeddings?api-version=%s", deploymentName, apiVersion)
	} else {
		return "", core.NewAdapterError(core.ErrorTypeInvalidRequest, "invalid request: cannot determine endpoint", http.StatusBadRequest, a.GetProviderName())
	}
	
	return core.BuildURL(baseURL, endpoint), nil
}

// SetupHeaders sets up the request headers
func (a *Adapter) SetupHeaders(headers map[string]string, apiKey string) error {
	if apiKey == "" {
		apiKey = a.config.APIKey
	}
	
	if apiKey == "" {
		return core.NewAdapterError(core.ErrorTypeAuthentication, "API key is required", http.StatusUnauthorized, a.GetProviderName())
	}
	
	if headers == nil {
		headers = make(map[string]string)
	}
	
	headers["Content-Type"] = "application/json"
	headers["api-key"] = apiKey // Azure uses api-key header instead of Authorization
	
	return nil
}

// ExecuteRequest executes the HTTP request
func (a *Adapter) ExecuteRequest(ctx context.Context, httpClient *http.Client, url string, headers map[string]string, body []byte) (*http.Response, error) {
	if httpClient == nil {
		httpClient = a.config.HTTPClient
	}
	
	if httpClient == nil {
		httpClient = &http.Client{}
	}
	
	client := core.NewHTTPClientWithClient(httpClient)
	return client.DoRequest(ctx, "POST", url, headers, body)
}

// ConvertResponse converts the Azure OpenAI response to OpenAI format
func (a *Adapter) ConvertResponse(ctx context.Context, response *http.Response, isStream bool) (*core.OpenAIResponse, error) {
	if !core.IsSuccessStatusCode(response.StatusCode) {
		return nil, a.HandleError(response)
	}
	
	if isStream {
		return nil, core.NewAdapterError(core.ErrorTypeServerError, "streaming not implemented in this example", http.StatusNotImplemented, a.GetProviderName())
	}
	
	var azureResponse AzureResponse
	if err := core.ParseJSONResponse(response, &azureResponse); err != nil {
		return nil, core.WrapError(err, core.ErrorTypeServerError, "failed to parse Azure OpenAI response", http.StatusInternalServerError, a.GetProviderName())
	}
	
	// Convert Azure response to OpenAI format (mostly the same)
	openaiResponse := &core.OpenAIResponse{
		ID:                azureResponse.ID,
		Object:            azureResponse.Object,
		Created:           azureResponse.Created,
		Model:             azureResponse.Model,
		Choices:           azureResponse.Choices,
		Usage:             azureResponse.Usage,
		SystemFingerprint: azureResponse.SystemFingerprint,
	}
	
	return openaiResponse, nil
}

// HandleError handles Azure OpenAI-specific errors
func (a *Adapter) HandleError(response *http.Response) error {
	body, err := core.ReadResponseBody(response)
	if err != nil {
		return core.WrapError(err, core.ErrorTypeServerError, "failed to read error response", response.StatusCode, a.GetProviderName())
	}
	
	var errorResponse struct {
		Error *AzureError `json:"error"`
	}
	
	if err := json.Unmarshal(body, &errorResponse); err != nil {
		return core.NewAdapterError(
			core.MapHTTPStatusToErrorType(response.StatusCode),
			string(body),
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	if errorResponse.Error != nil {
		return core.NewAdapterError(
			mapAzureErrorType(errorResponse.Error.Type),
			errorResponse.Error.Message,
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	return core.NewAdapterError(
		core.MapHTTPStatusToErrorType(response.StatusCode),
		"unknown error",
		response.StatusCode,
		a.GetProviderName(),
	)
}

// mapAzureErrorType maps Azure error types to core error types
func mapAzureErrorType(azureErrorType string) core.ErrorType {
	switch azureErrorType {
	case "invalid_request_error":
		return core.ErrorTypeInvalidRequest
	case "authentication_error":
		return core.ErrorTypeAuthentication
	case "permission_error":
		return core.ErrorTypePermission
	case "not_found_error":
		return core.ErrorTypeNotFound
	case "rate_limit_error":
		return core.ErrorTypeRateLimit
	case "quota_exceeded_error":
		return core.ErrorTypeQuotaExceeded
	case "server_error":
		return core.ErrorTypeServerError
	case "service_unavailable_error":
		return core.ErrorTypeServiceUnavailable
	case "timeout_error":
		return core.ErrorTypeTimeout
	default:
		return core.ErrorTypeUnknown
	}
}

// isModelSupported checks if the model is supported by this adapter
func (a *Adapter) isModelSupported(model string) bool {
	supportedModels := a.GetSupportedModels()
	for _, supportedModel := range supportedModels {
		if model == supportedModel {
			return true
		}
		// Also check for model prefixes
		if strings.HasPrefix(model, supportedModel) {
			return true
		}
	}
	return false
}
