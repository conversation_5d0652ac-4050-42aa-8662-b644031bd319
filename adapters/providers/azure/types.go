package azure

import (
	"../../core"
)

// AzureRequest represents an Azure OpenAI API request
type AzureRequest struct {
	Model            string                 `json:"model"`
	Messages         []core.Message         `json:"messages,omitempty"`
	Prompt           interface{}            `json:"prompt,omitempty"`
	MaxTokens        *int                   `json:"max_tokens,omitempty"`
	Temperature      *float64               `json:"temperature,omitempty"`
	TopP             *float64               `json:"top_p,omitempty"`
	Stream           bool                   `json:"stream,omitempty"`
	Stop             interface{}            `json:"stop,omitempty"`
	FrequencyPenalty *float64               `json:"frequency_penalty,omitempty"`
	PresencePenalty  *float64               `json:"presence_penalty,omitempty"`
	N                *int                   `json:"n,omitempty"`
	Tools            []core.Tool            `json:"tools,omitempty"`
	ToolChoice       interface{}            `json:"tool_choice,omitempty"`
	Functions        []core.Function        `json:"functions,omitempty"`
	FunctionCall     interface{}            `json:"function_call,omitempty"`
	User             string                 `json:"user,omitempty"`
	Seed             *int                   `json:"seed,omitempty"`
	
	// Azure-specific parameters
	DataSources      interface{}            `json:"data_sources,omitempty"`
	Enhancements     interface{}            `json:"enhancements,omitempty"`
}

// AzureResponse represents an Azure OpenAI API response
type AzureResponse struct {
	ID                string        `json:"id"`
	Object            string        `json:"object"`
	Created           int64         `json:"created"`
	Model             string        `json:"model"`
	Choices           []core.Choice `json:"choices"`
	Usage             *core.Usage   `json:"usage,omitempty"`
	SystemFingerprint string        `json:"system_fingerprint,omitempty"`
	
	// Azure-specific fields
	PromptFilterResults []AzurePromptFilterResult `json:"prompt_filter_results,omitempty"`
}

// AzurePromptFilterResult represents Azure's content filtering results
type AzurePromptFilterResult struct {
	PromptIndex      int                    `json:"prompt_index"`
	ContentFilterResults AzureContentFilter `json:"content_filter_results"`
}

// AzureContentFilter represents Azure's content filtering
type AzureContentFilter struct {
	Hate     AzureFilterResult `json:"hate"`
	SelfHarm AzureFilterResult `json:"self_harm"`
	Sexual   AzureFilterResult `json:"sexual"`
	Violence AzureFilterResult `json:"violence"`
}

// AzureFilterResult represents a content filter result
type AzureFilterResult struct {
	Filtered bool   `json:"filtered"`
	Severity string `json:"severity"`
}

// AzureStreamResponse represents a streaming response chunk
type AzureStreamResponse struct {
	ID      string        `json:"id"`
	Object  string        `json:"object"`
	Created int64         `json:"created"`
	Model   string        `json:"model"`
	Choices []core.Choice `json:"choices"`
}

// AzureError represents an Azure OpenAI API error
type AzureError struct {
	Message string      `json:"message"`
	Type    string      `json:"type"`
	Param   *string     `json:"param,omitempty"`
	Code    interface{} `json:"code,omitempty"`
	
	// Azure-specific error fields
	InnerError *AzureInnerError `json:"innererror,omitempty"`
}

// AzureInnerError represents Azure's inner error details
type AzureInnerError struct {
	Code                 string `json:"code"`
	ContentFilterResults interface{} `json:"content_filter_result,omitempty"`
}

// AzureDataSource represents Azure's data source configuration
type AzureDataSource struct {
	Type       string                    `json:"type"`
	Parameters AzureDataSourceParameters `json:"parameters"`
}

// AzureDataSourceParameters represents data source parameters
type AzureDataSourceParameters struct {
	Endpoint        string `json:"endpoint,omitempty"`
	Key             string `json:"key,omitempty"`
	IndexName       string `json:"index_name,omitempty"`
	FieldsMapping   interface{} `json:"fields_mapping,omitempty"`
	QueryType       string `json:"query_type,omitempty"`
	SemanticConfiguration string `json:"semantic_configuration,omitempty"`
	TopNDocuments   int    `json:"top_n_documents,omitempty"`
	InScope         bool   `json:"in_scope,omitempty"`
	RoleInformation string `json:"role_information,omitempty"`
}

// AzureEnhancements represents Azure's enhancement configuration
type AzureEnhancements struct {
	Grounding AzureGrounding `json:"grounding,omitempty"`
	OCR       AzureOCR       `json:"ocr,omitempty"`
}

// AzureGrounding represents grounding enhancement
type AzureGrounding struct {
	Enabled bool `json:"enabled"`
}

// AzureOCR represents OCR enhancement
type AzureOCR struct {
	Enabled bool `json:"enabled"`
}

// API versions
const (
	APIVersion20240215Preview = "2024-02-15-preview"
	APIVersion20231201Preview = "2023-12-01-preview"
	APIVersion20231001Preview = "2023-10-01-preview"
	APIVersion20230901Preview = "2023-09-01-preview"
	APIVersion20230701Preview = "2023-07-01-preview"
	APIVersion20230601Preview = "2023-06-01-preview"
	APIVersion20230501Preview = "2023-05-01-preview"
)

// Data source types
const (
	DataSourceTypeAzureCognitiveSearch = "AzureCognitiveSearch"
	DataSourceTypeAzureCosmosDB        = "AzureCosmosDB"
	DataSourceTypeElasticsearch        = "Elasticsearch"
	DataSourceTypePinecone             = "Pinecone"
)

// Query types for Azure Cognitive Search
const (
	QueryTypeSimple    = "simple"
	QueryTypeSemantic  = "semantic"
	QueryTypeVector    = "vector"
	QueryTypeVectorSimpleHybrid = "vectorSimpleHybrid"
	QueryTypeVectorSemanticHybrid = "vectorSemanticHybrid"
)

// Content filter severity levels
const (
	SeveritySafe   = "safe"
	SeverityLow    = "low"
	SeverityMedium = "medium"
	SeverityHigh   = "high"
)

// IsContentFiltered checks if content was filtered
func (r *AzureResponse) IsContentFiltered() bool {
	for _, result := range r.PromptFilterResults {
		if result.ContentFilterResults.Hate.Filtered ||
		   result.ContentFilterResults.SelfHarm.Filtered ||
		   result.ContentFilterResults.Sexual.Filtered ||
		   result.ContentFilterResults.Violence.Filtered {
			return true
		}
	}
	return false
}

// GetFilteredCategories returns the categories that were filtered
func (r *AzureResponse) GetFilteredCategories() []string {
	var filtered []string
	for _, result := range r.PromptFilterResults {
		if result.ContentFilterResults.Hate.Filtered {
			filtered = append(filtered, "hate")
		}
		if result.ContentFilterResults.SelfHarm.Filtered {
			filtered = append(filtered, "self_harm")
		}
		if result.ContentFilterResults.Sexual.Filtered {
			filtered = append(filtered, "sexual")
		}
		if result.ContentFilterResults.Violence.Filtered {
			filtered = append(filtered, "violence")
		}
	}
	return filtered
}

// IsEmbeddingModel checks if the model is an embedding model
func IsEmbeddingModel(model string) bool {
	return strings.Contains(model, "embedding")
}

// IsGPT4Model checks if the model is a GPT-4 model
func IsGPT4Model(model string) bool {
	return strings.Contains(model, "gpt-4")
}

// IsGPT35Model checks if the model is a GPT-3.5 model
func IsGPT35Model(model string) bool {
	return strings.Contains(model, "gpt-35")
}

// IsTTSModel checks if the model is a text-to-speech model
func IsTTSModel(model string) bool {
	return strings.Contains(model, "tts")
}

// IsDALLEModel checks if the model is a DALL-E model
func IsDALLEModel(model string) bool {
	return strings.Contains(model, "dall-e")
}

// IsWhisperModel checks if the model is a Whisper model
func IsWhisperModel(model string) bool {
	return strings.Contains(model, "whisper")
}
