package moonshot

import (
	"../../core"
)

// MoonshotRequest represents a Moonshot API request
type MoonshotRequest struct {
	Model            string           `json:"model"`
	Messages         []core.Message   `json:"messages"`
	MaxTokens        *int             `json:"max_tokens,omitempty"`
	Temperature      *float64         `json:"temperature,omitempty"`
	TopP             *float64         `json:"top_p,omitempty"`
	Stream           bool             `json:"stream,omitempty"`
	Stop             []string         `json:"stop,omitempty"`
	FrequencyPenalty *float64         `json:"frequency_penalty,omitempty"`
	PresencePenalty  *float64         `json:"presence_penalty,omitempty"`
	N                *int             `json:"n,omitempty"`
	Tools            []MoonshotTool   `json:"tools,omitempty"`
	ToolChoice       interface{}      `json:"tool_choice,omitempty"`
}

// MoonshotTool represents a tool definition
type MoonshotTool struct {
	Type     string        `json:"type"`
	Function core.Function `json:"function"`
}

// MoonshotResponse represents a Moonshot API response
type MoonshotResponse struct {
	ID      string             `json:"id"`
	Object  string             `json:"object"`
	Created int64              `json:"created"`
	Model   string             `json:"model"`
	Choices []MoonshotChoice   `json:"choices"`
	Usage   *core.Usage        `json:"usage,omitempty"`
}

// MoonshotChoice represents a choice in the response
type MoonshotChoice struct {
	Index        int           `json:"index"`
	Message      *core.Message `json:"message,omitempty"`
	Delta        *core.Message `json:"delta,omitempty"`
	FinishReason *string       `json:"finish_reason,omitempty"`
}

// MoonshotStreamResponse represents a streaming response chunk
type MoonshotStreamResponse struct {
	ID      string             `json:"id"`
	Object  string             `json:"object"`
	Created int64              `json:"created"`
	Model   string             `json:"model"`
	Choices []MoonshotChoice   `json:"choices"`
}

// MoonshotError represents a Moonshot API error
type MoonshotError struct {
	Error struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    string `json:"code"`
	} `json:"error"`
}

// Model context lengths
const (
	ContextLength8K   = 8192
	ContextLength32K  = 32768
	ContextLength128K = 131072
)

// Model categories
const (
	ModelCategoryV1   = "v1"
	ModelCategoryAuto = "auto"
)

// Tool choice options
const (
	ToolChoiceAuto = "auto"
	ToolChoiceNone = "none"
)

// GetModelContextLength returns the context length for a given model
func GetModelContextLength(model string) int {
	switch model {
	case "moonshot-v1-8k":
		return ContextLength8K
	case "moonshot-v1-32k":
		return ContextLength32K
	case "moonshot-v1-128k":
		return ContextLength128K
	case "moonshot-v1-auto":
		return ContextLength128K // Auto model uses the largest context
	default:
		return ContextLength8K // Default fallback
	}
}

// IsAutoModel checks if the model is the auto model
func IsAutoModel(model string) bool {
	return strings.Contains(model, "auto")
}

// GetModelVersion returns the version of the model
func GetModelVersion(model string) string {
	if strings.Contains(model, "v1") {
		return ModelCategoryV1
	}
	return ""
}
