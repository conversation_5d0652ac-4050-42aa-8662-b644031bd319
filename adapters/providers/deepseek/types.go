package deepseek

import (
	"../../core"
)

// DeepseekRequest represents a Deepseek API request
type DeepseekRequest struct {
	Model            string           `json:"model"`
	Messages         []core.Message   `json:"messages"`
	MaxTokens        *int             `json:"max_tokens,omitempty"`
	Temperature      *float64         `json:"temperature,omitempty"`
	TopP             *float64         `json:"top_p,omitempty"`
	Stream           bool             `json:"stream,omitempty"`
	Stop             []string         `json:"stop,omitempty"`
	FrequencyPenalty *float64         `json:"frequency_penalty,omitempty"`
	PresencePenalty  *float64         `json:"presence_penalty,omitempty"`
	Tools            []DeepseekTool   `json:"tools,omitempty"`
	ToolChoice       interface{}      `json:"tool_choice,omitempty"`
	
	// Deepseek-specific parameters for reasoning models
	ReasoningEffort  string           `json:"reasoning_effort,omitempty"`
}

// DeepseekTool represents a tool definition
type DeepseekTool struct {
	Type     string        `json:"type"`
	Function core.Function `json:"function"`
}

// DeepseekResponse represents a Deepseek API response
type DeepseekResponse struct {
	ID      string            `json:"id"`
	Object  string            `json:"object"`
	Created int64             `json:"created"`
	Model   string            `json:"model"`
	Choices []DeepseekChoice  `json:"choices"`
	Usage   *core.Usage       `json:"usage,omitempty"`
}

// DeepseekChoice represents a choice in the response
type DeepseekChoice struct {
	Index        int           `json:"index"`
	Message      *core.Message `json:"message,omitempty"`
	Delta        *core.Message `json:"delta,omitempty"`
	FinishReason *string       `json:"finish_reason,omitempty"`
}

// DeepseekStreamResponse represents a streaming response chunk
type DeepseekStreamResponse struct {
	ID      string            `json:"id"`
	Object  string            `json:"object"`
	Created int64             `json:"created"`
	Model   string            `json:"model"`
	Choices []DeepseekChoice  `json:"choices"`
}

// DeepseekError represents a Deepseek API error
type DeepseekError struct {
	Error struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    string `json:"code"`
	} `json:"error"`
}

// Model categories
const (
	ModelCategoryChat     = "chat"
	ModelCategoryCoder    = "coder"
	ModelCategoryReasoner = "reasoner"
	ModelCategoryR1       = "r1"
	ModelCategoryDistill  = "distill"
)

// Reasoning effort levels for R1 models
const (
	ReasoningEffortLow    = "low"
	ReasoningEffortMedium = "medium"
	ReasoningEffortHigh   = "high"
)

// Tool choice options
const (
	ToolChoiceAuto = "auto"
	ToolChoiceNone = "none"
)

// IsReasoningModel checks if the model is a reasoning model
func IsReasoningModel(model string) bool {
	return strings.Contains(model, "r1") || strings.Contains(model, "reasoner")
}

// IsCoderModel checks if the model is a coder model
func IsCoderModel(model string) bool {
	return strings.Contains(model, "coder")
}

// IsDistillModel checks if the model is a distilled model
func IsDistillModel(model string) bool {
	return strings.Contains(model, "distill")
}

// GetModelCategory returns the category of the model
func GetModelCategory(model string) string {
	if IsReasoningModel(model) {
		return ModelCategoryReasoner
	}
	if IsCoderModel(model) {
		return ModelCategoryCoder
	}
	if IsDistillModel(model) {
		return ModelCategoryDistill
	}
	return ModelCategoryChat
}
