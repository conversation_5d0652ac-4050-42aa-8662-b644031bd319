package deepseek

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"../../core"
)

// Adapter implements the Deepseek adapter
type Adapter struct {
	config *core.AdapterConfig
}

// NewAdapter creates a new Deepseek adapter
func NewAdapter(config *core.AdapterConfig) *Adapter {
	if config.BaseURL == "" {
		config.BaseURL = "https://api.deepseek.com"
	}
	
	return &Adapter{
		config: config,
	}
}

// GetProviderName returns the provider name
func (a *Adapter) GetProviderName() string {
	return "deepseek"
}

// GetSupportedModels returns the list of supported models
func (a *Adapter) GetSupportedModels() []string {
	return []string{
		"deepseek-chat",
		"deepseek-coder",
		"deepseek-reasoner",
		"deepseek-v3",
		"deepseek-r1",
		"deepseek-r1-distill-qwen-32b",
		"deepseek-r1-distill-qwen-14b",
		"deepseek-r1-distill-qwen-7b",
		"deepseek-r1-distill-llama-70b",
		"deepseek-r1-distill-llama-8b",
	}
}

// ConvertRequest converts an OpenAI request to Deepseek format
func (a *Adapter) ConvertRequest(ctx context.Context, request *core.OpenAIRequest) (interface{}, error) {
	if request == nil {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, "request cannot be nil", http.StatusBadRequest, a.GetProviderName())
	}
	
	// Validate the model
	if !a.isModelSupported(request.Model) {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, fmt.Sprintf("model %s is not supported", request.Model), http.StatusBadRequest, a.GetProviderName())
	}
	
	// Deepseek uses OpenAI format with some modifications
	deepseekRequest := &DeepseekRequest{
		Model:            request.Model,
		Messages:         request.Messages,
		MaxTokens:        request.MaxTokens,
		Temperature:      request.Temperature,
		TopP:             request.TopP,
		Stream:           request.Stream,
		FrequencyPenalty: request.FrequencyPenalty,
		PresencePenalty:  request.PresencePenalty,
	}

	// Handle stop sequences
	if request.Stop != nil {
		switch stop := request.Stop.(type) {
		case string:
			deepseekRequest.Stop = []string{stop}
		case []string:
			deepseekRequest.Stop = stop
		case []interface{}:
			stopSeqs := make([]string, 0, len(stop))
			for _, s := range stop {
				if str, ok := s.(string); ok {
					stopSeqs = append(stopSeqs, str)
				}
			}
			deepseekRequest.Stop = stopSeqs
		}
	}

	// Handle tools/functions
	if len(request.Tools) > 0 {
		tools := make([]DeepseekTool, len(request.Tools))
		for i, tool := range request.Tools {
			tools[i] = DeepseekTool{
				Type:     tool.Type,
				Function: tool.Function,
			}
		}
		deepseekRequest.Tools = tools
	}

	// Handle tool choice
	if request.ToolChoice != nil {
		deepseekRequest.ToolChoice = request.ToolChoice
	}

	// Deepseek-specific parameters for reasoning models
	if strings.Contains(request.Model, "r1") || strings.Contains(request.Model, "reasoner") {
		deepseekRequest.ReasoningEffort = "medium" // Can be configured: low, medium, high
	}

	return deepseekRequest, nil
}

// BuildRequestURL builds the request URL for the given request
func (a *Adapter) BuildRequestURL(baseURL string, request *core.OpenAIRequest) (string, error) {
	if baseURL == "" {
		baseURL = a.config.BaseURL
	}
	
	var endpoint string
	
	// Handle different endpoints based on model type
	if strings.Contains(request.Model, "coder") {
		endpoint = "/beta/completions"
	} else {
		endpoint = "/v1/chat/completions"
	}
	
	return core.BuildURL(baseURL, endpoint), nil
}

// SetupHeaders sets up the request headers
func (a *Adapter) SetupHeaders(headers map[string]string, apiKey string) error {
	if apiKey == "" {
		apiKey = a.config.APIKey
	}
	
	if apiKey == "" {
		return core.NewAdapterError(core.ErrorTypeAuthentication, "API key is required", http.StatusUnauthorized, a.GetProviderName())
	}
	
	core.SetupCommonHeaders(headers, apiKey)
	
	return nil
}

// ExecuteRequest executes the HTTP request
func (a *Adapter) ExecuteRequest(ctx context.Context, httpClient *http.Client, url string, headers map[string]string, body []byte) (*http.Response, error) {
	if httpClient == nil {
		httpClient = a.config.HTTPClient
	}
	
	if httpClient == nil {
		httpClient = &http.Client{}
	}
	
	client := core.NewHTTPClientWithClient(httpClient)
	return client.DoRequest(ctx, "POST", url, headers, body)
}

// ConvertResponse converts the Deepseek response to OpenAI format
func (a *Adapter) ConvertResponse(ctx context.Context, response *http.Response, isStream bool) (*core.OpenAIResponse, error) {
	if !core.IsSuccessStatusCode(response.StatusCode) {
		return nil, a.HandleError(response)
	}
	
	if isStream {
		// For streaming responses, we need to handle them differently
		// This is a simplified implementation - in practice, you'd want to handle SSE properly
		return nil, core.NewAdapterError(core.ErrorTypeServerError, "streaming not implemented in this example", http.StatusNotImplemented, a.GetProviderName())
	}
	
	var deepseekResponse DeepseekResponse
	if err := core.ParseJSONResponse(response, &deepseekResponse); err != nil {
		return nil, core.WrapError(err, core.ErrorTypeServerError, "failed to parse Deepseek response", http.StatusInternalServerError, a.GetProviderName())
	}
	
	// Convert Deepseek response to OpenAI format
	openaiResponse := &core.OpenAIResponse{
		ID:      deepseekResponse.ID,
		Object:  deepseekResponse.Object,
		Created: deepseekResponse.Created,
		Model:   deepseekResponse.Model,
		Choices: make([]core.Choice, len(deepseekResponse.Choices)),
		Usage:   deepseekResponse.Usage,
	}

	for i, choice := range deepseekResponse.Choices {
		openaiResponse.Choices[i] = core.Choice{
			Index:        choice.Index,
			Message:      choice.Message,
			FinishReason: choice.FinishReason,
		}
	}

	return openaiResponse, nil
}

// HandleError handles Deepseek-specific errors
func (a *Adapter) HandleError(response *http.Response) error {
	body, err := core.ReadResponseBody(response)
	if err != nil {
		return core.WrapError(err, core.ErrorTypeServerError, "failed to read error response", response.StatusCode, a.GetProviderName())
	}
	
	var errorResponse struct {
		Error *core.OpenAIError `json:"error"`
	}
	
	if err := json.Unmarshal(body, &errorResponse); err != nil {
		// If we can't parse the error, return a generic error
		return core.NewAdapterError(
			core.MapHTTPStatusToErrorType(response.StatusCode),
			string(body),
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	if errorResponse.Error != nil {
		return core.NewAdapterError(
			core.ErrorType(errorResponse.Error.Type),
			errorResponse.Error.Message,
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	return core.NewAdapterError(
		core.MapHTTPStatusToErrorType(response.StatusCode),
		"unknown error",
		response.StatusCode,
		a.GetProviderName(),
	)
}

// isModelSupported checks if the model is supported by this adapter
func (a *Adapter) isModelSupported(model string) bool {
	supportedModels := a.GetSupportedModels()
	for _, supportedModel := range supportedModels {
		if model == supportedModel {
			return true
		}
		// Also check for model prefixes
		if strings.HasPrefix(model, supportedModel) {
			return true
		}
	}
	return false
}
