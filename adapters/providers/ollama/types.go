package ollama

import (
	"../../core"
)

// OllamaRequest represents an Ollama API request
type OllamaRequest struct {
	Model    string                 `json:"model"`
	Messages []core.Message         `json:"messages"`
	Stream   bool                   `json:"stream,omitempty"`
	Options  map[string]interface{} `json:"options,omitempty"`
	Tools    []OllamaTool           `json:"tools,omitempty"`
}

// OllamaTool represents a tool definition
type OllamaTool struct {
	Type     string        `json:"type"`
	Function core.Function `json:"function"`
}

// OllamaResponse represents an Ollama API response
type OllamaResponse struct {
	ID      string          `json:"id"`
	Object  string          `json:"object"`
	Created int64           `json:"created"`
	Model   string          `json:"model"`
	Choices []OllamaChoice  `json:"choices"`
	Usage   *core.Usage     `json:"usage,omitempty"`
}

// OllamaChoice represents a choice in the response
type OllamaChoice struct {
	Index        int           `json:"index"`
	Message      *core.Message `json:"message,omitempty"`
	Delta        *core.Message `json:"delta,omitempty"`
	FinishReason *string       `json:"finish_reason,omitempty"`
}

// OllamaStreamResponse represents a streaming response chunk
type OllamaStreamResponse struct {
	Model     string        `json:"model"`
	CreatedAt string        `json:"created_at"`
	Message   *core.Message `json:"message,omitempty"`
	Done      bool          `json:"done"`
}

// OllamaEmbeddingRequest represents an embedding request
type OllamaEmbeddingRequest struct {
	Model  string `json:"model"`
	Prompt string `json:"prompt"`
}

// OllamaEmbeddingResponse represents an embedding response
type OllamaEmbeddingResponse struct {
	Embedding []float64 `json:"embedding"`
}

// OllamaError represents an Ollama API error
type OllamaError struct {
	Error string `json:"error"`
}

// OllamaModelInfo represents model information
type OllamaModelInfo struct {
	Name       string            `json:"name"`
	Size       int64             `json:"size"`
	Digest     string            `json:"digest"`
	Details    OllamaModelDetails `json:"details"`
	ModifiedAt string            `json:"modified_at"`
}

// OllamaModelDetails represents model details
type OllamaModelDetails struct {
	Format            string   `json:"format"`
	Family            string   `json:"family"`
	Families          []string `json:"families"`
	ParameterSize     string   `json:"parameter_size"`
	QuantizationLevel string   `json:"quantization_level"`
}

// Common Ollama options
const (
	OptionTemperature      = "temperature"
	OptionTopP             = "top_p"
	OptionTopK             = "top_k"
	OptionNumPredict       = "num_predict"
	OptionStop             = "stop"
	OptionFrequencyPenalty = "frequency_penalty"
	OptionPresencePenalty  = "presence_penalty"
	OptionRepeatPenalty    = "repeat_penalty"
	OptionSeed             = "seed"
	OptionNumCtx           = "num_ctx"
	OptionNumBatch         = "num_batch"
	OptionNumGPU           = "num_gpu"
	OptionMainGPU          = "main_gpu"
	OptionLowVRAM          = "low_vram"
	OptionF16KV            = "f16_kv"
	OptionLogitsAll        = "logits_all"
	OptionVocabOnly        = "vocab_only"
	OptionUseMMap          = "use_mmap"
	OptionUseMlock         = "use_mlock"
	OptionNumThread        = "num_thread"
)

// Model families
const (
	FamilyLlama     = "llama"
	FamilyMistral   = "mistral"
	FamilyGemma     = "gemma"
	FamilyQwen      = "qwen"
	FamilyPhi       = "phi"
	FamilyCodeLlama = "codellama"
	FamilyVicuna    = "vicuna"
	FamilyOrcaMini  = "orca-mini"
)

// IsVisionModel checks if the model supports vision
func IsVisionModel(model string) bool {
	visionModels := []string{"llava", "bakllava", "moondream"}
	for _, visionModel := range visionModels {
		if strings.Contains(model, visionModel) {
			return true
		}
	}
	return false
}

// IsCodeModel checks if the model is specialized for code
func IsCodeModel(model string) bool {
	codeModels := []string{"codellama", "deepseek-coder", "starcoder", "wizard-coder"}
	for _, codeModel := range codeModels {
		if strings.Contains(model, codeModel) {
			return true
		}
	}
	return false
}

// IsEmbeddingModel checks if the model is for embeddings
func IsEmbeddingModel(model string) bool {
	embeddingModels := []string{"text-embedding", "nomic-embed", "all-minilm"}
	for _, embModel := range embeddingModels {
		if strings.Contains(model, embModel) {
			return true
		}
	}
	return false
}

// GetModelFamily returns the family of the model
func GetModelFamily(model string) string {
	if strings.Contains(model, "llama") {
		return FamilyLlama
	}
	if strings.Contains(model, "mistral") {
		return FamilyMistral
	}
	if strings.Contains(model, "gemma") {
		return FamilyGemma
	}
	if strings.Contains(model, "qwen") {
		return FamilyQwen
	}
	if strings.Contains(model, "phi") {
		return FamilyPhi
	}
	if strings.Contains(model, "codellama") {
		return FamilyCodeLlama
	}
	if strings.Contains(model, "vicuna") {
		return FamilyVicuna
	}
	if strings.Contains(model, "orca") {
		return FamilyOrcaMini
	}
	return "unknown"
}
