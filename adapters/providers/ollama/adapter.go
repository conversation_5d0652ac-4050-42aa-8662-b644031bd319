package ollama

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"../../core"
)

// Adapter implements the Ollama adapter
type Adapter struct {
	config *core.AdapterConfig
}

// NewAdapter creates a new Ollama adapter
func NewAdapter(config *core.AdapterConfig) *Adapter {
	if config.BaseURL == "" {
		config.BaseURL = "http://localhost:11434"
	}
	
	return &Adapter{
		config: config,
	}
}

// GetProviderName returns the provider name
func (a *Adapter) GetProviderName() string {
	return "ollama"
}

// GetSupportedModels returns the list of supported models
// Note: Ollama supports many models, this is a common subset
func (a *Adapter) GetSupportedModels() []string {
	return []string{
		"llama3.2",
		"llama3.1",
		"llama3",
		"llama2",
		"codellama",
		"mistral",
		"mixtral",
		"gemma",
		"gemma2",
		"qwen",
		"qwen2",
		"phi3",
		"deepseek-coder",
		"starcoder",
		"vicuna",
		"orca-mini",
		"wizard-coder",
		"solar",
		"neural-chat",
		"starling-lm",
		"openchat",
		"zephyr",
		"nous-hermes",
		"dolphin-mixtral",
		"dolphin-llama3",
		"llava",
		"bakllava",
		"moondream",
		"text-embedding-ada-002", // For embeddings
		"nomic-embed-text",
		"all-minilm",
	}
}

// ConvertRequest converts an OpenAI request to Ollama format
func (a *Adapter) ConvertRequest(ctx context.Context, request *core.OpenAIRequest) (interface{}, error) {
	if request == nil {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, "request cannot be nil", http.StatusBadRequest, a.GetProviderName())
	}
	
	// For embeddings
	if a.isEmbeddingModel(request.Model) {
		return a.convertEmbeddingRequest(request)
	}
	
	// Ollama uses OpenAI format with some modifications
	ollamaRequest := &OllamaRequest{
		Model:       request.Model,
		Messages:    request.Messages,
		Stream:      request.Stream,
		Options:     make(map[string]interface{}),
	}

	// Map OpenAI parameters to Ollama options
	if request.Temperature != nil {
		ollamaRequest.Options["temperature"] = *request.Temperature
	}
	if request.TopP != nil {
		ollamaRequest.Options["top_p"] = *request.TopP
	}
	if request.TopK != nil {
		ollamaRequest.Options["top_k"] = *request.TopK
	}
	if request.MaxTokens != nil {
		ollamaRequest.Options["num_predict"] = *request.MaxTokens
	}
	if request.FrequencyPenalty != nil {
		ollamaRequest.Options["frequency_penalty"] = *request.FrequencyPenalty
	}
	if request.PresencePenalty != nil {
		ollamaRequest.Options["presence_penalty"] = *request.PresencePenalty
	}

	// Handle stop sequences
	if request.Stop != nil {
		switch stop := request.Stop.(type) {
		case string:
			ollamaRequest.Options["stop"] = []string{stop}
		case []string:
			ollamaRequest.Options["stop"] = stop
		case []interface{}:
			stopSeqs := make([]string, 0, len(stop))
			for _, s := range stop {
				if str, ok := s.(string); ok {
					stopSeqs = append(stopSeqs, str)
				}
			}
			ollamaRequest.Options["stop"] = stopSeqs
		}
	}

	// Handle tools/functions (if supported by the model)
	if len(request.Tools) > 0 {
		tools := make([]OllamaTool, len(request.Tools))
		for i, tool := range request.Tools {
			tools[i] = OllamaTool{
				Type:     tool.Type,
				Function: tool.Function,
			}
		}
		ollamaRequest.Tools = tools
	}

	return ollamaRequest, nil
}

// convertEmbeddingRequest converts an embedding request
func (a *Adapter) convertEmbeddingRequest(request *core.OpenAIRequest) (*OllamaEmbeddingRequest, error) {
	embeddingRequest := &OllamaEmbeddingRequest{
		Model: request.Model,
	}

	// Extract input from messages or prompt
	if len(request.Messages) > 0 {
		// Use the last message content
		lastMessage := request.Messages[len(request.Messages)-1]
		embeddingRequest.Prompt = lastMessage.GetStringContent()
	} else if request.Prompt != nil {
		switch prompt := request.Prompt.(type) {
		case string:
			embeddingRequest.Prompt = prompt
		case []string:
			if len(prompt) > 0 {
				embeddingRequest.Prompt = prompt[0] // Take first prompt
			}
		}
	}

	return embeddingRequest, nil
}

// BuildRequestURL builds the request URL for the given request
func (a *Adapter) BuildRequestURL(baseURL string, request *core.OpenAIRequest) (string, error) {
	if baseURL == "" {
		baseURL = a.config.BaseURL
	}
	
	var endpoint string
	if a.isEmbeddingModel(request.Model) {
		endpoint = "/api/embeddings"
	} else {
		endpoint = "/v1/chat/completions"
	}
	
	return core.BuildURL(baseURL, endpoint), nil
}

// SetupHeaders sets up the request headers
func (a *Adapter) SetupHeaders(headers map[string]string, apiKey string) error {
	// Ollama typically doesn't require authentication for local deployments
	// But we'll set it if provided
	if apiKey == "" {
		apiKey = a.config.APIKey
	}
	
	if headers == nil {
		headers = make(map[string]string)
	}
	
	headers["Content-Type"] = "application/json"
	headers["User-Agent"] = "llm-adapter/1.0"
	
	if apiKey != "" {
		headers["Authorization"] = "Bearer " + apiKey
	}
	
	return nil
}

// ExecuteRequest executes the HTTP request
func (a *Adapter) ExecuteRequest(ctx context.Context, httpClient *http.Client, url string, headers map[string]string, body []byte) (*http.Response, error) {
	if httpClient == nil {
		httpClient = a.config.HTTPClient
	}
	
	if httpClient == nil {
		httpClient = &http.Client{}
	}
	
	client := core.NewHTTPClientWithClient(httpClient)
	return client.DoRequest(ctx, "POST", url, headers, body)
}

// ConvertResponse converts the Ollama response to OpenAI format
func (a *Adapter) ConvertResponse(ctx context.Context, response *http.Response, isStream bool) (*core.OpenAIResponse, error) {
	if !core.IsSuccessStatusCode(response.StatusCode) {
		return nil, a.HandleError(response)
	}
	
	if isStream {
		// For streaming responses, we need to handle them differently
		// This is a simplified implementation - in practice, you'd want to handle SSE properly
		return nil, core.NewAdapterError(core.ErrorTypeServerError, "streaming not implemented in this example", http.StatusNotImplemented, a.GetProviderName())
	}
	
	var ollamaResponse OllamaResponse
	if err := core.ParseJSONResponse(response, &ollamaResponse); err != nil {
		return nil, core.WrapError(err, core.ErrorTypeServerError, "failed to parse Ollama response", http.StatusInternalServerError, a.GetProviderName())
	}
	
	// Convert Ollama response to OpenAI format
	openaiResponse := &core.OpenAIResponse{
		ID:      ollamaResponse.ID,
		Object:  ollamaResponse.Object,
		Created: ollamaResponse.Created,
		Model:   ollamaResponse.Model,
		Choices: make([]core.Choice, len(ollamaResponse.Choices)),
		Usage:   ollamaResponse.Usage,
	}

	for i, choice := range ollamaResponse.Choices {
		openaiResponse.Choices[i] = core.Choice{
			Index:        choice.Index,
			Message:      choice.Message,
			FinishReason: choice.FinishReason,
		}
	}

	return openaiResponse, nil
}

// HandleError handles Ollama-specific errors
func (a *Adapter) HandleError(response *http.Response) error {
	body, err := core.ReadResponseBody(response)
	if err != nil {
		return core.WrapError(err, core.ErrorTypeServerError, "failed to read error response", response.StatusCode, a.GetProviderName())
	}
	
	var errorResponse struct {
		Error string `json:"error"`
	}
	
	if err := json.Unmarshal(body, &errorResponse); err != nil {
		// If we can't parse the error, return a generic error
		return core.NewAdapterError(
			core.MapHTTPStatusToErrorType(response.StatusCode),
			string(body),
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	if errorResponse.Error != "" {
		return core.NewAdapterError(
			core.MapHTTPStatusToErrorType(response.StatusCode),
			errorResponse.Error,
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	return core.NewAdapterError(
		core.MapHTTPStatusToErrorType(response.StatusCode),
		"unknown error",
		response.StatusCode,
		a.GetProviderName(),
	)
}

// isEmbeddingModel checks if the model is an embedding model
func (a *Adapter) isEmbeddingModel(model string) bool {
	embeddingModels := []string{"text-embedding", "nomic-embed", "all-minilm"}
	for _, embModel := range embeddingModels {
		if strings.Contains(model, embModel) {
			return true
		}
	}
	return false
}

// isModelSupported checks if the model is supported by this adapter
func (a *Adapter) isModelSupported(model string) bool {
	// Ollama is flexible and supports many models
	// We'll be permissive here and allow any model
	return true
}
