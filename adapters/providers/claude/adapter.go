package claude

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"../../core"
)

// Adapter implements the Claude adapter
type Adapter struct {
	config *core.AdapterConfig
}

// NewAdapter creates a new Claude adapter
func NewAdapter(config *core.AdapterConfig) *Adapter {
	if config.BaseURL == "" {
		config.BaseURL = "https://api.anthropic.com/v1"
	}
	
	return &Adapter{
		config: config,
	}
}

// GetProviderName returns the provider name
func (a *Adapter) GetProviderName() string {
	return "claude"
}

// GetSupportedModels returns the list of supported models
func (a *Adapter) GetSupportedModels() []string {
	return []string{
		"claude-3-5-sonnet-20241022",
		"claude-3-5-sonnet-20240620",
		"claude-3-5-haiku-20241022",
		"claude-3-opus-20240229",
		"claude-3-sonnet-20240229",
		"claude-3-haiku-20240307",
		"claude-2.1",
		"claude-2.0",
		"claude-instant-1.2",
	}
}

// ConvertRequest converts an OpenAI request to Claude format
func (a *Adapter) ConvertRequest(ctx context.Context, request *core.OpenAIRequest) (interface{}, error) {
	if request == nil {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, "request cannot be nil", http.StatusBadRequest, a.GetProviderName())
	}
	
	// Validate the model
	if !a.isModelSupported(request.Model) {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, fmt.Sprintf("model %s is not supported", request.Model), http.StatusBadRequest, a.GetProviderName())
	}
	
	// Convert OpenAI request to Claude format
	claudeRequest, err := ConvertOpenAIToClaude(request)
	if err != nil {
		return nil, core.WrapError(err, core.ErrorTypeInvalidRequest, "failed to convert request to Claude format", http.StatusBadRequest, a.GetProviderName())
	}
	
	return claudeRequest, nil
}

// BuildRequestURL builds the request URL for the given request
func (a *Adapter) BuildRequestURL(baseURL string, request *core.OpenAIRequest) (string, error) {
	if baseURL == "" {
		baseURL = a.config.BaseURL
	}
	
	// Claude uses /messages endpoint for chat completions
	endpoint := "/messages"
	
	return core.BuildURL(baseURL, endpoint), nil
}

// SetupHeaders sets up the request headers
func (a *Adapter) SetupHeaders(headers map[string]string, apiKey string) error {
	if apiKey == "" {
		apiKey = a.config.APIKey
	}
	
	if apiKey == "" {
		return core.NewAdapterError(core.ErrorTypeAuthentication, "API key is required", http.StatusUnauthorized, a.GetProviderName())
	}
	
	if headers == nil {
		headers = make(map[string]string)
	}
	
	headers["Content-Type"] = "application/json"
	headers["x-api-key"] = apiKey
	headers["anthropic-version"] = "2023-06-01"
	
	return nil
}

// ExecuteRequest executes the HTTP request
func (a *Adapter) ExecuteRequest(ctx context.Context, httpClient *http.Client, url string, headers map[string]string, body []byte) (*http.Response, error) {
	if httpClient == nil {
		httpClient = a.config.HTTPClient
	}
	
	if httpClient == nil {
		httpClient = &http.Client{}
	}
	
	client := core.NewHTTPClientWithClient(httpClient)
	return client.DoRequest(ctx, "POST", url, headers, body)
}

// ConvertResponse converts the Claude response to OpenAI format
func (a *Adapter) ConvertResponse(ctx context.Context, response *http.Response, isStream bool) (*core.OpenAIResponse, error) {
	if !core.IsSuccessStatusCode(response.StatusCode) {
		return nil, a.HandleError(response)
	}
	
	if isStream {
		// For streaming responses, we need to handle them differently
		// This is a simplified implementation - in practice, you'd want to handle SSE properly
		return nil, core.NewAdapterError(core.ErrorTypeServerError, "streaming not implemented in this example", http.StatusNotImplemented, a.GetProviderName())
	}
	
	var claudeResponse ClaudeResponse
	if err := core.ParseJSONResponse(response, &claudeResponse); err != nil {
		return nil, core.WrapError(err, core.ErrorTypeServerError, "failed to parse Claude response", http.StatusInternalServerError, a.GetProviderName())
	}
	
	// Convert Claude response to OpenAI format
	openaiResponse, err := ConvertClaudeToOpenAI(&claudeResponse, claudeResponse.Model)
	if err != nil {
		return nil, core.WrapError(err, core.ErrorTypeServerError, "failed to convert Claude response to OpenAI format", http.StatusInternalServerError, a.GetProviderName())
	}
	
	return openaiResponse, nil
}

// HandleError handles Claude-specific errors
func (a *Adapter) HandleError(response *http.Response) error {
	body, err := core.ReadResponseBody(response)
	if err != nil {
		return core.WrapError(err, core.ErrorTypeServerError, "failed to read error response", response.StatusCode, a.GetProviderName())
	}
	
	var claudeError ClaudeErrorResponse
	if err := json.Unmarshal(body, &claudeError); err != nil {
		// If we can't parse the error, return a generic error
		return core.NewAdapterError(
			core.MapHTTPStatusToErrorType(response.StatusCode),
			string(body),
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	if claudeError.Error.Message != "" {
		errorType := mapClaudeErrorType(claudeError.Error.Type)
		
		return core.NewAdapterError(
			errorType,
			claudeError.Error.Message,
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	return core.NewAdapterError(
		core.MapHTTPStatusToErrorType(response.StatusCode),
		"unknown Claude error",
		response.StatusCode,
		a.GetProviderName(),
	)
}

// mapClaudeErrorType maps Claude error types to core error types
func mapClaudeErrorType(claudeErrorType string) core.ErrorType {
	switch claudeErrorType {
	case ErrorTypeInvalidRequest:
		return core.ErrorTypeInvalidRequest
	case ErrorTypeAuthentication:
		return core.ErrorTypeAuthentication
	case ErrorTypePermission:
		return core.ErrorTypePermission
	case ErrorTypeNotFound:
		return core.ErrorTypeNotFound
	case ErrorTypeRateLimit:
		return core.ErrorTypeRateLimit
	case ErrorTypeAPIError:
		return core.ErrorTypeServerError
	case ErrorTypeOverloaded:
		return core.ErrorTypeServiceUnavailable
	default:
		return core.ErrorTypeUnknown
	}
}

// isModelSupported checks if the model is supported by this adapter
func (a *Adapter) isModelSupported(model string) bool {
	supportedModels := a.GetSupportedModels()
	for _, supportedModel := range supportedModels {
		if model == supportedModel {
			return true
		}
		// Also check for model prefixes
		if strings.HasPrefix(model, "claude") && strings.Contains(supportedModel, strings.Split(model, "-")[1]) {
			return true
		}
	}
	return false
}
