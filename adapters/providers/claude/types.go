package claude

// ClaudeRequest represents a Claude API request
type ClaudeRequest struct {
	Model         string          `json:"model"`
	MaxTokens     int             `json:"max_tokens"`
	Messages      []ClaudeMessage `json:"messages,omitempty"`
	System        string          `json:"system,omitempty"`
	Temperature   *float64        `json:"temperature,omitempty"`
	TopP          *float64        `json:"top_p,omitempty"`
	TopK          *int            `json:"top_k,omitempty"`
	StopSequences []string        `json:"stop_sequences,omitempty"`
	Stream        bool            `json:"stream,omitempty"`
	Tools         []ClaudeTool    `json:"tools,omitempty"`
	ToolChoice    interface{}     `json:"tool_choice,omitempty"`
}

// ClaudeMessage represents a message in Claude format
type ClaudeMessage struct {
	Role    string      `json:"role"`
	Content interface{} `json:"content"`
}

// ClaudeContent represents content in a Claude message
type ClaudeContent struct {
	Type   string                 `json:"type"`
	Text   string                 `json:"text,omitempty"`
	Source *ClaudeContentSource   `json:"source,omitempty"`
	Name   string                 `json:"name,omitempty"`
	Input  map[string]interface{} `json:"input,omitempty"`
	ID     string                 `json:"id,omitempty"`
}

// ClaudeContentSource represents a content source (e.g., image)
type ClaudeContentSource struct {
	Type      string `json:"type"`
	MediaType string `json:"media_type"`
	Data      string `json:"data"`
}

// ClaudeTool represents a tool definition
type ClaudeTool struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	InputSchema map[string]interface{} `json:"input_schema"`
}

// ClaudeResponse represents a Claude API response
type ClaudeResponse struct {
	ID           string               `json:"id"`
	Type         string               `json:"type"`
	Role         string               `json:"role"`
	Content      []ClaudeContent      `json:"content"`
	Model        string               `json:"model"`
	StopReason   string               `json:"stop_reason,omitempty"`
	StopSequence string               `json:"stop_sequence,omitempty"`
	Usage        ClaudeUsage          `json:"usage"`
}

// ClaudeUsage represents usage information
type ClaudeUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
}

// ClaudeStreamResponse represents a streaming response chunk
type ClaudeStreamResponse struct {
	Type         string               `json:"type"`
	Message      *ClaudeResponse      `json:"message,omitempty"`
	Index        int                  `json:"index,omitempty"`
	ContentBlock *ClaudeContent       `json:"content_block,omitempty"`
	Delta        *ClaudeContentDelta  `json:"delta,omitempty"`
	Usage        *ClaudeUsage         `json:"usage,omitempty"`
}

// ClaudeContentDelta represents a content delta in streaming
type ClaudeContentDelta struct {
	Type         string                 `json:"type"`
	Text         string                 `json:"text,omitempty"`
	PartialJSON  string                 `json:"partial_json,omitempty"`
	Name         string                 `json:"name,omitempty"`
	Input        map[string]interface{} `json:"input,omitempty"`
}

// ClaudeError represents a Claude API error
type ClaudeError struct {
	Type    string `json:"type"`
	Message string `json:"message"`
}

// ClaudeErrorResponse represents an error response
type ClaudeErrorResponse struct {
	Error ClaudeError `json:"error"`
}

// Content types
const (
	ContentTypeText     = "text"
	ContentTypeImage    = "image"
	ContentTypeToolUse  = "tool_use"
	ContentTypeToolResult = "tool_result"
)

// Message roles
const (
	RoleUser      = "user"
	RoleAssistant = "assistant"
)

// Stop reasons
const (
	StopReasonEndTurn      = "end_turn"
	StopReasonMaxTokens    = "max_tokens"
	StopReasonStopSequence = "stop_sequence"
	StopReasonToolUse      = "tool_use"
)

// Stream event types
const (
	StreamTypeMessageStart    = "message_start"
	StreamTypeMessageDelta    = "message_delta"
	StreamTypeMessageStop     = "message_stop"
	StreamTypeContentBlockStart = "content_block_start"
	StreamTypeContentBlockDelta = "content_block_delta"
	StreamTypeContentBlockStop  = "content_block_stop"
	StreamTypePing            = "ping"
	StreamTypeError           = "error"
)

// Tool choice options
const (
	ToolChoiceAuto = "auto"
	ToolChoiceAny  = "any"
)

// Image media types
const (
	MediaTypeJPEG = "image/jpeg"
	MediaTypePNG  = "image/png"
	MediaTypeGIF  = "image/gif"
	MediaTypeWebP = "image/webp"
)

// Error types
const (
	ErrorTypeInvalidRequest = "invalid_request_error"
	ErrorTypeAuthentication = "authentication_error"
	ErrorTypePermission     = "permission_error"
	ErrorTypeNotFound       = "not_found_error"
	ErrorTypeRateLimit      = "rate_limit_error"
	ErrorTypeAPIError       = "api_error"
	ErrorTypeOverloaded     = "overloaded_error"
)
