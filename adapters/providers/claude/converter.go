package claude

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"../../core"
)

// ConvertOpenAIToClaude converts an OpenAI request to Claude format
func ConvertOpenAIToClaude(request *core.OpenAIRequest) (*ClaudeRequest, error) {
	claudeRequest := &ClaudeRequest{
		Model:       request.Model,
		Temperature: request.Temperature,
		TopP:        request.TopP,
		TopK:        request.TopK,
		Stream:      request.Stream,
	}

	// Set max tokens (required for <PERSON>)
	if request.MaxTokens != nil {
		claudeRequest.MaxTokens = *request.MaxTokens
	} else {
		claudeRequest.MaxTokens = 4096 // Default value
	}

	// Convert stop sequences
	if request.Stop != nil {
		switch stop := request.Stop.(type) {
		case string:
			claudeRequest.StopSequences = []string{stop}
		case []string:
			claudeRequest.StopSequences = stop
		case []interface{}:
			stopSeqs := make([]string, 0, len(stop))
			for _, s := range stop {
				if str, ok := s.(string); ok {
					stopSeqs = append(stopSeqs, str)
				}
			}
			claudeRequest.StopSequences = stopSeqs
		}
	}

	// Convert messages
	messages, system, err := convertMessages(request.Messages)
	if err != nil {
		return nil, err
	}
	
	claudeRequest.Messages = messages
	claudeRequest.System = system

	// Convert tools/functions
	if len(request.Tools) > 0 || len(request.Functions) > 0 {
		tools, err := convertTools(request.Tools, request.Functions)
		if err != nil {
			return nil, err
		}
		claudeRequest.Tools = tools
	}

	// Convert tool choice
	if request.ToolChoice != nil {
		claudeRequest.ToolChoice = request.ToolChoice
	}

	return claudeRequest, nil
}

// convertMessages converts OpenAI messages to Claude format
func convertMessages(messages []core.Message) ([]ClaudeMessage, string, error) {
	var claudeMessages []ClaudeMessage
	var systemMessage string

	for _, message := range messages {
		switch message.Role {
		case "system":
			// Claude handles system messages separately
			systemMessage = message.GetStringContent()

		case "user", "assistant":
			claudeMessage := ClaudeMessage{
				Role: message.Role,
			}

			// Handle different content types
			if message.Content != nil {
				content, err := message.ParseContent()
				if err != nil {
					return nil, "", fmt.Errorf("failed to parse message content: %w", err)
				}

				if len(content) == 1 && content[0].Type == "text" {
					// Simple text content
					claudeMessage.Content = content[0].Text
				} else {
					// Multi-part content
					claudeContent, err := convertContentParts(content)
					if err != nil {
						return nil, "", fmt.Errorf("failed to convert content parts: %w", err)
					}
					claudeMessage.Content = claudeContent
				}
			}

			// Handle tool calls for assistant messages
			if message.Role == "assistant" && message.ToolCalls != nil {
				var toolCalls []core.ToolCall
				if err := json.Unmarshal(message.ToolCalls, &toolCalls); err != nil {
					return nil, "", fmt.Errorf("failed to parse tool calls: %w", err)
				}

				var claudeContent []ClaudeContent
				
				// Add existing text content if any
				if claudeMessage.Content != nil {
					if text, ok := claudeMessage.Content.(string); ok && text != "" {
						claudeContent = append(claudeContent, ClaudeContent{
							Type: ContentTypeText,
							Text: text,
						})
					}
				}

				// Add tool calls
				for _, toolCall := range toolCalls {
					if toolCall.Type == "function" {
						var input map[string]interface{}
						if err := json.Unmarshal([]byte(toolCall.Function.Arguments), &input); err != nil {
							return nil, "", fmt.Errorf("failed to parse function arguments: %w", err)
						}

						claudeContent = append(claudeContent, ClaudeContent{
							Type:  ContentTypeToolUse,
							ID:    toolCall.ID,
							Name:  toolCall.Function.Name,
							Input: input,
						})
					}
				}

				claudeMessage.Content = claudeContent
			}

			claudeMessages = append(claudeMessages, claudeMessage)

		case "tool":
			// Handle tool responses
			if message.ToolCallID != "" {
				claudeMessage := ClaudeMessage{
					Role: "user",
					Content: []ClaudeContent{
						{
							Type:    ContentTypeToolResult,
							ID:      message.ToolCallID,
							Content: message.GetStringContent(),
						},
					},
				}
				claudeMessages = append(claudeMessages, claudeMessage)
			}
		}
	}

	return claudeMessages, systemMessage, nil
}

// convertContentParts converts OpenAI content parts to Claude format
func convertContentParts(content []core.MediaContent) ([]ClaudeContent, error) {
	var claudeContent []ClaudeContent

	for _, part := range content {
		switch part.Type {
		case "text":
			claudeContent = append(claudeContent, ClaudeContent{
				Type: ContentTypeText,
				Text: part.Text,
			})

		case "image_url":
			if part.ImageURL != nil {
				// Handle base64 images
				if strings.HasPrefix(part.ImageURL.URL, "data:") {
					// Extract mime type and data from data URL
					dataParts := strings.SplitN(part.ImageURL.URL, ",", 2)
					if len(dataParts) == 2 {
						mediaType := MediaTypeJPEG // default
						if strings.Contains(dataParts[0], "image/") {
							start := strings.Index(dataParts[0], "image/")
							end := strings.Index(dataParts[0][start:], ";")
							if end > 0 {
								mediaType = dataParts[0][start : start+end]
							}
						}

						claudeContent = append(claudeContent, ClaudeContent{
							Type: ContentTypeImage,
							Source: &ClaudeContentSource{
								Type:      "base64",
								MediaType: mediaType,
								Data:      dataParts[1],
							},
						})
					}
				} else {
					// For URL images, we'd need to fetch and convert to base64
					// For now, we'll skip this and just add a text description
					claudeContent = append(claudeContent, ClaudeContent{
						Type: ContentTypeText,
						Text: fmt.Sprintf("[Image: %s]", part.ImageURL.URL),
					})
				}
			}
		}
	}

	return claudeContent, nil
}

// convertTools converts OpenAI tools/functions to Claude format
func convertTools(tools []core.Tool, functions []core.Function) ([]ClaudeTool, error) {
	var claudeTools []ClaudeTool

	// Convert tools
	for _, tool := range tools {
		if tool.Type == "function" {
			schema, ok := tool.Function.Parameters.(map[string]interface{})
			if !ok {
				schema = make(map[string]interface{})
			}

			claudeTools = append(claudeTools, ClaudeTool{
				Name:        tool.Function.Name,
				Description: tool.Function.Description,
				InputSchema: schema,
			})
		}
	}

	// Convert legacy functions
	for _, function := range functions {
		schema, ok := function.Parameters.(map[string]interface{})
		if !ok {
			schema = make(map[string]interface{})
		}

		claudeTools = append(claudeTools, ClaudeTool{
			Name:        function.Name,
			Description: function.Description,
			InputSchema: schema,
		})
	}

	return claudeTools, nil
}

// ConvertClaudeToOpenAI converts a Claude response to OpenAI format
func ConvertClaudeToOpenAI(response *ClaudeResponse, model string) (*core.OpenAIResponse, error) {
	openaiResponse := &core.OpenAIResponse{
		ID:      response.ID,
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   model,
		Choices: []core.Choice{},
	}

	// Convert usage
	openaiResponse.Usage = &core.Usage{
		PromptTokens:     response.Usage.InputTokens,
		CompletionTokens: response.Usage.OutputTokens,
		TotalTokens:      response.Usage.InputTokens + response.Usage.OutputTokens,
	}

	// Convert content to choice
	choice := core.Choice{
		Index: 0,
	}

	// Convert finish reason
	finishReason := convertFinishReason(response.StopReason)
	choice.FinishReason = &finishReason

	// Convert content
	message, err := convertClaudeContentToMessage(response.Content)
	if err != nil {
		return nil, fmt.Errorf("failed to convert Claude content: %w", err)
	}
	choice.Message = message

	openaiResponse.Choices = append(openaiResponse.Choices, choice)

	return openaiResponse, nil
}

// convertClaudeContentToMessage converts Claude content to OpenAI message
func convertClaudeContentToMessage(content []ClaudeContent) (*core.Message, error) {
	message := &core.Message{
		Role: "assistant",
	}

	var textParts []string
	var toolCalls []core.ToolCall

	for _, part := range content {
		switch part.Type {
		case ContentTypeText:
			textParts = append(textParts, part.Text)

		case ContentTypeToolUse:
			args, err := json.Marshal(part.Input)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal tool input: %w", err)
			}

			toolCalls = append(toolCalls, core.ToolCall{
				ID:   part.ID,
				Type: "function",
				Function: core.FunctionCall{
					Name:      part.Name,
					Arguments: string(args),
				},
			})
		}
	}

	// Set content
	if len(textParts) > 0 {
		contentText := strings.Join(textParts, "")
		contentBytes, err := json.Marshal(contentText)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal content: %w", err)
		}
		message.Content = contentBytes
	}

	// Set tool calls
	if len(toolCalls) > 0 {
		toolCallsBytes, err := json.Marshal(toolCalls)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal tool calls: %w", err)
		}
		message.ToolCalls = toolCallsBytes
	}

	return message, nil
}

// convertFinishReason converts Claude finish reason to OpenAI format
func convertFinishReason(reason string) string {
	switch reason {
	case StopReasonEndTurn:
		return "stop"
	case StopReasonMaxTokens:
		return "length"
	case StopReasonStopSequence:
		return "stop"
	case StopReasonToolUse:
		return "tool_calls"
	default:
		return "stop"
	}
}
