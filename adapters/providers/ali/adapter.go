package ali

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"../../core"
)

// Adapter implements the Alibaba (Ali) adapter
type Adapter struct {
	config *core.AdapterConfig
}

// NewAdapter creates a new Alibaba adapter
func NewAdapter(config *core.AdapterConfig) *Adapter {
	if config.BaseURL == "" {
		config.BaseURL = "https://dashscope.aliyuncs.com/api/v1"
	}
	
	return &Adapter{
		config: config,
	}
}

// GetProviderName returns the provider name
func (a *Adapter) GetProviderName() string {
	return "ali"
}

// GetSupportedModels returns the list of supported models
func (a *Adapter) GetSupportedModels() []string {
	return []string{
		"qwen-turbo",
		"qwen-plus",
		"qwen-max",
		"qwen-max-1201",
		"qwen-max-longcontext",
		"qwen1.5-72b-chat",
		"qwen1.5-14b-chat",
		"qwen1.5-7b-chat",
		"qwen1.5-1.8b-chat",
		"qwen1.5-0.5b-chat",
		"qwen-72b-chat",
		"qwen-14b-chat",
		"qwen-7b-chat",
		"qwen-1.8b-longcontext-chat",
		"qwen-1.8b-chat",
		"text-embedding-v1",
		"text-embedding-v2",
		"text-embedding-async-v1",
		"text-embedding-async-v2",
	}
}

// ConvertRequest converts an OpenAI request to Alibaba format
func (a *Adapter) ConvertRequest(ctx context.Context, request *core.OpenAIRequest) (interface{}, error) {
	if request == nil {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, "request cannot be nil", http.StatusBadRequest, a.GetProviderName())
	}
	
	// Validate the model
	if !a.isModelSupported(request.Model) {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, fmt.Sprintf("model %s is not supported", request.Model), http.StatusBadRequest, a.GetProviderName())
	}
	
	// Handle embedding requests
	if strings.Contains(request.Model, "embedding") {
		return a.convertEmbeddingRequest(request)
	}
	
	// Convert to Alibaba format
	aliRequest := &AliRequest{
		Model: request.Model,
		Input: AliInput{
			Messages: make([]AliMessage, len(request.Messages)),
		},
		Parameters: AliParameters{
			ResultFormat: "message",
		},
	}
	
	// Convert messages
	for i, message := range request.Messages {
		aliRequest.Input.Messages[i] = AliMessage{
			Role:    message.Role,
			Content: message.GetStringContent(),
		}
	}
	
	// Set parameters
	if request.MaxTokens != nil {
		aliRequest.Parameters.MaxTokens = request.MaxTokens
	}
	if request.Temperature != nil {
		aliRequest.Parameters.Temperature = request.Temperature
	}
	if request.TopP != nil {
		aliRequest.Parameters.TopP = request.TopP
	}
	if request.TopK != nil {
		aliRequest.Parameters.TopK = request.TopK
	}
	
	// Handle stop sequences
	if request.Stop != nil {
		switch stop := request.Stop.(type) {
		case string:
			aliRequest.Parameters.Stop = []string{stop}
		case []string:
			aliRequest.Parameters.Stop = stop
		case []interface{}:
			stopSeqs := make([]string, 0, len(stop))
			for _, s := range stop {
				if str, ok := s.(string); ok {
					stopSeqs = append(stopSeqs, str)
				}
			}
			aliRequest.Parameters.Stop = stopSeqs
		}
	}
	
	// Handle streaming
	if request.Stream {
		aliRequest.Parameters.IncrementalOutput = true
	}
	
	// Handle tools/functions
	if len(request.Tools) > 0 {
		tools := make([]AliTool, len(request.Tools))
		for i, tool := range request.Tools {
			tools[i] = AliTool{
				Type:     tool.Type,
				Function: AliFunction{
					Name:        tool.Function.Name,
					Description: tool.Function.Description,
					Parameters:  tool.Function.Parameters,
				},
			}
		}
		aliRequest.Parameters.Tools = tools
	}
	
	return aliRequest, nil
}

// convertEmbeddingRequest converts an embedding request
func (a *Adapter) convertEmbeddingRequest(request *core.OpenAIRequest) (*AliEmbeddingRequest, error) {
	embeddingRequest := &AliEmbeddingRequest{
		Model: request.Model,
		Input: AliEmbeddingInput{},
		Parameters: AliEmbeddingParameters{
			TextType: "document",
		},
	}
	
	// Extract input from messages or prompt
	if len(request.Messages) > 0 {
		texts := make([]string, len(request.Messages))
		for i, message := range request.Messages {
			texts[i] = message.GetStringContent()
		}
		embeddingRequest.Input.Texts = texts
	} else if request.Prompt != nil {
		switch prompt := request.Prompt.(type) {
		case string:
			embeddingRequest.Input.Texts = []string{prompt}
		case []string:
			embeddingRequest.Input.Texts = prompt
		case []interface{}:
			texts := make([]string, len(prompt))
			for i, p := range prompt {
				if str, ok := p.(string); ok {
					texts[i] = str
				}
			}
			embeddingRequest.Input.Texts = texts
		}
	}
	
	return embeddingRequest, nil
}

// BuildRequestURL builds the request URL for the given request
func (a *Adapter) BuildRequestURL(baseURL string, request *core.OpenAIRequest) (string, error) {
	if baseURL == "" {
		baseURL = a.config.BaseURL
	}
	
	var endpoint string
	if strings.Contains(request.Model, "embedding") {
		endpoint = "/services/embeddings/text-embedding/text-embedding"
	} else {
		endpoint = "/services/aigc/text-generation/generation"
	}
	
	return core.BuildURL(baseURL, endpoint), nil
}

// SetupHeaders sets up the request headers
func (a *Adapter) SetupHeaders(headers map[string]string, apiKey string) error {
	if apiKey == "" {
		apiKey = a.config.APIKey
	}
	
	if apiKey == "" {
		return core.NewAdapterError(core.ErrorTypeAuthentication, "API key is required", http.StatusUnauthorized, a.GetProviderName())
	}
	
	if headers == nil {
		headers = make(map[string]string)
	}
	
	headers["Content-Type"] = "application/json"
	headers["Authorization"] = "Bearer " + apiKey
	headers["X-DashScope-SSE"] = "enable"
	
	return nil
}

// ExecuteRequest executes the HTTP request
func (a *Adapter) ExecuteRequest(ctx context.Context, httpClient *http.Client, url string, headers map[string]string, body []byte) (*http.Response, error) {
	if httpClient == nil {
		httpClient = a.config.HTTPClient
	}
	
	if httpClient == nil {
		httpClient = &http.Client{}
	}
	
	client := core.NewHTTPClientWithClient(httpClient)
	return client.DoRequest(ctx, "POST", url, headers, body)
}

// ConvertResponse converts the Alibaba response to OpenAI format
func (a *Adapter) ConvertResponse(ctx context.Context, response *http.Response, isStream bool) (*core.OpenAIResponse, error) {
	if !core.IsSuccessStatusCode(response.StatusCode) {
		return nil, a.HandleError(response)
	}
	
	if isStream {
		return nil, core.NewAdapterError(core.ErrorTypeServerError, "streaming not implemented in this example", http.StatusNotImplemented, a.GetProviderName())
	}
	
	var aliResponse AliResponse
	if err := core.ParseJSONResponse(response, &aliResponse); err != nil {
		return nil, core.WrapError(err, core.ErrorTypeServerError, "failed to parse Alibaba response", http.StatusInternalServerError, a.GetProviderName())
	}
	
	// Convert Alibaba response to OpenAI format
	return ConvertAliToOpenAI(&aliResponse)
}

// HandleError handles Alibaba-specific errors
func (a *Adapter) HandleError(response *http.Response) error {
	body, err := core.ReadResponseBody(response)
	if err != nil {
		return core.WrapError(err, core.ErrorTypeServerError, "failed to read error response", response.StatusCode, a.GetProviderName())
	}
	
	var errorResponse AliError
	if err := json.Unmarshal(body, &errorResponse); err != nil {
		return core.NewAdapterError(
			core.MapHTTPStatusToErrorType(response.StatusCode),
			string(body),
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	return core.NewAdapterError(
		core.MapHTTPStatusToErrorType(response.StatusCode),
		errorResponse.Message,
		response.StatusCode,
		a.GetProviderName(),
	)
}

// isModelSupported checks if the model is supported by this adapter
func (a *Adapter) isModelSupported(model string) bool {
	supportedModels := a.GetSupportedModels()
	for _, supportedModel := range supportedModels {
		if model == supportedModel {
			return true
		}
		// Also check for model prefixes
		if strings.HasPrefix(model, supportedModel) {
			return true
		}
	}
	return false
}
