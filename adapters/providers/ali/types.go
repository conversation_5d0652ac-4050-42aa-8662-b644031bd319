package ali

import (
	"encoding/json"
	"time"

	"../../core"
)

// AliRequest represents an Alibaba API request
type AliRequest struct {
	Model      string        `json:"model"`
	Input      AliInput      `json:"input"`
	Parameters AliParameters `json:"parameters"`
}

// AliInput represents the input for Alibaba API
type AliInput struct {
	Messages []AliMessage `json:"messages"`
}

// AliMessage represents a message in Alibaba format
type AliMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// AliParameters represents parameters for Alibaba API
type AliParameters struct {
	ResultFormat      string    `json:"result_format"`
	MaxTokens         *int      `json:"max_tokens,omitempty"`
	Temperature       *float64  `json:"temperature,omitempty"`
	TopP              *float64  `json:"top_p,omitempty"`
	TopK              *int      `json:"top_k,omitempty"`
	Stop              []string  `json:"stop,omitempty"`
	IncrementalOutput bool      `json:"incremental_output,omitempty"`
	Tools             []AliTool `json:"tools,omitempty"`
}

// AliTool represents a tool definition
type AliTool struct {
	Type     string      `json:"type"`
	Function AliFunction `json:"function"`
}

// AliFunction represents a function definition
type AliFunction struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Parameters  interface{} `json:"parameters"`
}

// AliResponse represents an Alibaba API response
type AliResponse struct {
	Output    AliOutput `json:"output"`
	Usage     AliUsage  `json:"usage"`
	RequestID string    `json:"request_id"`
}

// AliOutput represents the output from Alibaba API
type AliOutput struct {
	Text         string       `json:"text,omitempty"`
	FinishReason string       `json:"finish_reason,omitempty"`
	Choices      []AliChoice  `json:"choices,omitempty"`
}

// AliChoice represents a choice in the response
type AliChoice struct {
	FinishReason string     `json:"finish_reason"`
	Message      AliMessage `json:"message"`
}

// AliUsage represents usage information
type AliUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
	TotalTokens  int `json:"total_tokens"`
}

// AliEmbeddingRequest represents an embedding request
type AliEmbeddingRequest struct {
	Model      string                 `json:"model"`
	Input      AliEmbeddingInput      `json:"input"`
	Parameters AliEmbeddingParameters `json:"parameters"`
}

// AliEmbeddingInput represents embedding input
type AliEmbeddingInput struct {
	Texts []string `json:"texts"`
}

// AliEmbeddingParameters represents embedding parameters
type AliEmbeddingParameters struct {
	TextType string `json:"text_type"`
}

// AliEmbeddingResponse represents an embedding response
type AliEmbeddingResponse struct {
	Output    AliEmbeddingOutput `json:"output"`
	Usage     AliUsage           `json:"usage"`
	RequestID string             `json:"request_id"`
}

// AliEmbeddingOutput represents embedding output
type AliEmbeddingOutput struct {
	Embeddings []AliEmbeddingData `json:"embeddings"`
}

// AliEmbeddingData represents embedding data
type AliEmbeddingData struct {
	TextIndex int       `json:"text_index"`
	Embedding []float64 `json:"embedding"`
}

// AliStreamResponse represents a streaming response
type AliStreamResponse struct {
	Output    AliOutput `json:"output"`
	Usage     AliUsage  `json:"usage"`
	RequestID string    `json:"request_id"`
}

// AliError represents an Alibaba API error
type AliError struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"request_id"`
}

// Model categories
const (
	ModelCategoryQwen      = "qwen"
	ModelCategoryEmbedding = "embedding"
)

// Text types for embeddings
const (
	TextTypeQuery    = "query"
	TextTypeDocument = "document"
)

// Result formats
const (
	ResultFormatText    = "text"
	ResultFormatMessage = "message"
)

// Finish reasons
const (
	FinishReasonStop   = "stop"
	FinishReasonLength = "length"
	FinishReasonNull   = "null"
)

// ConvertAliToOpenAI converts an Alibaba response to OpenAI format
func ConvertAliToOpenAI(response *AliResponse) (*core.OpenAIResponse, error) {
	openaiResponse := &core.OpenAIResponse{
		ID:      response.RequestID,
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   "qwen", // Default model name
		Choices: []core.Choice{},
	}
	
	// Convert usage
	openaiResponse.Usage = &core.Usage{
		PromptTokens:     response.Usage.InputTokens,
		CompletionTokens: response.Usage.OutputTokens,
		TotalTokens:      response.Usage.TotalTokens,
	}
	
	// Handle different output formats
	if len(response.Output.Choices) > 0 {
		// Message format
		for i, choice := range response.Output.Choices {
			openaiChoice := core.Choice{
				Index: i,
				Message: &core.Message{
					Role: choice.Message.Role,
				},
				FinishReason: convertFinishReason(choice.FinishReason),
			}
			
			// Set content
			contentBytes, err := json.Marshal(choice.Message.Content)
			if err != nil {
				return nil, err
			}
			openaiChoice.Message.Content = contentBytes
			
			openaiResponse.Choices = append(openaiResponse.Choices, openaiChoice)
		}
	} else if response.Output.Text != "" {
		// Text format
		choice := core.Choice{
			Index: 0,
			Message: &core.Message{
				Role: "assistant",
			},
			FinishReason: convertFinishReason(response.Output.FinishReason),
		}
		
		// Set content
		contentBytes, err := json.Marshal(response.Output.Text)
		if err != nil {
			return nil, err
		}
		choice.Message.Content = contentBytes
		
		openaiResponse.Choices = append(openaiResponse.Choices, choice)
	}
	
	return openaiResponse, nil
}

// convertFinishReason converts Alibaba finish reason to OpenAI format
func convertFinishReason(reason string) *string {
	var openaiReason string
	switch reason {
	case FinishReasonStop:
		openaiReason = "stop"
	case FinishReasonLength:
		openaiReason = "length"
	case FinishReasonNull:
		openaiReason = "stop"
	default:
		openaiReason = "stop"
	}
	return &openaiReason
}

// IsEmbeddingModel checks if the model is an embedding model
func IsEmbeddingModel(model string) bool {
	return strings.Contains(model, "embedding")
}

// IsQwenModel checks if the model is a Qwen model
func IsQwenModel(model string) bool {
	return strings.Contains(model, "qwen")
}

// GetModelCategory returns the category of the model
func GetModelCategory(model string) string {
	if IsEmbeddingModel(model) {
		return ModelCategoryEmbedding
	}
	if IsQwenModel(model) {
		return ModelCategoryQwen
	}
	return ModelCategoryQwen // Default
}
