package aws

import (
	"../../core"
	"time"
)

// BedrockClaudeRequest represents a Claude request for Bedrock
type BedrockClaudeRequest struct {
	AnthropicVersion string                  `json:"anthropic_version"`
	MaxTokens        int                     `json:"max_tokens"`
	Messages         []BedrockClaudeMessage  `json:"messages,omitempty"`
	System           string                  `json:"system,omitempty"`
	Temperature      *float64                `json:"temperature,omitempty"`
	TopP             *float64                `json:"top_p,omitempty"`
	TopK             *int                    `json:"top_k,omitempty"`
	StopSequences    []string                `json:"stop_sequences,omitempty"`
	Tools            []BedrockClaudeTool     `json:"tools,omitempty"`
}

// BedrockClaudeMessage represents a message in Claude format
type BedrockClaudeMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// BedrockClaudeTool represents a tool for Claude
type BedrockClaudeTool struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	InputSchema map[string]interface{} `json:"input_schema"`
}

// BedrockTitanRequest represents a Titan request for Bedrock
type BedrockTitanRequest struct {
	InputText            string             `json:"inputText"`
	TextGenerationConfig BedrockTitanConfig `json:"textGenerationConfig"`
}

// BedrockTitanConfig represents Titan generation config
type BedrockTitanConfig struct {
	MaxTokenCount int      `json:"maxTokenCount"`
	Temperature   float64  `json:"temperature"`
	TopP          float64  `json:"topP"`
	StopSequences []string `json:"stopSequences"`
}

// BedrockLlamaRequest represents a Llama request for Bedrock
type BedrockLlamaRequest struct {
	Prompt      string  `json:"prompt"`
	MaxGenLen   int     `json:"max_gen_len"`
	Temperature float64 `json:"temperature"`
	TopP        float64 `json:"top_p"`
}

// BedrockMistralRequest represents a Mistral request for Bedrock
type BedrockMistralRequest struct {
	Prompt      string  `json:"prompt"`
	MaxTokens   int     `json:"max_tokens"`
	Temperature float64 `json:"temperature"`
	TopP        float64 `json:"top_p"`
	TopK        int     `json:"top_k"`
}

// BedrockCohereRequest represents a Cohere request for Bedrock
type BedrockCohereRequest struct {
	Prompt      string  `json:"prompt"`
	MaxTokens   int     `json:"max_tokens"`
	Temperature float64 `json:"temperature"`
	P           float64 `json:"p"`
	K           int     `json:"k"`
}

// BedrockResponse represents a generic Bedrock response
type BedrockResponse struct {
	// Claude response format
	ID           string                    `json:"id,omitempty"`
	Type         string                    `json:"type,omitempty"`
	Role         string                    `json:"role,omitempty"`
	Content      []BedrockClaudeContent    `json:"content,omitempty"`
	Model        string                    `json:"model,omitempty"`
	StopReason   string                    `json:"stop_reason,omitempty"`
	Usage        BedrockClaudeUsage        `json:"usage,omitempty"`
	
	// Titan response format
	Results      []BedrockTitanResult      `json:"results,omitempty"`
	InputTextTokenCount int                `json:"inputTextTokenCount,omitempty"`
	
	// Llama response format
	Generation   string                    `json:"generation,omitempty"`
	PromptTokenCount int                   `json:"prompt_token_count,omitempty"`
	GenerationTokenCount int              `json:"generation_token_count,omitempty"`
	StopReason2  string                    `json:"stop_reason,omitempty"`
	
	// Mistral response format
	Outputs      []BedrockMistralOutput    `json:"outputs,omitempty"`
	
	// Cohere response format
	Generations  []BedrockCohereGeneration `json:"generations,omitempty"`
}

// BedrockClaudeContent represents Claude content
type BedrockClaudeContent struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

// BedrockClaudeUsage represents Claude usage
type BedrockClaudeUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
}

// BedrockTitanResult represents Titan result
type BedrockTitanResult struct {
	TokenCount       int    `json:"tokenCount"`
	OutputText       string `json:"outputText"`
	CompletionReason string `json:"completionReason"`
}

// BedrockMistralOutput represents Mistral output
type BedrockMistralOutput struct {
	Text         string `json:"text"`
	StopReason   string `json:"stop_reason"`
}

// BedrockCohereGeneration represents Cohere generation
type BedrockCohereGeneration struct {
	FinishReason string `json:"finish_reason"`
	ID           string `json:"id"`
	Text         string `json:"text"`
}

// BedrockError represents a Bedrock error
type BedrockError struct {
	Message string `json:"message"`
	Type    string `json:"__type"`
}

// BedrockStreamResponse represents a streaming response
type BedrockStreamResponse struct {
	Bytes []byte `json:"bytes"`
}

// Model families
const (
	ModelFamilyClaude  = "claude"
	ModelFamilyTitan   = "titan"
	ModelFamilyLlama   = "llama"
	ModelFamilyMistral = "mistral"
	ModelFamilyCohere  = "cohere"
)

// AWS regions
const (
	RegionUSEast1      = "us-east-1"
	RegionUSWest2      = "us-west-2"
	RegionEUWest1      = "eu-west-1"
	RegionAPSoutheast1 = "ap-southeast-1"
)

// ConvertBedrockToOpenAI converts a Bedrock response to OpenAI format
func ConvertBedrockToOpenAI(response *BedrockResponse) (*core.OpenAIResponse, error) {
	openaiResponse := &core.OpenAIResponse{
		ID:      response.ID,
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   response.Model,
		Choices: []core.Choice{},
	}
	
	// Handle Claude response
	if len(response.Content) > 0 {
		var textContent string
		for _, content := range response.Content {
			if content.Type == "text" {
				textContent += content.Text
			}
		}
		
		choice := core.Choice{
			Index: 0,
			Message: &core.Message{
				Role: "assistant",
			},
			FinishReason: convertFinishReason(response.StopReason),
		}
		
		// Set content
		if textContent != "" {
			contentBytes, _ := json.Marshal(textContent)
			choice.Message.Content = contentBytes
		}
		
		openaiResponse.Choices = append(openaiResponse.Choices, choice)
		
		// Set usage
		if response.Usage.InputTokens > 0 || response.Usage.OutputTokens > 0 {
			openaiResponse.Usage = &core.Usage{
				PromptTokens:     response.Usage.InputTokens,
				CompletionTokens: response.Usage.OutputTokens,
				TotalTokens:      response.Usage.InputTokens + response.Usage.OutputTokens,
			}
		}
	}
	
	// Handle Titan response
	if len(response.Results) > 0 {
		for i, result := range response.Results {
			choice := core.Choice{
				Index: i,
				Message: &core.Message{
					Role: "assistant",
				},
				FinishReason: convertFinishReason(result.CompletionReason),
			}
			
			contentBytes, _ := json.Marshal(result.OutputText)
			choice.Message.Content = contentBytes
			
			openaiResponse.Choices = append(openaiResponse.Choices, choice)
		}
		
		if response.InputTextTokenCount > 0 {
			openaiResponse.Usage = &core.Usage{
				PromptTokens:     response.InputTextTokenCount,
				CompletionTokens: 0, // Titan doesn't provide this
				TotalTokens:      response.InputTextTokenCount,
			}
		}
	}
	
	// Handle Llama response
	if response.Generation != "" {
		choice := core.Choice{
			Index: 0,
			Message: &core.Message{
				Role: "assistant",
			},
			FinishReason: convertFinishReason(response.StopReason2),
		}
		
		contentBytes, _ := json.Marshal(response.Generation)
		choice.Message.Content = contentBytes
		
		openaiResponse.Choices = append(openaiResponse.Choices, choice)
		
		if response.PromptTokenCount > 0 || response.GenerationTokenCount > 0 {
			openaiResponse.Usage = &core.Usage{
				PromptTokens:     response.PromptTokenCount,
				CompletionTokens: response.GenerationTokenCount,
				TotalTokens:      response.PromptTokenCount + response.GenerationTokenCount,
			}
		}
	}
	
	// Handle Mistral response
	if len(response.Outputs) > 0 {
		for i, output := range response.Outputs {
			choice := core.Choice{
				Index: i,
				Message: &core.Message{
					Role: "assistant",
				},
				FinishReason: convertFinishReason(output.StopReason),
			}
			
			contentBytes, _ := json.Marshal(output.Text)
			choice.Message.Content = contentBytes
			
			openaiResponse.Choices = append(openaiResponse.Choices, choice)
		}
	}
	
	// Handle Cohere response
	if len(response.Generations) > 0 {
		for i, generation := range response.Generations {
			choice := core.Choice{
				Index: i,
				Message: &core.Message{
					Role: "assistant",
				},
				FinishReason: convertFinishReason(generation.FinishReason),
			}
			
			contentBytes, _ := json.Marshal(generation.Text)
			choice.Message.Content = contentBytes
			
			openaiResponse.Choices = append(openaiResponse.Choices, choice)
		}
	}
	
	return openaiResponse, nil
}

// convertFinishReason converts Bedrock finish reasons to OpenAI format
func convertFinishReason(reason string) *string {
	var openaiReason string
	switch reason {
	case "end_turn", "stop_sequence", "COMPLETE":
		openaiReason = "stop"
	case "max_tokens", "length":
		openaiReason = "length"
	case "tool_use":
		openaiReason = "tool_calls"
	default:
		openaiReason = "stop"
	}
	return &openaiReason
}
