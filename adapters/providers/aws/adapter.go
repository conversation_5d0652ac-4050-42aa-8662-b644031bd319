package aws

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"../../core"
)

// Adapter implements the AWS Bedrock adapter
type Adapter struct {
	config *core.AdapterConfig
}

// NewAdapter creates a new AWS Bedrock adapter
func NewAdapter(config *core.AdapterConfig) *Adapter {
	if config.BaseURL == "" {
		config.BaseURL = "https://bedrock-runtime.us-east-1.amazonaws.com"
	}
	
	return &Adapter{
		config: config,
	}
}

// GetProviderName returns the provider name
func (a *Adapter) GetProviderName() string {
	return "aws"
}

// GetSupportedModels returns the list of supported models
func (a *Adapter) GetSupportedModels() []string {
	return []string{
		"claude-instant-1.2",
		"claude-2.0",
		"claude-2.1",
		"claude-3-sonnet-20240229",
		"claude-3-opus-20240229",
		"claude-3-haiku-20240307",
		"claude-3-5-sonnet-20240620",
		"claude-3-5-sonnet-20241022",
		"claude-3-5-haiku-20241022",
		"claude-3-7-sonnet-20250219",
		"claude-sonnet-4-20250514",
		"claude-opus-4-20250514",
		"amazon.titan-text-lite-v1",
		"amazon.titan-text-express-v1",
		"amazon.titan-embed-text-v1",
		"amazon.titan-embed-text-v2:0",
		"meta.llama2-13b-chat-v1",
		"meta.llama2-70b-chat-v1",
		"meta.llama3-8b-instruct-v1:0",
		"meta.llama3-70b-instruct-v1:0",
		"mistral.mistral-7b-instruct-v0:2",
		"mistral.mixtral-8x7b-instruct-v0:1",
		"mistral.mistral-large-2402-v1:0",
		"cohere.command-text-v14",
		"cohere.command-light-text-v14",
		"cohere.embed-english-v3",
		"cohere.embed-multilingual-v3",
	}
}

// ConvertRequest converts an OpenAI request to AWS Bedrock format
func (a *Adapter) ConvertRequest(ctx context.Context, request *core.OpenAIRequest) (interface{}, error) {
	if request == nil {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, "request cannot be nil", http.StatusBadRequest, a.GetProviderName())
	}
	
	// Validate the model
	if !a.isModelSupported(request.Model) {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, fmt.Sprintf("model %s is not supported", request.Model), http.StatusBadRequest, a.GetProviderName())
	}
	
	// Get the actual Bedrock model ID
	bedrockModelID, err := a.getBedrockModelID(request.Model)
	if err != nil {
		return nil, core.WrapError(err, core.ErrorTypeInvalidRequest, "failed to get Bedrock model ID", http.StatusBadRequest, a.GetProviderName())
	}
	
	// Convert based on model family
	if strings.HasPrefix(bedrockModelID, "anthropic.claude") {
		return a.convertClaudeRequest(request, bedrockModelID)
	} else if strings.HasPrefix(bedrockModelID, "amazon.titan") {
		return a.convertTitanRequest(request, bedrockModelID)
	} else if strings.HasPrefix(bedrockModelID, "meta.llama") {
		return a.convertLlamaRequest(request, bedrockModelID)
	} else if strings.HasPrefix(bedrockModelID, "mistral") {
		return a.convertMistralRequest(request, bedrockModelID)
	} else if strings.HasPrefix(bedrockModelID, "cohere") {
		return a.convertCohereRequest(request, bedrockModelID)
	}
	
	return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, fmt.Sprintf("unsupported model family for %s", bedrockModelID), http.StatusBadRequest, a.GetProviderName())
}

// convertClaudeRequest converts to Claude format for Bedrock
func (a *Adapter) convertClaudeRequest(request *core.OpenAIRequest, modelID string) (*BedrockClaudeRequest, error) {
	claudeRequest := &BedrockClaudeRequest{
		AnthropicVersion: "bedrock-2023-05-31",
		MaxTokens:        4096, // Default
	}
	
	if request.MaxTokens != nil {
		claudeRequest.MaxTokens = *request.MaxTokens
	}
	if request.Temperature != nil {
		claudeRequest.Temperature = request.Temperature
	}
	if request.TopP != nil {
		claudeRequest.TopP = request.TopP
	}
	
	// Convert messages
	var systemMessage string
	var messages []BedrockClaudeMessage
	
	for _, message := range request.Messages {
		if message.Role == "system" {
			systemMessage = message.GetStringContent()
		} else {
			messages = append(messages, BedrockClaudeMessage{
				Role:    message.Role,
				Content: message.GetStringContent(),
			})
		}
	}
	
	claudeRequest.System = systemMessage
	claudeRequest.Messages = messages
	
	// Handle stop sequences
	if request.Stop != nil {
		switch stop := request.Stop.(type) {
		case string:
			claudeRequest.StopSequences = []string{stop}
		case []string:
			claudeRequest.StopSequences = stop
		case []interface{}:
			stopSeqs := make([]string, 0, len(stop))
			for _, s := range stop {
				if str, ok := s.(string); ok {
					stopSeqs = append(stopSeqs, str)
				}
			}
			claudeRequest.StopSequences = stopSeqs
		}
	}
	
	return claudeRequest, nil
}

// convertTitanRequest converts to Titan format for Bedrock
func (a *Adapter) convertTitanRequest(request *core.OpenAIRequest, modelID string) (*BedrockTitanRequest, error) {
	// Combine all messages into a single prompt
	var promptBuilder strings.Builder
	for _, message := range request.Messages {
		promptBuilder.WriteString(fmt.Sprintf("%s: %s\n", message.Role, message.GetStringContent()))
	}
	
	titanRequest := &BedrockTitanRequest{
		InputText: promptBuilder.String(),
		TextGenerationConfig: BedrockTitanConfig{
			MaxTokenCount:   512, // Default
			Temperature:     0.7, // Default
			TopP:            0.9, // Default
			StopSequences:   []string{},
		},
	}
	
	if request.MaxTokens != nil {
		titanRequest.TextGenerationConfig.MaxTokenCount = *request.MaxTokens
	}
	if request.Temperature != nil {
		titanRequest.TextGenerationConfig.Temperature = *request.Temperature
	}
	if request.TopP != nil {
		titanRequest.TextGenerationConfig.TopP = *request.TopP
	}
	
	return titanRequest, nil
}

// convertLlamaRequest converts to Llama format for Bedrock
func (a *Adapter) convertLlamaRequest(request *core.OpenAIRequest, modelID string) (*BedrockLlamaRequest, error) {
	// Format messages for Llama
	var promptBuilder strings.Builder
	for _, message := range request.Messages {
		switch message.Role {
		case "system":
			promptBuilder.WriteString(fmt.Sprintf("<s>[INST] <<SYS>>\n%s\n<</SYS>>\n\n", message.GetStringContent()))
		case "user":
			promptBuilder.WriteString(fmt.Sprintf("%s [/INST]", message.GetStringContent()))
		case "assistant":
			promptBuilder.WriteString(fmt.Sprintf(" %s </s><s>[INST] ", message.GetStringContent()))
		}
	}
	
	llamaRequest := &BedrockLlamaRequest{
		Prompt:      promptBuilder.String(),
		MaxGenLen:   512, // Default
		Temperature: 0.7, // Default
		TopP:        0.9, // Default
	}
	
	if request.MaxTokens != nil {
		llamaRequest.MaxGenLen = *request.MaxTokens
	}
	if request.Temperature != nil {
		llamaRequest.Temperature = *request.Temperature
	}
	if request.TopP != nil {
		llamaRequest.TopP = *request.TopP
	}
	
	return llamaRequest, nil
}

// convertMistralRequest converts to Mistral format for Bedrock
func (a *Adapter) convertMistralRequest(request *core.OpenAIRequest, modelID string) (*BedrockMistralRequest, error) {
	// Format prompt for Mistral
	var promptBuilder strings.Builder
	for _, message := range request.Messages {
		promptBuilder.WriteString(fmt.Sprintf("[INST] %s [/INST]", message.GetStringContent()))
	}
	
	mistralRequest := &BedrockMistralRequest{
		Prompt:      promptBuilder.String(),
		MaxTokens:   512, // Default
		Temperature: 0.7, // Default
		TopP:        0.9, // Default
		TopK:        50,  // Default
	}
	
	if request.MaxTokens != nil {
		mistralRequest.MaxTokens = *request.MaxTokens
	}
	if request.Temperature != nil {
		mistralRequest.Temperature = *request.Temperature
	}
	if request.TopP != nil {
		mistralRequest.TopP = *request.TopP
	}
	if request.TopK != nil {
		mistralRequest.TopK = *request.TopK
	}
	
	return mistralRequest, nil
}

// convertCohereRequest converts to Cohere format for Bedrock
func (a *Adapter) convertCohereRequest(request *core.OpenAIRequest, modelID string) (*BedrockCohereRequest, error) {
	// Combine messages into prompt
	var promptBuilder strings.Builder
	for _, message := range request.Messages {
		promptBuilder.WriteString(message.GetStringContent() + "\n")
	}
	
	cohereRequest := &BedrockCohereRequest{
		Prompt:      promptBuilder.String(),
		MaxTokens:   512, // Default
		Temperature: 0.7, // Default
		P:           0.9, // Default
		K:           0,   // Default
	}
	
	if request.MaxTokens != nil {
		cohereRequest.MaxTokens = *request.MaxTokens
	}
	if request.Temperature != nil {
		cohereRequest.Temperature = *request.Temperature
	}
	if request.TopP != nil {
		cohereRequest.P = *request.TopP
	}
	if request.TopK != nil {
		cohereRequest.K = *request.TopK
	}
	
	return cohereRequest, nil
}

// BuildRequestURL builds the request URL for the given request
func (a *Adapter) BuildRequestURL(baseURL string, request *core.OpenAIRequest) (string, error) {
	if baseURL == "" {
		baseURL = a.config.BaseURL
	}
	
	bedrockModelID, err := a.getBedrockModelID(request.Model)
	if err != nil {
		return "", err
	}
	
	endpoint := fmt.Sprintf("/model/%s/invoke", bedrockModelID)
	if request.Stream {
		endpoint = fmt.Sprintf("/model/%s/invoke-with-response-stream", bedrockModelID)
	}
	
	return core.BuildURL(baseURL, endpoint), nil
}

// SetupHeaders sets up the request headers for AWS authentication
func (a *Adapter) SetupHeaders(headers map[string]string, apiKey string) error {
	if headers == nil {
		headers = make(map[string]string)
	}
	
	headers["Content-Type"] = "application/json"
	headers["Accept"] = "application/json"
	
	// AWS requires proper AWS Signature V4 authentication
	// This is a simplified version - in practice, you'd need proper AWS SDK integration
	if apiKey != "" {
		headers["Authorization"] = apiKey // Should be AWS Signature V4
	}
	
	return nil
}

// ExecuteRequest executes the HTTP request
func (a *Adapter) ExecuteRequest(ctx context.Context, httpClient *http.Client, url string, headers map[string]string, body []byte) (*http.Response, error) {
	if httpClient == nil {
		httpClient = a.config.HTTPClient
	}
	
	if httpClient == nil {
		httpClient = &http.Client{}
	}
	
	client := core.NewHTTPClientWithClient(httpClient)
	return client.DoRequest(ctx, "POST", url, headers, body)
}

// ConvertResponse converts the AWS Bedrock response to OpenAI format
func (a *Adapter) ConvertResponse(ctx context.Context, response *http.Response, isStream bool) (*core.OpenAIResponse, error) {
	if !core.IsSuccessStatusCode(response.StatusCode) {
		return nil, a.HandleError(response)
	}
	
	if isStream {
		return nil, core.NewAdapterError(core.ErrorTypeServerError, "streaming not implemented in this example", http.StatusNotImplemented, a.GetProviderName())
	}
	
	// Parse the response based on the model family
	// This is simplified - in practice, you'd need to handle different response formats
	var bedrockResponse BedrockResponse
	if err := core.ParseJSONResponse(response, &bedrockResponse); err != nil {
		return nil, core.WrapError(err, core.ErrorTypeServerError, "failed to parse Bedrock response", http.StatusInternalServerError, a.GetProviderName())
	}
	
	return ConvertBedrockToOpenAI(&bedrockResponse)
}

// HandleError handles AWS Bedrock-specific errors
func (a *Adapter) HandleError(response *http.Response) error {
	body, err := core.ReadResponseBody(response)
	if err != nil {
		return core.WrapError(err, core.ErrorTypeServerError, "failed to read error response", response.StatusCode, a.GetProviderName())
	}
	
	var errorResponse BedrockError
	if err := json.Unmarshal(body, &errorResponse); err != nil {
		return core.NewAdapterError(
			core.MapHTTPStatusToErrorType(response.StatusCode),
			string(body),
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	return core.NewAdapterError(
		core.MapHTTPStatusToErrorType(response.StatusCode),
		errorResponse.Message,
		response.StatusCode,
		a.GetProviderName(),
	)
}

// getBedrockModelID maps OpenAI model names to Bedrock model IDs
func (a *Adapter) getBedrockModelID(model string) (string, error) {
	modelMap := map[string]string{
		"claude-instant-1.2":         "anthropic.claude-instant-v1",
		"claude-2.0":                 "anthropic.claude-v2",
		"claude-2.1":                 "anthropic.claude-v2:1",
		"claude-3-sonnet-20240229":   "anthropic.claude-3-sonnet-20240229-v1:0",
		"claude-3-opus-20240229":     "anthropic.claude-3-opus-20240229-v1:0",
		"claude-3-haiku-20240307":    "anthropic.claude-3-haiku-20240307-v1:0",
		"claude-3-5-sonnet-20240620": "anthropic.claude-3-5-sonnet-20240620-v1:0",
		"claude-3-5-sonnet-20241022": "anthropic.claude-3-5-sonnet-20241022-v2:0",
		"claude-3-5-haiku-20241022":  "anthropic.claude-3-5-haiku-20241022-v1:0",
	}
	
	if bedrockID, exists := modelMap[model]; exists {
		return bedrockID, nil
	}
	
	// If not in map, assume it's already a Bedrock model ID
	return model, nil
}

// isModelSupported checks if the model is supported by this adapter
func (a *Adapter) isModelSupported(model string) bool {
	supportedModels := a.GetSupportedModels()
	for _, supportedModel := range supportedModels {
		if model == supportedModel {
			return true
		}
	}
	return false
}
