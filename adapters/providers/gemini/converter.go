package gemini

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"../../core"
)

// ConvertOpenAIToGemini converts an OpenAI request to Gemini format
func ConvertOpenAIToGemini(request *core.OpenAIRequest) (*GeminiRequest, error) {
	geminiRequest := &GeminiRequest{
		SafetySettings: DefaultSafetySettings(),
		GenerationConfig: GeminiGenerationConfig{
			Temperature:     request.Temperature,
			TopP:            request.TopP,
			TopK:            request.TopK,
			MaxOutputTokens: request.MaxTokens,
		},
	}

	// Set candidate count
	if request.N != nil && *request.N > 0 {
		geminiRequest.GenerationConfig.CandidateCount = request.N
	}

	// Convert stop sequences
	if request.Stop != nil {
		switch stop := request.Stop.(type) {
		case string:
			geminiRequest.GenerationConfig.StopSequences = []string{stop}
		case []string:
			geminiRequest.GenerationConfig.StopSequences = stop
		case []interface{}:
			stopSeqs := make([]string, 0, len(stop))
			for _, s := range stop {
				if str, ok := s.(string); ok {
					stopSeqs = append(stopSeqs, str)
				}
			}
			geminiRequest.GenerationConfig.StopSequences = stopSeqs
		}
	}

	// Convert messages
	contents, systemInstruction, err := convertMessages(request.Messages)
	if err != nil {
		return nil, err
	}
	
	geminiRequest.Contents = contents
	geminiRequest.SystemInstruction = systemInstruction

	// Convert tools/functions
	if len(request.Tools) > 0 || len(request.Functions) > 0 {
		tools, err := convertTools(request.Tools, request.Functions)
		if err != nil {
			return nil, err
		}
		geminiRequest.Tools = tools
	}

	return geminiRequest, nil
}

// convertMessages converts OpenAI messages to Gemini contents
func convertMessages(messages []core.Message) ([]GeminiContent, *GeminiContent, error) {
	var contents []GeminiContent
	var systemInstruction *GeminiContent

	for _, message := range messages {
		switch message.Role {
		case "system":
			// Gemini handles system messages as systemInstruction
			content, err := message.ParseContent()
			if err != nil {
				return nil, nil, fmt.Errorf("failed to parse system message content: %w", err)
			}
			
			parts, err := convertContentParts(content)
			if err != nil {
				return nil, nil, fmt.Errorf("failed to convert system message parts: %w", err)
			}
			
			systemInstruction = &GeminiContent{
				Parts: parts,
			}

		case "user":
			content, err := message.ParseContent()
			if err != nil {
				return nil, nil, fmt.Errorf("failed to parse user message content: %w", err)
			}
			
			parts, err := convertContentParts(content)
			if err != nil {
				return nil, nil, fmt.Errorf("failed to convert user message parts: %w", err)
			}
			
			contents = append(contents, GeminiContent{
				Role:  "user",
				Parts: parts,
			})

		case "assistant":
			parts := []GeminiPart{}
			
			// Handle text content
			if message.Content != nil {
				content, err := message.ParseContent()
				if err != nil {
					return nil, nil, fmt.Errorf("failed to parse assistant message content: %w", err)
				}
				
				textParts, err := convertContentParts(content)
				if err != nil {
					return nil, nil, fmt.Errorf("failed to convert assistant message parts: %w", err)
				}
				parts = append(parts, textParts...)
			}
			
			// Handle tool calls
			if message.ToolCalls != nil {
				var toolCalls []core.ToolCall
				if err := json.Unmarshal(message.ToolCalls, &toolCalls); err != nil {
					return nil, nil, fmt.Errorf("failed to parse tool calls: %w", err)
				}
				
				for _, toolCall := range toolCalls {
					if toolCall.Type == "function" {
						var args map[string]interface{}
						if err := json.Unmarshal([]byte(toolCall.Function.Arguments), &args); err != nil {
							return nil, nil, fmt.Errorf("failed to parse function arguments: %w", err)
						}
						
						parts = append(parts, GeminiPart{
							FunctionCall: &GeminiFunctionCall{
								Name: toolCall.Function.Name,
								Args: args,
							},
						})
					}
				}
			}
			
			contents = append(contents, GeminiContent{
				Role:  "model",
				Parts: parts,
			})

		case "tool":
			// Handle tool responses
			if message.ToolCallID != "" {
				var response map[string]interface{}
				content := message.GetStringContent()
				if content != "" {
					response = map[string]interface{}{
						"result": content,
					}
				}
				
				parts := []GeminiPart{
					{
						FunctionResponse: &GeminiFunctionResponse{
							Name:     message.Name,
							Response: response,
						},
					},
				}
				
				contents = append(contents, GeminiContent{
					Role:  "function",
					Parts: parts,
				})
			}
		}
	}

	return contents, systemInstruction, nil
}

// convertContentParts converts OpenAI content parts to Gemini parts
func convertContentParts(content []core.MediaContent) ([]GeminiPart, error) {
	var parts []GeminiPart

	for _, part := range content {
		switch part.Type {
		case "text":
			parts = append(parts, GeminiPart{
				Text: part.Text,
			})

		case "image_url":
			if part.ImageURL != nil {
				// Handle base64 images
				if strings.HasPrefix(part.ImageURL.URL, "data:") {
					// Extract mime type and data from data URL
					dataParts := strings.SplitN(part.ImageURL.URL, ",", 2)
					if len(dataParts) == 2 {
						mimeType := "image/jpeg" // default
						if strings.Contains(dataParts[0], "image/") {
							start := strings.Index(dataParts[0], "image/")
							end := strings.Index(dataParts[0][start:], ";")
							if end > 0 {
								mimeType = dataParts[0][start : start+end]
							}
						}
						
						parts = append(parts, GeminiPart{
							InlineData: &GeminiInlineData{
								MimeType: mimeType,
								Data:     dataParts[1],
							},
						})
					}
				} else {
					// For URL images, we'd need to fetch and convert to base64
					// For now, we'll skip this and just add a text description
					parts = append(parts, GeminiPart{
						Text: fmt.Sprintf("[Image: %s]", part.ImageURL.URL),
					})
				}
			}
		}
	}

	return parts, nil
}

// convertTools converts OpenAI tools/functions to Gemini tools
func convertTools(tools []core.Tool, functions []core.Function) ([]GeminiTool, error) {
	var declarations []GeminiFunctionDeclaration

	// Convert tools
	for _, tool := range tools {
		if tool.Type == "function" {
			params, ok := tool.Function.Parameters.(map[string]interface{})
			if !ok {
				params = make(map[string]interface{})
			}
			
			declarations = append(declarations, GeminiFunctionDeclaration{
				Name:        tool.Function.Name,
				Description: tool.Function.Description,
				Parameters:  params,
			})
		}
	}

	// Convert legacy functions
	for _, function := range functions {
		params, ok := function.Parameters.(map[string]interface{})
		if !ok {
			params = make(map[string]interface{})
		}
		
		declarations = append(declarations, GeminiFunctionDeclaration{
			Name:        function.Name,
			Description: function.Description,
			Parameters:  params,
		})
	}

	if len(declarations) == 0 {
		return nil, nil
	}

	return []GeminiTool{
		{
			FunctionDeclarations: declarations,
		},
	}, nil
}

// ConvertGeminiToOpenAI converts a Gemini response to OpenAI format
func ConvertGeminiToOpenAI(response *GeminiResponse, model string) (*core.OpenAIResponse, error) {
	openaiResponse := &core.OpenAIResponse{
		ID:      fmt.Sprintf("chatcmpl-%d", time.Now().Unix()),
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   model,
		Choices: make([]core.Choice, 0, len(response.Candidates)),
	}

	// Convert usage
	if response.UsageMetadata.TotalTokenCount > 0 {
		openaiResponse.Usage = &core.Usage{
			PromptTokens:     response.UsageMetadata.PromptTokenCount,
			CompletionTokens: response.UsageMetadata.CandidatesTokenCount,
			TotalTokens:      response.UsageMetadata.TotalTokenCount,
		}
	}

	// Convert candidates to choices
	for i, candidate := range response.Candidates {
		choice := core.Choice{
			Index: i,
		}

		// Convert finish reason
		finishReason := convertFinishReason(candidate.FinishReason)
		choice.FinishReason = &finishReason

		// Convert content
		message, err := convertGeminiContentToMessage(candidate.Content)
		if err != nil {
			return nil, fmt.Errorf("failed to convert candidate content: %w", err)
		}
		choice.Message = message

		openaiResponse.Choices = append(openaiResponse.Choices, choice)
	}

	return openaiResponse, nil
}

// convertGeminiContentToMessage converts Gemini content to OpenAI message
func convertGeminiContentToMessage(content GeminiContent) (*core.Message, error) {
	message := &core.Message{
		Role: "assistant",
	}

	var textParts []string
	var toolCalls []core.ToolCall

	for _, part := range content.Parts {
		if part.Text != "" {
			textParts = append(textParts, part.Text)
		}
		
		if part.FunctionCall != nil {
			args, err := json.Marshal(part.FunctionCall.Args)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal function arguments: %w", err)
			}
			
			toolCalls = append(toolCalls, core.ToolCall{
				ID:   fmt.Sprintf("call_%d", time.Now().UnixNano()),
				Type: "function",
				Function: core.FunctionCall{
					Name:      part.FunctionCall.Name,
					Arguments: string(args),
				},
			})
		}
	}

	// Set content
	if len(textParts) > 0 {
		contentText := strings.Join(textParts, "")
		contentBytes, err := json.Marshal(contentText)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal content: %w", err)
		}
		message.Content = contentBytes
	}

	// Set tool calls
	if len(toolCalls) > 0 {
		toolCallsBytes, err := json.Marshal(toolCalls)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal tool calls: %w", err)
		}
		message.ToolCalls = toolCallsBytes
	}

	return message, nil
}

// convertFinishReason converts Gemini finish reason to OpenAI format
func convertFinishReason(reason string) string {
	switch reason {
	case FinishReasonStop:
		return "stop"
	case FinishReasonMaxTokens:
		return "length"
	case FinishReasonSafety:
		return "content_filter"
	default:
		return "stop"
	}
}
