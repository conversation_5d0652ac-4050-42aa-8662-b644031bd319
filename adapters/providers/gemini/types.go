package gemini

// GeminiRequest represents a Gemini API request
type GeminiRequest struct {
	Contents           []GeminiContent        `json:"contents"`
	SafetySettings     []GeminiSafetySetting  `json:"safetySettings,omitempty"`
	GenerationConfig   GeminiGenerationConfig `json:"generationConfig,omitempty"`
	Tools              []GeminiTool           `json:"tools,omitempty"`
	SystemInstruction  *GeminiContent         `json:"systemInstruction,omitempty"`
}

// GeminiContent represents content in a Gemini request
type GeminiContent struct {
	Role  string       `json:"role,omitempty"`
	Parts []GeminiPart `json:"parts"`
}

// GeminiPart represents a part of content
type GeminiPart struct {
	Text         string            `json:"text,omitempty"`
	InlineData   *GeminiInlineData `json:"inlineData,omitempty"`
	FunctionCall *GeminiFunctionCall `json:"functionCall,omitempty"`
	FunctionResponse *GeminiFunctionResponse `json:"functionResponse,omitempty"`
}

// GeminiInlineData represents inline data (e.g., images)
type GeminiInlineData struct {
	MimeType string `json:"mimeType"`
	Data     string `json:"data"`
}

// GeminiFunctionCall represents a function call
type GeminiFunctionCall struct {
	Name string                 `json:"name"`
	Args map[string]interface{} `json:"args"`
}

// GeminiFunctionResponse represents a function response
type GeminiFunctionResponse struct {
	Name     string                 `json:"name"`
	Response map[string]interface{} `json:"response"`
}

// GeminiSafetySetting represents safety settings
type GeminiSafetySetting struct {
	Category  string `json:"category"`
	Threshold string `json:"threshold"`
}

// GeminiGenerationConfig represents generation configuration
type GeminiGenerationConfig struct {
	Temperature     *float64 `json:"temperature,omitempty"`
	TopP            *float64 `json:"topP,omitempty"`
	TopK            *int     `json:"topK,omitempty"`
	MaxOutputTokens *int     `json:"maxOutputTokens,omitempty"`
	StopSequences   []string `json:"stopSequences,omitempty"`
	CandidateCount  *int     `json:"candidateCount,omitempty"`
}

// GeminiTool represents a tool/function definition
type GeminiTool struct {
	FunctionDeclarations []GeminiFunctionDeclaration `json:"functionDeclarations"`
}

// GeminiFunctionDeclaration represents a function declaration
type GeminiFunctionDeclaration struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// GeminiResponse represents a Gemini API response
type GeminiResponse struct {
	Candidates     []GeminiCandidate    `json:"candidates"`
	PromptFeedback GeminiPromptFeedback `json:"promptFeedback,omitempty"`
	UsageMetadata  GeminiUsageMetadata  `json:"usageMetadata,omitempty"`
}

// GeminiCandidate represents a response candidate
type GeminiCandidate struct {
	Content       GeminiContent        `json:"content"`
	FinishReason  string               `json:"finishReason,omitempty"`
	Index         int                  `json:"index,omitempty"`
	SafetyRatings []GeminiSafetyRating `json:"safetyRatings,omitempty"`
}

// GeminiSafetyRating represents a safety rating
type GeminiSafetyRating struct {
	Category    string `json:"category"`
	Probability string `json:"probability"`
}

// GeminiPromptFeedback represents prompt feedback
type GeminiPromptFeedback struct {
	SafetyRatings []GeminiSafetyRating `json:"safetyRatings,omitempty"`
	BlockReason   string               `json:"blockReason,omitempty"`
}

// GeminiUsageMetadata represents usage metadata
type GeminiUsageMetadata struct {
	PromptTokenCount     int `json:"promptTokenCount"`
	CandidatesTokenCount int `json:"candidatesTokenCount"`
	TotalTokenCount      int `json:"totalTokenCount"`
}

// GeminiError represents a Gemini API error
type GeminiError struct {
	Error struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Status  string `json:"status"`
		Details []struct {
			Type     string `json:"@type"`
			Reason   string `json:"reason,omitempty"`
			Domain   string `json:"domain,omitempty"`
			Metadata map[string]interface{} `json:"metadata,omitempty"`
		} `json:"details,omitempty"`
	} `json:"error"`
}

// Safety categories
const (
	SafetyCategoryHarassment       = "HARM_CATEGORY_HARASSMENT"
	SafetyCategoryHateSpeech       = "HARM_CATEGORY_HATE_SPEECH"
	SafetyCategorySexuallyExplicit = "HARM_CATEGORY_SEXUALLY_EXPLICIT"
	SafetyCategoryDangerousContent = "HARM_CATEGORY_DANGEROUS_CONTENT"
)

// Safety thresholds
const (
	SafetyThresholdBlockNone = "BLOCK_NONE"
	SafetyThresholdBlockLow  = "BLOCK_ONLY_HIGH"
	SafetyThresholdBlockMed  = "BLOCK_MEDIUM_AND_ABOVE"
	SafetyThresholdBlockHigh = "BLOCK_LOW_AND_ABOVE"
)

// Finish reasons
const (
	FinishReasonStop         = "STOP"
	FinishReasonMaxTokens    = "MAX_TOKENS"
	FinishReasonSafety       = "SAFETY"
	FinishReasonRecitation   = "RECITATION"
	FinishReasonOther        = "OTHER"
)

// Default safety settings (most permissive)
func DefaultSafetySettings() []GeminiSafetySetting {
	return []GeminiSafetySetting{
		{Category: SafetyCategoryHarassment, Threshold: SafetyThresholdBlockNone},
		{Category: SafetyCategoryHateSpeech, Threshold: SafetyThresholdBlockNone},
		{Category: SafetyCategorySexuallyExplicit, Threshold: SafetyThresholdBlockNone},
		{Category: SafetyCategoryDangerousContent, Threshold: SafetyThresholdBlockNone},
	}
}
