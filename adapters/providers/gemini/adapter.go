package gemini

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"../../core"
)

// Adapter implements the Gemini adapter
type Adapter struct {
	config *core.AdapterConfig
}

// NewAdapter creates a new Gemini adapter
func NewAdapter(config *core.AdapterConfig) *Adapter {
	if config.BaseURL == "" {
		config.BaseURL = "https://generativelanguage.googleapis.com/v1beta"
	}
	
	return &Adapter{
		config: config,
	}
}

// GetProviderName returns the provider name
func (a *Adapter) GetProviderName() string {
	return "gemini"
}

// GetSupportedModels returns the list of supported models
func (a *Adapter) GetSupportedModels() []string {
	return []string{
		"gemini-1.5-pro",
		"gemini-1.5-flash",
		"gemini-1.0-pro",
		"gemini-pro",
		"gemini-pro-vision",
		"text-embedding-004",
		"embedding-001",
	}
}

// ConvertRequest converts an OpenAI request to Gemini format
func (a *Adapter) ConvertRequest(ctx context.Context, request *core.OpenAIRequest) (interface{}, error) {
	if request == nil {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, "request cannot be nil", http.StatusBadRequest, a.GetProviderName())
	}
	
	// Validate the model
	if !a.isModelSupported(request.Model) {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, fmt.Sprintf("model %s is not supported", request.Model), http.StatusBadRequest, a.GetProviderName())
	}
	
	// Convert OpenAI request to Gemini format
	geminiRequest, err := ConvertOpenAIToGemini(request)
	if err != nil {
		return nil, core.WrapError(err, core.ErrorTypeInvalidRequest, "failed to convert request to Gemini format", http.StatusBadRequest, a.GetProviderName())
	}
	
	return geminiRequest, nil
}

// BuildRequestURL builds the request URL for the given request
func (a *Adapter) BuildRequestURL(baseURL string, request *core.OpenAIRequest) (string, error) {
	if baseURL == "" {
		baseURL = a.config.BaseURL
	}
	
	// Determine the endpoint based on the model and request type
	var endpoint string
	
	if strings.Contains(request.Model, "embedding") {
		endpoint = fmt.Sprintf("/models/%s:embedContent", request.Model)
	} else {
		// For chat completions
		if request.Stream {
			endpoint = fmt.Sprintf("/models/%s:streamGenerateContent", request.Model)
		} else {
			endpoint = fmt.Sprintf("/models/%s:generateContent", request.Model)
		}
	}
	
	return core.BuildURL(baseURL, endpoint), nil
}

// SetupHeaders sets up the request headers
func (a *Adapter) SetupHeaders(headers map[string]string, apiKey string) error {
	if apiKey == "" {
		apiKey = a.config.APIKey
	}
	
	if apiKey == "" {
		return core.NewAdapterError(core.ErrorTypeAuthentication, "API key is required", http.StatusUnauthorized, a.GetProviderName())
	}
	
	if headers == nil {
		headers = make(map[string]string)
	}
	
	headers["Content-Type"] = "application/json"
	headers["x-goog-api-key"] = apiKey
	
	return nil
}

// ExecuteRequest executes the HTTP request
func (a *Adapter) ExecuteRequest(ctx context.Context, httpClient *http.Client, url string, headers map[string]string, body []byte) (*http.Response, error) {
	if httpClient == nil {
		httpClient = a.config.HTTPClient
	}
	
	if httpClient == nil {
		httpClient = &http.Client{}
	}
	
	client := core.NewHTTPClientWithClient(httpClient)
	return client.DoRequest(ctx, "POST", url, headers, body)
}

// ConvertResponse converts the Gemini response to OpenAI format
func (a *Adapter) ConvertResponse(ctx context.Context, response *http.Response, isStream bool) (*core.OpenAIResponse, error) {
	if !core.IsSuccessStatusCode(response.StatusCode) {
		return nil, a.HandleError(response)
	}
	
	if isStream {
		// For streaming responses, we need to handle them differently
		// This is a simplified implementation - in practice, you'd want to handle SSE properly
		return nil, core.NewAdapterError(core.ErrorTypeServerError, "streaming not implemented in this example", http.StatusNotImplemented, a.GetProviderName())
	}
	
	var geminiResponse GeminiResponse
	if err := core.ParseJSONResponse(response, &geminiResponse); err != nil {
		return nil, core.WrapError(err, core.ErrorTypeServerError, "failed to parse Gemini response", http.StatusInternalServerError, a.GetProviderName())
	}
	
	// Convert Gemini response to OpenAI format
	openaiResponse, err := ConvertGeminiToOpenAI(&geminiResponse, "gemini-pro") // You might want to pass the actual model
	if err != nil {
		return nil, core.WrapError(err, core.ErrorTypeServerError, "failed to convert Gemini response to OpenAI format", http.StatusInternalServerError, a.GetProviderName())
	}
	
	return openaiResponse, nil
}

// HandleError handles Gemini-specific errors
func (a *Adapter) HandleError(response *http.Response) error {
	body, err := core.ReadResponseBody(response)
	if err != nil {
		return core.WrapError(err, core.ErrorTypeServerError, "failed to read error response", response.StatusCode, a.GetProviderName())
	}
	
	var geminiError GeminiError
	if err := json.Unmarshal(body, &geminiError); err != nil {
		// If we can't parse the error, return a generic error
		return core.NewAdapterError(
			core.MapHTTPStatusToErrorType(response.StatusCode),
			string(body),
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	if geminiError.Error.Message != "" {
		errorType := core.MapHTTPStatusToErrorType(geminiError.Error.Code)
		if geminiError.Error.Code == 0 {
			errorType = core.MapHTTPStatusToErrorType(response.StatusCode)
		}
		
		return core.NewAdapterError(
			errorType,
			geminiError.Error.Message,
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	return core.NewAdapterError(
		core.MapHTTPStatusToErrorType(response.StatusCode),
		"unknown Gemini error",
		response.StatusCode,
		a.GetProviderName(),
	)
}

// isModelSupported checks if the model is supported by this adapter
func (a *Adapter) isModelSupported(model string) bool {
	supportedModels := a.GetSupportedModels()
	for _, supportedModel := range supportedModels {
		if model == supportedModel {
			return true
		}
		// Also check for model prefixes
		if strings.HasPrefix(model, supportedModel) {
			return true
		}
	}
	return false
}
