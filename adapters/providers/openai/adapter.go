package openai

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"../../core"
)

// Adapter implements the OpenAI adapter
type Adapter struct {
	config *core.AdapterConfig
}

// NewAdapter creates a new OpenAI adapter
func NewAdapter(config *core.AdapterConfig) *Adapter {
	if config.BaseURL == "" {
		config.BaseURL = "https://api.openai.com/v1"
	}
	
	return &Adapter{
		config: config,
	}
}

// GetProviderName returns the provider name
func (a *Adapter) GetProviderName() string {
	return "openai"
}

// GetSupportedModels returns the list of supported models
func (a *Adapter) GetSupportedModels() []string {
	return []string{
		"gpt-4o",
		"gpt-4o-mini",
		"gpt-4-turbo",
		"gpt-4",
		"gpt-3.5-turbo",
		"gpt-3.5-turbo-16k",
		"text-embedding-3-large",
		"text-embedding-3-small",
		"text-embedding-ada-002",
		"whisper-1",
		"tts-1",
		"tts-1-hd",
		"dall-e-3",
		"dall-e-2",
	}
}

// ConvertRequest converts an OpenAI request to the provider's format
// For OpenAI, this is a pass-through since it's the canonical format
func (a *Adapter) ConvertRequest(ctx context.Context, request *core.OpenAIRequest) (interface{}, error) {
	if request == nil {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, "request cannot be nil", http.StatusBadRequest, a.GetProviderName())
	}
	
	// Validate the model
	if !a.isModelSupported(request.Model) {
		return nil, core.NewAdapterError(core.ErrorTypeInvalidRequest, fmt.Sprintf("model %s is not supported", request.Model), http.StatusBadRequest, a.GetProviderName())
	}
	
	// For OpenAI, we return the request as-is since it's already in the correct format
	return request, nil
}

// BuildRequestURL builds the request URL for the given request
func (a *Adapter) BuildRequestURL(baseURL string, request *core.OpenAIRequest) (string, error) {
	if baseURL == "" {
		baseURL = a.config.BaseURL
	}
	
	// Determine the endpoint based on the request type
	var endpoint string
	
	if len(request.Messages) > 0 {
		endpoint = "/chat/completions"
	} else if request.Prompt != nil {
		endpoint = "/completions"
	} else {
		return "", core.NewAdapterError(core.ErrorTypeInvalidRequest, "invalid request: neither messages nor prompt provided", http.StatusBadRequest, a.GetProviderName())
	}
	
	return core.BuildURL(baseURL, endpoint), nil
}

// SetupHeaders sets up the request headers
func (a *Adapter) SetupHeaders(headers map[string]string, apiKey string) error {
	if apiKey == "" {
		apiKey = a.config.APIKey
	}
	
	if apiKey == "" {
		return core.NewAdapterError(core.ErrorTypeAuthentication, "API key is required", http.StatusUnauthorized, a.GetProviderName())
	}
	
	core.SetupCommonHeaders(headers, apiKey)
	
	return nil
}

// ExecuteRequest executes the HTTP request
func (a *Adapter) ExecuteRequest(ctx context.Context, httpClient *http.Client, url string, headers map[string]string, body []byte) (*http.Response, error) {
	if httpClient == nil {
		httpClient = a.config.HTTPClient
	}
	
	if httpClient == nil {
		httpClient = &http.Client{}
	}
	
	client := core.NewHTTPClientWithClient(httpClient)
	return client.DoRequest(ctx, "POST", url, headers, body)
}

// ConvertResponse converts the provider's response to OpenAI format
func (a *Adapter) ConvertResponse(ctx context.Context, response *http.Response, isStream bool) (*core.OpenAIResponse, error) {
	if !core.IsSuccessStatusCode(response.StatusCode) {
		return nil, a.HandleError(response)
	}
	
	if isStream {
		// For streaming responses, we need to handle them differently
		// This is a simplified implementation - in practice, you'd want to handle SSE properly
		return nil, core.NewAdapterError(core.ErrorTypeServerError, "streaming not implemented in this example", http.StatusNotImplemented, a.GetProviderName())
	}
	
	var openaiResponse core.OpenAIResponse
	if err := core.ParseJSONResponse(response, &openaiResponse); err != nil {
		return nil, core.WrapError(err, core.ErrorTypeServerError, "failed to parse response", http.StatusInternalServerError, a.GetProviderName())
	}
	
	return &openaiResponse, nil
}

// HandleError handles provider-specific errors
func (a *Adapter) HandleError(response *http.Response) error {
	body, err := core.ReadResponseBody(response)
	if err != nil {
		return core.WrapError(err, core.ErrorTypeServerError, "failed to read error response", response.StatusCode, a.GetProviderName())
	}
	
	var errorResponse struct {
		Error *core.OpenAIError `json:"error"`
	}
	
	if err := json.Unmarshal(body, &errorResponse); err != nil {
		// If we can't parse the error, return a generic error
		return core.NewAdapterError(
			core.MapHTTPStatusToErrorType(response.StatusCode),
			string(body),
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	if errorResponse.Error != nil {
		return core.NewAdapterError(
			core.ErrorType(errorResponse.Error.Type),
			errorResponse.Error.Message,
			response.StatusCode,
			a.GetProviderName(),
		)
	}
	
	return core.NewAdapterError(
		core.MapHTTPStatusToErrorType(response.StatusCode),
		"unknown error",
		response.StatusCode,
		a.GetProviderName(),
	)
}

// isModelSupported checks if the model is supported by this adapter
func (a *Adapter) isModelSupported(model string) bool {
	supportedModels := a.GetSupportedModels()
	for _, supportedModel := range supportedModels {
		if model == supportedModel {
			return true
		}
		// Also check for model prefixes (e.g., gpt-4 matches gpt-4-0613)
		if strings.HasPrefix(model, supportedModel) {
			return true
		}
	}
	return false
}
