package perplexity

import (
	"../../core"
)

// PerplexityRequest represents a Perplexity API request
type PerplexityRequest struct {
	Model                    string           `json:"model"`
	Messages                 []core.Message   `json:"messages"`
	MaxTokens                *int             `json:"max_tokens,omitempty"`
	Temperature              *float64         `json:"temperature,omitempty"`
	TopP                     *float64         `json:"top_p,omitempty"`
	Stream                   bool             `json:"stream,omitempty"`
	FrequencyPenalty         *float64         `json:"frequency_penalty,omitempty"`
	PresencePenalty          *float64         `json:"presence_penalty,omitempty"`
	Stop                     []string         `json:"stop,omitempty"`
	
	// Perplexity-specific parameters for online models
	ReturnCitations          bool             `json:"return_citations,omitempty"`
	ReturnImages             bool             `json:"return_images,omitempty"`
	ReturnRelatedQuestions   bool             `json:"return_related_questions,omitempty"`
	SearchDomainFilter       []string         `json:"search_domain_filter,omitempty"`
	SearchRecencyFilter      string           `json:"search_recency_filter,omitempty"`
}

// PerplexityResponse represents a Perplexity API response
type PerplexityResponse struct {
	ID      string                 `json:"id"`
	Object  string                 `json:"object"`
	Created int64                  `json:"created"`
	Model   string                 `json:"model"`
	Choices []PerplexityChoice     `json:"choices"`
	Usage   *core.Usage            `json:"usage,omitempty"`
}

// PerplexityChoice represents a choice in the response
type PerplexityChoice struct {
	Index        int           `json:"index"`
	Message      *core.Message `json:"message,omitempty"`
	Delta        *core.Message `json:"delta,omitempty"`
	FinishReason *string       `json:"finish_reason,omitempty"`
}

// PerplexityStreamResponse represents a streaming response chunk
type PerplexityStreamResponse struct {
	ID      string                 `json:"id"`
	Object  string                 `json:"object"`
	Created int64                  `json:"created"`
	Model   string                 `json:"model"`
	Choices []PerplexityChoice     `json:"choices"`
}

// PerplexityError represents a Perplexity API error
type PerplexityError struct {
	Error struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    string `json:"code"`
	} `json:"error"`
}

// Search recency filter options
const (
	RecencyFilterMonth = "month"
	RecencyFilterWeek  = "week"
	RecencyFilterDay   = "day"
	RecencyFilterHour  = "hour"
)

// Model categories
const (
	ModelCategoryOnline = "online"
	ModelCategoryChat   = "chat"
)

// IsOnlineModel checks if the model is an online search model
func IsOnlineModel(model string) bool {
	return strings.Contains(model, "online") || strings.Contains(model, "sonar")
}

// IsChatModel checks if the model is a chat model
func IsChatModel(model string) bool {
	return strings.Contains(model, "chat") || strings.Contains(model, "instruct")
}
