package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"../core"
	"../providers"
	"../utils"
)

func main() {
	// Example 1: Basic usage with OpenAI
	fmt.Println("=== Example 1: Basic OpenAI Usage ===")
	if err := basicOpenAIExample(); err != nil {
		log.Printf("OpenAI example failed: %v", err)
	}

	// Example 2: Using Gemini adapter
	fmt.Println("\n=== Example 2: Gemini Usage ===")
	if err := geminiExample(); err != nil {
		log.Printf("Gemini example failed: %v", err)
	}

	// Example 3: Using Claude adapter
	fmt.Println("\n=== Example 3: Claude Usage ===")
	if err := claudeExample(); err != nil {
		log.Printf("Claude example failed: %v", err)
	}

	// Example 4: Using Perplexity adapter
	fmt.Println("\n=== Example 4: Perplexity Usage ===")
	if err := perplexityExample(); err != nil {
		log.Printf("Perplexity example failed: %v", err)
	}

	// Example 5: Using Mistral adapter
	fmt.Println("\n=== Example 5: Mistral Usage ===")
	if err := mistralExample(); err != nil {
		log.Printf("Mistral example failed: %v", err)
	}

	// Example 6: Using Deepseek adapter
	fmt.Println("\n=== Example 6: Deepseek Usage ===")
	if err := deepseekExample(); err != nil {
		log.Printf("Deepseek example failed: %v", err)
	}

	// Example 7: Using AWS Bedrock adapter
	fmt.Println("\n=== Example 7: AWS Bedrock Usage ===")
	if err := awsExample(); err != nil {
		log.Printf("AWS example failed: %v", err)
	}

	// Example 8: Using Azure OpenAI adapter
	fmt.Println("\n=== Example 8: Azure OpenAI Usage ===")
	if err := azureExample(); err != nil {
		log.Printf("Azure example failed: %v", err)
	}

	// Example 9: Using the adapter manager for automatic routing
	fmt.Println("\n=== Example 9: Adapter Manager Usage ===")
	if err := adapterManagerExample(); err != nil {
		log.Printf("Adapter manager example failed: %v", err)
	}

	// Example 10: Custom adapter configuration
	fmt.Println("\n=== Example 10: Custom Configuration ===")
	if err := customConfigExample(); err != nil {
		log.Printf("Custom config example failed: %v", err)
	}
}

func basicOpenAIExample() error {
	// Create OpenAI adapter with configuration
	config := &core.AdapterConfig{
		APIKey:  "your-openai-api-key", // Replace with actual API key
		BaseURL: "https://api.openai.com/v1",
		HTTPClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}

	factory := providers.NewAdapterFactory()
	adapter, err := factory.CreateAdapter("openai", config)
	if err != nil {
		return fmt.Errorf("failed to create OpenAI adapter: %w", err)
	}

	// Create a request
	request := &core.OpenAIRequest{
		Model: "gpt-3.5-turbo",
		Messages: []core.Message{
			{
				Role:    "user",
				Content: json.RawMessage(`"Hello, how are you?"`),
			},
		},
		MaxTokens:   intPtr(100),
		Temperature: float64Ptr(0.7),
	}

	// Validate the request
	validator := utils.NewValidator()
	if err := validator.ValidateRequest(request); err != nil {
		return fmt.Errorf("request validation failed: %w", err)
	}

	// Convert the request
	ctx := context.Background()
	convertedRequest, err := adapter.ConvertRequest(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to convert request: %w", err)
	}

	fmt.Printf("Converted request: %+v\n", convertedRequest)

	// Build URL
	url, err := adapter.BuildRequestURL("", request)
	if err != nil {
		return fmt.Errorf("failed to build URL: %w", err)
	}

	fmt.Printf("Request URL: %s\n", url)

	// Setup headers
	headers := make(map[string]string)
	if err := adapter.SetupHeaders(headers, ""); err != nil {
		return fmt.Errorf("failed to setup headers: %w", err)
	}

	fmt.Printf("Headers: %+v\n", headers)

	return nil
}

func geminiExample() error {
	config := &core.AdapterConfig{
		APIKey:  "your-gemini-api-key", // Replace with actual API key
		BaseURL: "https://generativelanguage.googleapis.com/v1beta",
	}

	factory := providers.NewAdapterFactory()
	adapter, err := factory.CreateAdapter("gemini", config)
	if err != nil {
		return fmt.Errorf("failed to create Gemini adapter: %w", err)
	}

	request := &core.OpenAIRequest{
		Model: "gemini-1.5-pro",
		Messages: []core.Message{
			{
				Role:    "user",
				Content: json.RawMessage(`"Explain quantum computing in simple terms"`),
			},
		},
		MaxTokens:   intPtr(150),
		Temperature: float64Ptr(0.5),
	}

	ctx := context.Background()
	convertedRequest, err := adapter.ConvertRequest(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to convert request: %w", err)
	}

	fmt.Printf("Gemini converted request: %+v\n", convertedRequest)

	url, err := adapter.BuildRequestURL("", request)
	if err != nil {
		return fmt.Errorf("failed to build URL: %w", err)
	}

	fmt.Printf("Gemini URL: %s\n", url)

	return nil
}

func claudeExample() error {
	config := &core.AdapterConfig{
		APIKey:  "your-claude-api-key", // Replace with actual API key
		BaseURL: "https://api.anthropic.com/v1",
	}

	factory := providers.NewAdapterFactory()
	adapter, err := factory.CreateAdapter("claude", config)
	if err != nil {
		return fmt.Errorf("failed to create Claude adapter: %w", err)
	}

	request := &core.OpenAIRequest{
		Model: "claude-3-sonnet-20240229",
		Messages: []core.Message{
			{
				Role:    "system",
				Content: json.RawMessage(`"You are a helpful assistant."`),
			},
			{
				Role:    "user",
				Content: json.RawMessage(`"What is the capital of France?"`),
			},
		},
		MaxTokens:   intPtr(100),
		Temperature: float64Ptr(0.3),
	}

	ctx := context.Background()
	convertedRequest, err := adapter.ConvertRequest(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to convert request: %w", err)
	}

	fmt.Printf("Claude converted request: %+v\n", convertedRequest)

	return nil
}

func perplexityExample() error {
	config := &core.AdapterConfig{
		APIKey:  "your-perplexity-api-key", // Replace with actual API key
		BaseURL: "https://api.perplexity.ai",
	}

	factory := providers.NewAdapterFactory()
	adapter, err := factory.CreateAdapter("perplexity", config)
	if err != nil {
		return fmt.Errorf("failed to create Perplexity adapter: %w", err)
	}

	request := &core.OpenAIRequest{
		Model: "llama-3.1-sonar-large-128k-online",
		Messages: []core.Message{
			{
				Role:    "user",
				Content: json.RawMessage(`"What are the latest developments in AI?"`),
			},
		},
		MaxTokens:   intPtr(200),
		Temperature: float64Ptr(0.7),
	}

	ctx := context.Background()
	convertedRequest, err := adapter.ConvertRequest(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to convert request: %w", err)
	}

	fmt.Printf("Perplexity converted request: %+v\n", convertedRequest)
	return nil
}

func mistralExample() error {
	config := &core.AdapterConfig{
		APIKey:  "your-mistral-api-key", // Replace with actual API key
		BaseURL: "https://api.mistral.ai/v1",
	}

	factory := providers.NewAdapterFactory()
	adapter, err := factory.CreateAdapter("mistral", config)
	if err != nil {
		return fmt.Errorf("failed to create Mistral adapter: %w", err)
	}

	request := &core.OpenAIRequest{
		Model: "mistral-large-latest",
		Messages: []core.Message{
			{
				Role:    "user",
				Content: json.RawMessage(`"Explain the concept of machine learning"`),
			},
		},
		MaxTokens:   intPtr(150),
		Temperature: float64Ptr(0.5),
	}

	ctx := context.Background()
	convertedRequest, err := adapter.ConvertRequest(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to convert request: %w", err)
	}

	fmt.Printf("Mistral converted request: %+v\n", convertedRequest)
	return nil
}

func deepseekExample() error {
	config := &core.AdapterConfig{
		APIKey:  "your-deepseek-api-key", // Replace with actual API key
		BaseURL: "https://api.deepseek.com",
	}

	factory := providers.NewAdapterFactory()
	adapter, err := factory.CreateAdapter("deepseek", config)
	if err != nil {
		return fmt.Errorf("failed to create Deepseek adapter: %w", err)
	}

	request := &core.OpenAIRequest{
		Model: "deepseek-chat",
		Messages: []core.Message{
			{
				Role:    "user",
				Content: json.RawMessage(`"Write a Python function to calculate fibonacci numbers"`),
			},
		},
		MaxTokens:   intPtr(200),
		Temperature: float64Ptr(0.3),
	}

	ctx := context.Background()
	convertedRequest, err := adapter.ConvertRequest(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to convert request: %w", err)
	}

	fmt.Printf("Deepseek converted request: %+v\n", convertedRequest)
	return nil
}

func awsExample() error {
	config := &core.AdapterConfig{
		APIKey:  "your-aws-credentials", // Replace with actual AWS credentials
		BaseURL: "https://bedrock-runtime.us-east-1.amazonaws.com",
	}

	factory := providers.NewAdapterFactory()
	adapter, err := factory.CreateAdapter("aws", config)
	if err != nil {
		return fmt.Errorf("failed to create AWS adapter: %w", err)
	}

	request := &core.OpenAIRequest{
		Model: "claude-3-sonnet-20240229",
		Messages: []core.Message{
			{
				Role:    "user",
				Content: json.RawMessage(`"What are the benefits of cloud computing?"`),
			},
		},
		MaxTokens:   intPtr(150),
		Temperature: float64Ptr(0.7),
	}

	ctx := context.Background()
	convertedRequest, err := adapter.ConvertRequest(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to convert request: %w", err)
	}

	fmt.Printf("AWS Bedrock converted request: %+v\n", convertedRequest)
	return nil
}

func azureExample() error {
	config := &core.AdapterConfig{
		APIKey:  "your-azure-api-key", // Replace with actual API key
		BaseURL: "https://your-resource.openai.azure.com",
		ExtraParams: map[string]interface{}{
			"deployment_name": "gpt-4",
			"api_version":     "2024-02-15-preview",
		},
	}

	factory := providers.NewAdapterFactory()
	adapter, err := factory.CreateAdapter("azure", config)
	if err != nil {
		return fmt.Errorf("failed to create Azure adapter: %w", err)
	}

	request := &core.OpenAIRequest{
		Model: "gpt-4",
		Messages: []core.Message{
			{
				Role:    "user",
				Content: json.RawMessage(`"Explain Azure OpenAI Service"`),
			},
		},
		MaxTokens:   intPtr(150),
		Temperature: float64Ptr(0.6),
	}

	ctx := context.Background()
	convertedRequest, err := adapter.ConvertRequest(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to convert request: %w", err)
	}

	fmt.Printf("Azure OpenAI converted request: %+v\n", convertedRequest)
	return nil
}

func adapterManagerExample() error {
	// Create adapter manager with default registry
	manager := providers.NewAdapterManager()

	// Configure adapters
	configs := map[string]*core.AdapterConfig{
		"openai": {
			APIKey:  "your-openai-api-key",
			BaseURL: "https://api.openai.com/v1",
		},
		"gemini": {
			APIKey:  "your-gemini-api-key",
			BaseURL: "https://generativelanguage.googleapis.com/v1beta",
		},
		"claude": {
			APIKey:  "your-claude-api-key",
			BaseURL: "https://api.anthropic.com/v1",
		},
		"perplexity": {
			APIKey:  "your-perplexity-api-key",
			BaseURL: "https://api.perplexity.ai",
		},
		"mistral": {
			APIKey:  "your-mistral-api-key",
			BaseURL: "https://api.mistral.ai/v1",
		},
		"deepseek": {
			APIKey:  "your-deepseek-api-key",
			BaseURL: "https://api.deepseek.com",
		},
		"moonshot": {
			APIKey:  "your-moonshot-api-key",
			BaseURL: "https://api.moonshot.cn/v1",
		},
		"ollama": {
			APIKey:  "", // Ollama typically doesn't require API key for local
			BaseURL: "http://localhost:11434",
		},
		"aws": {
			APIKey:  "your-aws-credentials",
			BaseURL: "https://bedrock-runtime.us-east-1.amazonaws.com",
		},
		"azure": {
			APIKey:  "your-azure-api-key",
			BaseURL: "https://your-resource.openai.azure.com",
			ExtraParams: map[string]interface{}{
				"api_version": "2024-02-15-preview",
			},
		},
		"ali": {
			APIKey:  "your-ali-api-key",
			BaseURL: "https://dashscope.aliyuncs.com/api/v1",
		},
	}

	factory := providers.NewAdapterFactory()
	for provider, config := range configs {
		adapter, err := factory.CreateAdapter(provider, config)
		if err != nil {
			return fmt.Errorf("failed to create %s adapter: %w", provider, err)
		}
		if err := manager.RegisterAdapter(provider, adapter); err != nil {
			return fmt.Errorf("failed to register %s adapter: %w", provider, err)
		}
	}

	// Test different models
	testRequests := []*core.OpenAIRequest{
		{
			Model: "gpt-4",
			Messages: []core.Message{
				{Role: "user", Content: json.RawMessage(`"Hello GPT-4"`)},
			},
		},
		{
			Model: "gemini-1.5-pro",
			Messages: []core.Message{
				{Role: "user", Content: json.RawMessage(`"Hello Gemini"`)},
			},
		},
		{
			Model: "claude-3-sonnet-20240229",
			Messages: []core.Message{
				{Role: "user", Content: json.RawMessage(`"Hello Claude"`)},
			},
		},
		{
			Model: "llama-3.1-sonar-large-128k-online",
			Messages: []core.Message{
				{Role: "user", Content: json.RawMessage(`"Hello Perplexity"`)},
			},
		},
		{
			Model: "mistral-large-latest",
			Messages: []core.Message{
				{Role: "user", Content: json.RawMessage(`"Hello Mistral"`)},
			},
		},
		{
			Model: "deepseek-chat",
			Messages: []core.Message{
				{Role: "user", Content: json.RawMessage(`"Hello Deepseek"`)},
			},
		},
		{
			Model: "moonshot-v1-8k",
			Messages: []core.Message{
				{Role: "user", Content: json.RawMessage(`"Hello Moonshot"`)},
			},
		},
		{
			Model: "llama3.2",
			Messages: []core.Message{
				{Role: "user", Content: json.RawMessage(`"Hello Ollama"`)},
			},
		},
		{
			Model: "qwen-turbo",
			Messages: []core.Message{
				{Role: "user", Content: json.RawMessage(`"Hello Alibaba"`)},
			},
		},
	}

	for _, request := range testRequests {
		adapter, err := manager.RouteRequest(request)
		if err != nil {
			log.Printf("Failed to route request for model %s: %v", request.Model, err)
			continue
		}

		fmt.Printf("Model %s routed to provider: %s\n", request.Model, adapter.GetProviderName())

		ctx := context.Background()
		convertedRequest, err := adapter.ConvertRequest(ctx, request)
		if err != nil {
			log.Printf("Failed to convert request for %s: %v", request.Model, err)
			continue
		}

		fmt.Printf("Successfully converted request for %s\n", request.Model)
		_ = convertedRequest // Use the converted request as needed
	}

	return nil
}

func customConfigExample() error {
	// Create a custom HTTP client with specific settings
	httpClient := &http.Client{
		Timeout: 60 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        10,
			IdleConnTimeout:     30 * time.Second,
			DisableCompression:  true,
		},
	}

	// Create custom configurations for each provider
	openaiConfig := &core.AdapterConfig{
		APIKey:     "your-openai-api-key",
		BaseURL:    "https://api.openai.com/v1",
		HTTPClient: httpClient,
		ExtraParams: map[string]interface{}{
			"organization": "your-org-id",
		},
	}

	geminiConfig := &core.AdapterConfig{
		APIKey:     "your-gemini-api-key",
		BaseURL:    "https://generativelanguage.googleapis.com/v1beta",
		HTTPClient: httpClient,
		ExtraParams: map[string]interface{}{
			"safety_settings": "permissive",
		},
	}

	claudeConfig := &core.AdapterConfig{
		APIKey:     "your-claude-api-key",
		BaseURL:    "https://api.anthropic.com/v1",
		HTTPClient: httpClient,
		ExtraParams: map[string]interface{}{
			"anthropic_version": "2023-06-01",
		},
	}

	// Create adapters with custom configurations
	factory := providers.NewAdapterFactory()
	
	openaiAdapter, err := factory.CreateAdapter("openai", openaiConfig)
	if err != nil {
		return fmt.Errorf("failed to create OpenAI adapter: %w", err)
	}

	geminiAdapter, err := factory.CreateAdapter("gemini", geminiConfig)
	if err != nil {
		return fmt.Errorf("failed to create Gemini adapter: %w", err)
	}

	claudeAdapter, err := factory.CreateAdapter("claude", claudeConfig)
	if err != nil {
		return fmt.Errorf("failed to create Claude adapter: %w", err)
	}

	fmt.Printf("Created custom adapters:\n")
	fmt.Printf("- OpenAI: %s\n", openaiAdapter.GetProviderName())
	fmt.Printf("- Gemini: %s\n", geminiAdapter.GetProviderName())
	fmt.Printf("- Claude: %s\n", claudeAdapter.GetProviderName())

	return nil
}

// Helper functions
func intPtr(i int) *int {
	return &i
}

func float64Ptr(f float64) *float64 {
	return &f
}
