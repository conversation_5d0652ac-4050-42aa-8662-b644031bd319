package utils

import (
	"fmt"
	"strings"

	"../core"
)

// Validator implements request validation
type Validator struct{}

// NewValidator creates a new validator
func NewValidator() *Validator {
	return &Validator{}
}

// ValidateRequest validates an OpenAI request
func (v *Validator) ValidateRequest(request *core.OpenAIRequest) error {
	if request == nil {
		return core.NewAdapterError(core.ErrorTypeInvalidRequest, "request cannot be nil", 400, "")
	}

	// Validate model
	if request.Model == "" {
		return core.NewAdapterError(core.ErrorTypeInvalidRequest, "model is required", 400, "")
	}

	// Validate messages for chat completions
	if len(request.Messages) > 0 {
		if err := v.validateMessages(request.Messages); err != nil {
			return err
		}
	}

	// Validate prompt for completions
	if request.Prompt != nil && len(request.Messages) == 0 {
		if err := v.validatePrompt(request.Prompt); err != nil {
			return err
		}
	}

	// Validate parameters
	if err := v.validateParameters(request); err != nil {
		return err
	}

	return nil
}

// ValidateModel validates if a model is supported by a provider
func (v *Validator) ValidateModel(model string, provider string) error {
	if model == "" {
		return core.NewAdapterError(core.ErrorTypeInvalidRequest, "model cannot be empty", 400, provider)
	}

	// Basic model name validation
	if len(model) > 100 {
		return core.NewAdapterError(core.ErrorTypeInvalidRequest, "model name too long", 400, provider)
	}

	// Check for invalid characters
	if strings.ContainsAny(model, "<>\"'&") {
		return core.NewAdapterError(core.ErrorTypeInvalidRequest, "model name contains invalid characters", 400, provider)
	}

	return nil
}

// validateMessages validates the messages array
func (v *Validator) validateMessages(messages []core.Message) error {
	if len(messages) == 0 {
		return core.NewAdapterError(core.ErrorTypeInvalidRequest, "messages cannot be empty", 400, "")
	}

	for i, message := range messages {
		if err := v.validateMessage(message, i); err != nil {
			return err
		}
	}

	return nil
}

// validateMessage validates a single message
func (v *Validator) validateMessage(message core.Message, index int) error {
	// Validate role
	validRoles := []string{"system", "user", "assistant", "tool"}
	if !contains(validRoles, message.Role) {
		return core.NewAdapterError(
			core.ErrorTypeInvalidRequest,
			fmt.Sprintf("invalid role '%s' at message index %d", message.Role, index),
			400,
			"",
		)
	}

	// Validate content
	if message.Content == nil && message.ToolCalls == nil && message.FunctionCall == nil {
		return core.NewAdapterError(
			core.ErrorTypeInvalidRequest,
			fmt.Sprintf("message at index %d must have content, tool_calls, or function_call", index),
			400,
			"",
		)
	}

	// Validate tool call ID for tool messages
	if message.Role == "tool" && message.ToolCallID == "" {
		return core.NewAdapterError(
			core.ErrorTypeInvalidRequest,
			fmt.Sprintf("tool message at index %d must have tool_call_id", index),
			400,
			"",
		)
	}

	return nil
}

// validatePrompt validates the prompt field
func (v *Validator) validatePrompt(prompt interface{}) error {
	if prompt == nil {
		return core.NewAdapterError(core.ErrorTypeInvalidRequest, "prompt cannot be nil", 400, "")
	}

	switch p := prompt.(type) {
	case string:
		if strings.TrimSpace(p) == "" {
			return core.NewAdapterError(core.ErrorTypeInvalidRequest, "prompt cannot be empty", 400, "")
		}
	case []string:
		if len(p) == 0 {
			return core.NewAdapterError(core.ErrorTypeInvalidRequest, "prompt array cannot be empty", 400, "")
		}
		for i, str := range p {
			if strings.TrimSpace(str) == "" {
				return core.NewAdapterError(
					core.ErrorTypeInvalidRequest,
					fmt.Sprintf("prompt at index %d cannot be empty", i),
					400,
					"",
				)
			}
		}
	default:
		return core.NewAdapterError(core.ErrorTypeInvalidRequest, "prompt must be string or array of strings", 400, "")
	}

	return nil
}

// validateParameters validates request parameters
func (v *Validator) validateParameters(request *core.OpenAIRequest) error {
	// Validate temperature
	if request.Temperature != nil {
		if *request.Temperature < 0 || *request.Temperature > 2 {
			return core.NewAdapterError(
				core.ErrorTypeInvalidRequest,
				"temperature must be between 0 and 2",
				400,
				"",
			)
		}
	}

	// Validate top_p
	if request.TopP != nil {
		if *request.TopP < 0 || *request.TopP > 1 {
			return core.NewAdapterError(
				core.ErrorTypeInvalidRequest,
				"top_p must be between 0 and 1",
				400,
				"",
			)
		}
	}

	// Validate max_tokens
	if request.MaxTokens != nil {
		if *request.MaxTokens < 1 || *request.MaxTokens > 100000 {
			return core.NewAdapterError(
				core.ErrorTypeInvalidRequest,
				"max_tokens must be between 1 and 100000",
				400,
				"",
			)
		}
	}

	// Validate n
	if request.N != nil {
		if *request.N < 1 || *request.N > 10 {
			return core.NewAdapterError(
				core.ErrorTypeInvalidRequest,
				"n must be between 1 and 10",
				400,
				"",
			)
		}
	}

	// Validate frequency_penalty
	if request.FrequencyPenalty != nil {
		if *request.FrequencyPenalty < -2 || *request.FrequencyPenalty > 2 {
			return core.NewAdapterError(
				core.ErrorTypeInvalidRequest,
				"frequency_penalty must be between -2 and 2",
				400,
				"",
			)
		}
	}

	// Validate presence_penalty
	if request.PresencePenalty != nil {
		if *request.PresencePenalty < -2 || *request.PresencePenalty > 2 {
			return core.NewAdapterError(
				core.ErrorTypeInvalidRequest,
				"presence_penalty must be between -2 and 2",
				400,
				"",
			)
		}
	}

	return nil
}

// contains checks if a slice contains a string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// SanitizeInput sanitizes user input to prevent injection attacks
func SanitizeInput(input string) string {
	// Remove potentially dangerous characters
	input = strings.ReplaceAll(input, "<script>", "")
	input = strings.ReplaceAll(input, "</script>", "")
	input = strings.ReplaceAll(input, "javascript:", "")
	input = strings.ReplaceAll(input, "data:", "")
	
	return input
}

// ValidateAPIKey validates an API key format
func ValidateAPIKey(apiKey string, provider string) error {
	if apiKey == "" {
		return core.NewAdapterError(core.ErrorTypeAuthentication, "API key is required", 401, provider)
	}

	// Basic validation based on provider
	switch strings.ToLower(provider) {
	case "openai":
		if !strings.HasPrefix(apiKey, "sk-") {
			return core.NewAdapterError(core.ErrorTypeAuthentication, "invalid OpenAI API key format", 401, provider)
		}
	case "claude":
		if !strings.HasPrefix(apiKey, "sk-ant-") {
			return core.NewAdapterError(core.ErrorTypeAuthentication, "invalid Claude API key format", 401, provider)
		}
	case "gemini":
		// Gemini API keys are typically 39 characters long
		if len(apiKey) < 20 {
			return core.NewAdapterError(core.ErrorTypeAuthentication, "invalid Gemini API key format", 401, provider)
		}
	}

	return nil
}
