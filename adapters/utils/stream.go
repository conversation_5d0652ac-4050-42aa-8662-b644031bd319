package utils

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"../core"
)

// StreamProcessor handles streaming responses
type StreamProcessor struct {
	reader *bufio.Scanner
	resp   *http.Response
}

// NewStreamProcessor creates a new stream processor
func NewStreamProcessor(resp *http.Response) *StreamProcessor {
	return &StreamProcessor{
		reader: bufio.NewScanner(resp.Body),
		resp:   resp,
	}
}

// ProcessStream processes a streaming response and calls the callback for each chunk
func (sp *StreamProcessor) ProcessStream(ctx context.Context, callback func(chunk *core.StreamChunk) error) error {
	defer sp.resp.Body.Close()

	for sp.reader.Scan() {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		line := sp.reader.Text()
		
		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, ":") {
			continue
		}

		// Parse SSE format
		if strings.HasPrefix(line, "data: ") {
			data := strings.TrimPrefix(line, "data: ")
			
			// Check for end of stream
			if data == "[DONE]" {
				chunk := &core.StreamChunk{
					Data:   []byte(data),
					IsLast: true,
				}
				return callback(chunk)
			}

			chunk := &core.StreamChunk{
				Data:   []byte(data),
				IsLast: false,
			}

			if err := callback(chunk); err != nil {
				return err
			}
		}
	}

	if err := sp.reader.Err(); err != nil {
		return fmt.Errorf("error reading stream: %w", err)
	}

	return nil
}

// Close closes the stream processor
func (sp *StreamProcessor) Close() error {
	return sp.resp.Body.Close()
}

// StreamHandler implements the core.StreamHandler interface
type StreamHandler struct{}

// NewStreamHandler creates a new stream handler
func NewStreamHandler() *StreamHandler {
	return &StreamHandler{}
}

// HandleStream handles streaming responses
func (sh *StreamHandler) HandleStream(ctx context.Context, response *http.Response, callback func(chunk *core.StreamChunk) error) error {
	processor := NewStreamProcessor(response)
	return processor.ProcessStream(ctx, callback)
}

// StreamChunkParser parses streaming chunks for different providers
type StreamChunkParser struct{}

// NewStreamChunkParser creates a new stream chunk parser
func NewStreamChunkParser() *StreamChunkParser {
	return &StreamChunkParser{}
}

// ParseOpenAIChunk parses an OpenAI streaming chunk
func (scp *StreamChunkParser) ParseOpenAIChunk(data []byte) (*core.OpenAIResponse, error) {
	var streamResponse struct {
		ID      string `json:"id"`
		Object  string `json:"object"`
		Created int64  `json:"created"`
		Model   string `json:"model"`
		Choices []struct {
			Index int `json:"index"`
			Delta struct {
				Role    string `json:"role,omitempty"`
				Content string `json:"content,omitempty"`
			} `json:"delta"`
			FinishReason *string `json:"finish_reason"`
		} `json:"choices"`
	}

	if err := json.Unmarshal(data, &streamResponse); err != nil {
		return nil, fmt.Errorf("failed to parse OpenAI stream chunk: %w", err)
	}

	// Convert to standard response format
	response := &core.OpenAIResponse{
		ID:      streamResponse.ID,
		Object:  streamResponse.Object,
		Created: streamResponse.Created,
		Model:   streamResponse.Model,
		Choices: make([]core.Choice, len(streamResponse.Choices)),
	}

	for i, choice := range streamResponse.Choices {
		deltaContent, _ := json.Marshal(choice.Delta.Content)
		response.Choices[i] = core.Choice{
			Index: choice.Index,
			Delta: &core.Message{
				Role:    choice.Delta.Role,
				Content: deltaContent,
			},
			FinishReason: choice.FinishReason,
		}
	}

	return response, nil
}

// StreamWriter writes streaming responses in OpenAI format
type StreamWriter struct {
	writer io.Writer
}

// NewStreamWriter creates a new stream writer
func NewStreamWriter(writer io.Writer) *StreamWriter {
	return &StreamWriter{
		writer: writer,
	}
}

// WriteChunk writes a streaming chunk in SSE format
func (sw *StreamWriter) WriteChunk(chunk *core.OpenAIResponse) error {
	data, err := json.Marshal(chunk)
	if err != nil {
		return fmt.Errorf("failed to marshal chunk: %w", err)
	}

	_, err = fmt.Fprintf(sw.writer, "data: %s\n\n", data)
	return err
}

// WriteDone writes the done signal
func (sw *StreamWriter) WriteDone() error {
	_, err := fmt.Fprintf(sw.writer, "data: [DONE]\n\n")
	return err
}

// WriteError writes an error in SSE format
func (sw *StreamWriter) WriteError(err error) error {
	errorData := map[string]interface{}{
		"error": map[string]interface{}{
			"message": err.Error(),
			"type":    "error",
		},
	}

	data, jsonErr := json.Marshal(errorData)
	if jsonErr != nil {
		return fmt.Errorf("failed to marshal error: %w", jsonErr)
	}

	_, writeErr := fmt.Fprintf(sw.writer, "data: %s\n\n", data)
	return writeErr
}

// StreamConverter converts between different streaming formats
type StreamConverter struct{}

// NewStreamConverter creates a new stream converter
func NewStreamConverter() *StreamConverter {
	return &StreamConverter{}
}

// ConvertGeminiStreamToOpenAI converts Gemini streaming format to OpenAI format
func (sc *StreamConverter) ConvertGeminiStreamToOpenAI(geminiChunk []byte, model string) (*core.OpenAIResponse, error) {
	// This is a simplified implementation
	// In practice, you'd need to handle Gemini's specific streaming format
	var geminiResponse struct {
		Candidates []struct {
			Content struct {
				Parts []struct {
					Text string `json:"text"`
				} `json:"parts"`
			} `json:"content"`
			FinishReason string `json:"finishReason,omitempty"`
		} `json:"candidates"`
	}

	if err := json.Unmarshal(geminiChunk, &geminiResponse); err != nil {
		return nil, fmt.Errorf("failed to parse Gemini stream chunk: %w", err)
	}

	response := &core.OpenAIResponse{
		ID:      fmt.Sprintf("chatcmpl-%d", 12345), // Generate proper ID
		Object:  "chat.completion.chunk",
		Created: 1234567890, // Use proper timestamp
		Model:   model,
		Choices: make([]core.Choice, len(geminiResponse.Candidates)),
	}

	for i, candidate := range geminiResponse.Candidates {
		var content string
		if len(candidate.Content.Parts) > 0 {
			content = candidate.Content.Parts[0].Text
		}

		deltaContent, _ := json.Marshal(content)
		var finishReason *string
		if candidate.FinishReason != "" {
			reason := convertGeminiFinishReason(candidate.FinishReason)
			finishReason = &reason
		}

		response.Choices[i] = core.Choice{
			Index: i,
			Delta: &core.Message{
				Role:    "assistant",
				Content: deltaContent,
			},
			FinishReason: finishReason,
		}
	}

	return response, nil
}

// ConvertClaudeStreamToOpenAI converts Claude streaming format to OpenAI format
func (sc *StreamConverter) ConvertClaudeStreamToOpenAI(claudeChunk []byte, model string) (*core.OpenAIResponse, error) {
	// This is a simplified implementation
	// In practice, you'd need to handle Claude's specific streaming format
	var claudeResponse struct {
		Type  string `json:"type"`
		Delta struct {
			Type string `json:"type"`
			Text string `json:"text"`
		} `json:"delta,omitempty"`
	}

	if err := json.Unmarshal(claudeChunk, &claudeResponse); err != nil {
		return nil, fmt.Errorf("failed to parse Claude stream chunk: %w", err)
	}

	response := &core.OpenAIResponse{
		ID:      fmt.Sprintf("chatcmpl-%d", 12345), // Generate proper ID
		Object:  "chat.completion.chunk",
		Created: 1234567890, // Use proper timestamp
		Model:   model,
		Choices: []core.Choice{},
	}

	if claudeResponse.Type == "content_block_delta" && claudeResponse.Delta.Type == "text_delta" {
		deltaContent, _ := json.Marshal(claudeResponse.Delta.Text)
		response.Choices = append(response.Choices, core.Choice{
			Index: 0,
			Delta: &core.Message{
				Role:    "assistant",
				Content: deltaContent,
			},
		})
	}

	return response, nil
}

// convertGeminiFinishReason converts Gemini finish reason to OpenAI format
func convertGeminiFinishReason(reason string) string {
	switch reason {
	case "STOP":
		return "stop"
	case "MAX_TOKENS":
		return "length"
	case "SAFETY":
		return "content_filter"
	default:
		return "stop"
	}
}
