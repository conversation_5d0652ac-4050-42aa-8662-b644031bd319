# LLM Adapters - Modular OpenAI-Compatible Adapter System

A comprehensive, modular adapter system that provides OpenAI-compatible interfaces for 12+ major LLM providers. This system extracts the essential adapter functionality from the new-api codebase and makes it reusable as a standalone module.

## Features

- **OpenAI-Compatible Interface**: All providers expose the same OpenAI-compatible API
- **Comprehensive Provider Support**: Built-in support for 12+ providers including OpenAI, Google Gemini, Anthropic Claude, Perplexity, Mistral, Deepseek, AWS Bedrock, Azure OpenAI, and more
- **Modular Design**: Each provider is self-contained and easy to extend
- **Request/Response Conversion**: Automatic conversion between OpenAI format and provider-specific formats
- **Error Handling**: Consistent error handling across all providers
- **Streaming Support**: Framework for handling streaming responses (implementation varies by provider)
- **Validation**: Built-in request validation and sanitization
- **Enterprise Ready**: Support for AWS Bedrock, Azure OpenAI, and other enterprise platforms
- **Local Deployment**: Support for Ollama and other local model serving solutions
- **Extensible**: Easy to add new providers following the same patterns

## Architecture

```
adapters/
├── core/                    # Core interfaces and types
│   ├── interfaces.go        # Adapter interface definitions
│   ├── types.go            # Common data structures
│   ├── errors.go           # Error handling
│   └── http_client.go      # HTTP client utilities
├── providers/              # Provider implementations
│   ├── openai/            # OpenAI adapter
│   ├── gemini/            # Google Gemini adapter
│   ├── claude/            # Anthropic Claude adapter
│   └── registry.go        # Provider registry and management
├── utils/                 # Utility functions
│   ├── validation.go      # Request validation
│   └── stream.go         # Streaming utilities
└── examples/              # Usage examples
    └── basic_usage.go     # Basic usage examples
```

## Quick Start

### 1. Basic Usage

```go
package main

import (
    "context"
    "encoding/json"
    "fmt"
    "log"

    "path/to/adapters/core"
    "path/to/adapters/providers"
)

func main() {
    // Create an adapter factory
    factory := providers.NewAdapterFactory()
    
    // Create an OpenAI adapter
    config := &core.AdapterConfig{
        APIKey:  "your-openai-api-key",
        BaseURL: "https://api.openai.com/v1",
    }
    
    adapter, err := factory.CreateAdapter("openai", config)
    if err != nil {
        log.Fatal(err)
    }
    
    // Create a request
    request := &core.OpenAIRequest{
        Model: "gpt-3.5-turbo",
        Messages: []core.Message{
            {
                Role:    "user",
                Content: json.RawMessage(`"Hello, how are you?"`),
            },
        },
        MaxTokens:   &[]int{100}[0],
        Temperature: &[]float64{0.7}[0],
    }
    
    // Convert and process the request
    ctx := context.Background()
    convertedRequest, err := adapter.ConvertRequest(ctx, request)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("Converted request: %+v\n", convertedRequest)
}
```

### 2. Using the Adapter Manager

```go
// Create adapter manager with automatic routing
manager := providers.NewAdapterManager()

// Configure multiple providers
configs := map[string]*core.AdapterConfig{
    "openai": {APIKey: "your-openai-key"},
    "gemini": {APIKey: "your-gemini-key"},
    "claude": {APIKey: "your-claude-key"},
}

factory := providers.NewAdapterFactory()
for provider, config := range configs {
    adapter, _ := factory.CreateAdapter(provider, config)
    manager.RegisterAdapter(provider, adapter)
}

// Automatic routing based on model name
request := &core.OpenAIRequest{
    Model: "gpt-4",  // Will automatically route to OpenAI
    Messages: []core.Message{...},
}

adapter, err := manager.RouteRequest(request)
if err != nil {
    log.Fatal(err)
}

// Process with the appropriate adapter
convertedRequest, err := adapter.ConvertRequest(ctx, request)
```

## Supported Providers

### OpenAI
- **Models**: GPT-4o, GPT-4, GPT-3.5-turbo, text-embedding models, Whisper, DALL-E, TTS
- **Features**: Direct pass-through (canonical format)
- **Authentication**: Bearer token

### Google Gemini
- **Models**: Gemini 1.5 Pro, Gemini 1.5 Flash, Gemini 1.0 Pro, text-embedding-004
- **Features**: Message conversion, safety settings, function calling
- **Authentication**: API key header

### Anthropic Claude
- **Models**: Claude 3.5 Sonnet, Claude 3 Opus, Claude 3 Haiku, Claude 2.1
- **Features**: System message handling, tool use, content blocks
- **Authentication**: API key header

### Perplexity AI
- **Models**: Llama 3.1 Sonar models (online/chat), Mistral, CodeLlama
- **Features**: Online search capabilities, citation support
- **Authentication**: Bearer token

### Mistral AI
- **Models**: Mistral Large, Mistral Small, Mixtral, Codestral, Pixtral
- **Features**: Function calling, embeddings, code generation, vision
- **Authentication**: Bearer token

### Deepseek
- **Models**: Deepseek Chat, Deepseek Coder, Deepseek R1 (reasoning)
- **Features**: Code generation, reasoning models, distilled models
- **Authentication**: Bearer token

### Moonshot AI
- **Models**: Moonshot v1 (8k, 32k, 128k context), Auto model
- **Features**: Long context support, function calling
- **Authentication**: Bearer token

### Ollama
- **Models**: Llama, Mistral, Gemma, Qwen, Phi, CodeLlama, Vision models
- **Features**: Local deployment, flexible model support, embeddings
- **Authentication**: Optional (for local deployments)

### AWS Bedrock
- **Models**: Claude, Titan, Llama, Mistral, Cohere models via AWS
- **Features**: Enterprise security, multiple model families
- **Authentication**: AWS Signature V4

### Azure OpenAI
- **Models**: GPT-4, GPT-3.5, embeddings, Whisper, DALL-E via Azure
- **Features**: Enterprise features, content filtering, data sources
- **Authentication**: API key header

### Alibaba Cloud (Ali)
- **Models**: Qwen series, text embeddings
- **Features**: Chinese language optimization, embeddings
- **Authentication**: Bearer token

## Request/Response Conversion

The adapter system automatically converts between OpenAI's format and provider-specific formats:

### OpenAI → Gemini
- Messages → Contents with Parts
- System messages → SystemInstruction
- Tools → FunctionDeclarations
- Parameters → GenerationConfig

### OpenAI → Claude
- Messages → Messages array
- System messages → System field
- Tools → Tools array
- Images → Base64 content blocks

### Provider → OpenAI
- All responses are converted back to OpenAI's standard format
- Usage information is normalized
- Finish reasons are mapped consistently

## Error Handling

The system provides consistent error handling across all providers:

```go
// All errors implement the AdapterError interface
if err != nil {
    if adapterErr, ok := err.(*core.AdapterError); ok {
        fmt.Printf("Provider: %s\n", adapterErr.Provider)
        fmt.Printf("Type: %s\n", adapterErr.Type)
        fmt.Printf("Message: %s\n", adapterErr.Message)
        fmt.Printf("Retryable: %v\n", adapterErr.Retryable)
    }
}
```

## Validation

Built-in request validation ensures data integrity:

```go
validator := utils.NewValidator()
if err := validator.ValidateRequest(request); err != nil {
    // Handle validation error
}
```

## Adding New Providers

To add a new provider, implement the `core.Adapter` interface:

```go
type MyAdapter struct {
    config *core.AdapterConfig
}

func (a *MyAdapter) GetProviderName() string {
    return "myprovider"
}

func (a *MyAdapter) ConvertRequest(ctx context.Context, request *core.OpenAIRequest) (interface{}, error) {
    // Convert OpenAI request to provider format
}

func (a *MyAdapter) ConvertResponse(ctx context.Context, response *http.Response, isStream bool) (*core.OpenAIResponse, error) {
    // Convert provider response to OpenAI format
}

// Implement other required methods...
```

## Configuration

Each adapter can be configured with:

```go
config := &core.AdapterConfig{
    APIKey:      "your-api-key",
    BaseURL:     "https://api.provider.com/v1",
    HTTPClient:  customHTTPClient,
    ExtraParams: map[string]interface{}{
        "custom_param": "value",
    },
}
```

## Dependencies

This module has minimal dependencies:
- Standard Go library
- No external dependencies for core functionality
- HTTP client for API requests
- JSON marshaling/unmarshaling

## License

This adapter system is extracted from the new-api project and maintains compatibility with its OpenAI-compatible interface design.

## Contributing

1. Follow the existing adapter patterns
2. Implement all required interface methods
3. Add comprehensive error handling
4. Include conversion functions for request/response
5. Add validation for provider-specific requirements
6. Update the registry to include your adapter

## Examples

See the `examples/` directory for comprehensive usage examples including:
- Basic adapter usage
- Adapter manager with automatic routing
- Custom configurations
- Error handling patterns
- Streaming response handling (framework)
